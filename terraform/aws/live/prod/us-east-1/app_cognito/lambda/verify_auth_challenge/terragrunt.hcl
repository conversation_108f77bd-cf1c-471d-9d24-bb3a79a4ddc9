include {
  path = find_in_parent_folders("root.hcl")
}

terraform {
  source = "../../../../../../modules/lambda"
}

inputs = {
  function_name = "mobile_verify_auth_challenge"
  description   = "Lambda function that verifies authentication challenge responses for Mobile App Cognito"
  runtime       = "python3.13"
  handler       = "main.lambda_handler"
  timeout       = 30
  memory_size   = 128
  source_path   = "./src"
  environment   = "prod"

  # Enable Cognito trigger permissions
  enable_cognito_trigger = true

  tags = {
    product = "app"
    team    = "foundation"
  }
}
