import json


def lambda_handler(event, context):
    """
    Create Auth Challenge Lambda Trigger
    
    This function creates the authentication challenge.
    Currently returns a hardcoded 'TRUSTED' response for testing purposes.
    """
    print(f"Create Auth Challenge event: {json.dumps(event, default=str)}")

    username = event.get('userName', '')
    
    try:
        event['response'] = {
            'privateChallengeParameters': {
                'answer': 'TRUSTED'
            }
        }
        return event
    except Exception as e:
        print(f"{username} Error in Create Auth Challenge: {str(e)}")
        event['response'] = {
            'publicChallengeParameters': {
                'error': 'Internal error occurred'
            },
            'privateChallengeParameters': {
                'answer': 'INVALID'
            }
        }
        return event
