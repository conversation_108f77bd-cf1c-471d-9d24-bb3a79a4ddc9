remote_state {
  backend = "s3"
  config = {
    bucket         = "magie-iac-state-prod"
    key            = "${path_relative_to_include()}/terraform.tfstate"
    region         = "us-east-1"
    encrypt        = true
    use_lockfile   = true
  }
}

generate "provider" {
  path      = "provider.tf"
  if_exists = "overwrite_terragrunt"
  contents  = <<EOF
    provider "aws" {
      region = "us-east-1"

      default_tags {
        tags = {
          env = "prod"
          managed_by = "terraform"
        }
      }
    }
  EOF
}
