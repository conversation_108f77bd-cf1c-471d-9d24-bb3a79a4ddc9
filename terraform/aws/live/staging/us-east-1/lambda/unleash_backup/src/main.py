import json
import boto3
import urllib3
from datetime import datetime
from functools import partial
from s3 import upload as s3_uploader
from secrets_manager import get_all
from unleash import fetch as unleash_fetch

s3_client = boto3.client("s3", region_name="us-east-1")
secrets_manager_client = boto3.client("secretsmanager", region_name="us-east-1")

ENDPOINTS = {
    "projects": "/api/admin/projects",
    "environments": "/api/admin/environments",
    "strategies": "/api/admin/strategies",
    "segments": "/api/admin/segments",
    "tags": "/api/admin/tags",
    "tag-types": "/api/admin/tag-types"
}

SECRETS = get_all(secrets_manager_client, "staging/unleash_backup_lambda/secrets")

http = urllib3.PoolManager()

upload_to_s3 = partial(s3_uploader, s3_client, SECRETS['S3_BUCKET'], SECRETS['S3_BASE_PATH'])
fetch_from_unleash = partial(unleash_fetch, http, SECRETS['UNLEASH_URL'], SECRETS['UNLEASH_TOKEN'])


def sanitize_description(desc):
    if not desc:
        return ""
    return desc.replace("'", "")

def fetch_strategies(project_id, timestamp):
    try:
        environments = fetch_from_unleash("/api/admin/environments").get("environments", [])
        environment_names = sorted([env["name"] for env in environments], reverse=True)
        print(f"Found environments: {environment_names}")
    except Exception as e:
        print(f"❌ Error fetching environments list: {e}")
        return {
            "statusCode": 500,
            "body": json.dumps({
                "timestamp": timestamp
            })
        }

    try:
        features = fetch_from_unleash(f"/api/admin/projects/{project_id}/features").get("features", [])
        print(f"Found a total of {len(features)} features for project {project_id}")
        for env in environment_names:
            for feature in features:
                feature_name = feature.get("name")
                url = f"/api/admin/projects/{project_id}/features/{feature_name}/environments/{env}/strategies"
                try:
                    response = fetch_from_unleash(url)
                    if response:
                        upload_to_s3(response, f"{timestamp}/feature-strategies/{project_id}", f"{feature_name}--{env}")
                        upload_to_s3(response, f"latest/feature-strategies/{project_id}", f"{feature_name}--{env}")
                    else:
                        print(f"⚠️ No strategies for feature '{feature_name}' in env '{env}'")
                except Exception as e:
                    print(f"❌ Error fetching strategies for feature '{feature_name}' in env '{env}': {e}")
    except Exception as e:
        print(f"❌ Error fetching features for strategies for project '{project_id}': {e}")

def lambda_handler(event, context):
    timestamp = datetime.now().strftime("%Y-%m-%dT%H-%M-%SZ")
    version_log = {}

    # Step 1: Fetch top-level data (excluding features)
    for name, path in ENDPOINTS.items():
        try:
            data = fetch_from_unleash(path)
            upload_to_s3(data, timestamp, name)
            version_id = upload_to_s3(data, "latest", name)
            version_log[name] = version_id
        except Exception as e:
            print(f"❌ Error with {name}: {e}")
            version_log[name] = f"error: {str(e)}"

    # Step 2: Fetch features per project (using project_id as filename)
    try:
        projects_data = fetch_from_unleash("/api/admin/projects").get("projects", [])
    except Exception as e:
        print(f"❌ Error fetching projects list: {e}")
        version_log["features"] = f"error: {str(e)}"
        return {
            "statusCode": 500,
            "body": json.dumps({
                "timestamp": timestamp,
                "version_ids": version_log
            })
        }

    for project in projects_data:
        project_id = project.get("id")

        try:
            features = fetch_from_unleash(f"/api/admin/projects/{project_id}/features")
            upload_to_s3(features, f"{timestamp}/features", project_id)
            version_id = upload_to_s3(features, "latest/features", project_id)
            version_log[f"features/{project_id}"] = version_id
            fetch_strategies(project_id, timestamp)
        except Exception as e:
            print(f"❌ Error fetching features for project '{project_id}': {e}")
            version_log[f"features/{project_id}"] = f"error: {str(e)}"

    return {
        "statusCode": 200,
        "body": json.dumps({
            "timestamp": timestamp,
            "version_ids": version_log
        })
    }
