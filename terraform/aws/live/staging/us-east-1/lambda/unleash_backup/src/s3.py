import json


def upload(s3_client, s3_bucket, s3_base_path, data, folder, filename):
    key = f"{s3_base_path}/{folder}/{filename}.json"

    try:
        body = json.dumps(data, indent=2)

        response = s3_client.put_object(
            Bucket=s3_bucket,
            Key=key,
            Body=body,
            ContentType="application/json"
        )
        return response.get("VersionId")
    except (TypeError, ValueError) as e:
        raise ValueError(f"Failed to serialize data to JSON: {e}") from e
    except Exception as e:
        raise RuntimeError(f"Failed to upload to S3 key '{key}': {e}") from e
