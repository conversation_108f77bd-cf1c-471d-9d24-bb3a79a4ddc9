import json


def fetch(http, unleash_url, unleash_token, path):
    headers = {
        "Authorization": unleash_token,
        "Content-Type": "application/json"
    }
    try:
        response = http.request("GET", f"{unleash_url}{path}", headers=headers, timeout=10.0)
        match response.status:
            case 200:
                return json.loads(response.data.decode())
            case 404:
                return {}
            case _:
                raise Exception(f"Status {response.status}: {response.data.decode()}")
    except Exception as e:
        raise Exception(f"Request to {unleash_url}{path} failed: {e}") from e

