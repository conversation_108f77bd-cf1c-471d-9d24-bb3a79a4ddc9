include {
  path = find_in_parent_folders("root.hcl")
}

terraform {
  source = "../../../../../modules/lambda"
}

inputs = {
  function_name = "unleash_backup"
  description   = "Lambda function that backup the Unleash"
  runtime       = "python3.13"
  handler       = "main.lambda_handler"
  timeout       = 60
  memory_size   = 128
  source_path   = "./src"
  environment   = "staging"
  vpc_id        = "vpc-0c81cfcee3629b43a"

  enable_eventbridge_schedule = true
  schedule_expression         = "cron(0 * * * ? *)"
  enable_secrets_manager      = true

  additional_policy_statements = {
    s3list = {
      effect    = "Allow"
      actions   = ["s3:ListBucket"]
      resources = ["arn:aws:s3:::magie-unleash"]
    }
    s3access = {
      effect = "Allow"
      actions = [
        "s3:GetObject",
        "s3:PutObject"
      ]
      resources = [
        "arn:aws:s3:::magie-unleash/backup/*",
        "arn:aws:s3:::magie-unleash/exported/*"
      ]
    }
  }

  tags = {
    product = "internal_tool"
    team    = "foundation"
  }
}
