import urllib3
import datetime
import gzip
import io
import boto3
from secrets_manager import get_all
from jwcrypto import jwk, jwt
import time
import json

secrets_manager_client = boto3.client("secretsmanager", region_name="us-east-1")
SECRETS = get_all(secrets_manager_client, "staging/apple_download_metrics_lambda/secrets")
DD_METRIC_URL = "https://api.datadoghq.com/api/v1/series"

http = urllib3.PoolManager()

# === HELPERS ===
def send_to_datadog(metric_name, value, tags):
    print(f"[Datadog] Sending metric: {metric_name} = {value} with tags: {tags}")
    payload = {
        "series": [
            {
                "metric": metric_name,
                "points": [[int(time.time()), value]],
                "type": "gauge",
                "tags": tags
            }
        ]
    }
    headers = {
        "Content-Type": "application/json",
        "DD-API-KEY": SECRETS["DD_API_KEY"]
    }

    encoded_payload = json.dumps(payload).encode('utf-8')
    response = http.request(
        "POST",
        DD_METRIC_URL,
        body=encoded_payload,
        headers=headers
    )

    print(f"[Datadog] Response: {response.status} - {response.data.decode()}")
    if response.status >= 400:
        raise Exception(f"Datadog API error: {response.status} - {response.data.decode()}")

# === APP STORE CONNECT ===
def create_apple_token():
    print("[Apple] Generating JWT token with jwcrypto...")
    now = int(time.time())

    key = jwk.JWK.from_pem(SECRETS["APP_STORE_PRIVATE_KEY"].encode())

    payload = {
        "iss": SECRETS["APP_STORE_ISSUER_ID"],
        "iat": now,
        "exp": now + 20 * 60,
        "aud": "appstoreconnect-v1"
    }

    token = jwt.JWT(
        header={
            "alg": "ES256",
            "kid": SECRETS["APP_STORE_KEY_ID"],
            "typ": "JWT"
        },
        claims=payload
    )
    token.make_signed_token(key)
    print("[Apple] JWT token created.")
    return token.serialize()

def fetch_apple_installs():
    print("[Apple] Fetching install data...")
    today = datetime.date.today()
    dates_to_try = [today, today - datetime.timedelta(days=1)]

    type_mapping = {
        "1": "new",
        "7": "redownload",
        "F": "update"
    }

    counts = {k: {} for k in type_mapping}

    for date in dates_to_try:
        date_str = date.strftime("%Y-%m-%d")
        print(f"[Apple] Requesting sales report for {date_str}")

        token = create_apple_token()
        headers = {
            "Authorization": f"Bearer {token}",
            "Accept": "application/a-gzip"
        }

        url = (
            f"https://api.appstoreconnect.apple.com/v1/salesReports"
            f"?filter[reportDate]={date_str}"
            f"&filter[reportType]=SALES"
            f"&filter[reportSubType]=SUMMARY"
            f"&filter[frequency]=DAILY"
            f"&filter[vendorNumber]={SECRETS['APP_STORE_VENDOR_ID']}"
        )

        response = http.request("GET", url, headers=headers)

        print(f"[Apple] Response status: {response.status}")

        if response.status == 404:
            print(f"[Apple] Report for {date_str} not ready.")
            continue
        elif response.status != 200:
            print(f"[Apple] Error content: {response.data.decode()}")
            raise Exception(f"Apple API error: {response.status} - {response.data.decode()}")

        print("[Apple] Decompressing response...")
        decompressed = gzip.GzipFile(fileobj=io.BytesIO(response.data)).read().decode("utf-8")
        lines = decompressed.splitlines()

        print("[Apple] Parsing TSV content...")
        header = lines[0].split('\t')
        if "Units" not in header:
            raise ValueError("'Units' is not in list")

        version_index = header.index("Version")
        type_index = header.index("Product Type Identifier")
        units_index = header.index("Units")

        for row in lines[1:]:
            fields = row.split('\t')
            version = fields[version_index]
            product_type = fields[type_index]
            units = int(fields[units_index])

            if product_type in type_mapping:
                counts[product_type][version] = counts[product_type].get(version, 0) + units

        break  # first available successful report

    for product_type, versions in counts.items():
        for version, value in versions.items():
            tags = ["platform:ios", f"version:{version}", f"download_type:{type_mapping[product_type]}"]
            send_to_datadog("app.downloads.apple", value, tags)
            print(f"[Apple] Counted {value} for product type {product_type} (version: {version})")

    print("[Apple] Install counts sent to Datadog:")
    print(counts)

def lambda_handler(event=None, context=None):
    try:
        fetch_apple_installs()
        print("[Lambda] Script executed successfully.")
    except Exception as e:
        print(f"[Lambda] Apple error: {e}")

if __name__ == "__main__":
    lambda_handler()
