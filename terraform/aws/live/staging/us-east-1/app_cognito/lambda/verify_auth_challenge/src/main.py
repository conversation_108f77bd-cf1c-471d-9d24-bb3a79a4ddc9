import json


def lambda_handler(event, context):
    """
    Verify Auth Challenge Lambda Trigger
    
    This function verifies the user's response to the authentication challenge.
    """
    print(f"Verify Auth Challenge event: {json.dumps(event, default=str)}")

    username = event.get('userName', '')
    
    try:
        request = event.get('request', {})
        response = event.get('response', {})
        
        private_challenge_parameters = request.get('privateChallengeParameters', {})
        challenge_answer = request.get('challengeAnswer', '')
        
        expected_answer = private_challenge_parameters.get('answer', '')
        
        if challenge_answer and expected_answer and challenge_answer.strip() == expected_answer.strip():
            response['answerCorrect'] = True
            print(f"{username} Challenge answer is correct")
        else:
            response['answerCorrect'] = False
            print(f"{username} Challenge answer is incorrect")
        
        event['response'] = response
        
        return event
        
    except Exception as e:
        print(f"{username} Error in Verify Auth Challenge: {str(e)}")
        event['response'] = {
            'answerCorrect': False
        }
        return event
