import json


def lambda_handler(event, context):
    """
    Define Auth Challenge Lambda Trigger

    This function determines what challenge should be presented to the user
    during the authentication flow.
    """
    print(f"Define Auth Challenge event: {json.dumps(event, default=str)}")

    username = event.get('userName', '')

    try:
        request = event.get('request', {})
        session = request.get('session', [])
        last_challenge = session[-1] if session else None

        if not last_challenge:
            print(f"{username} No previous challenge, starting with custom challenge")
            event['response'] = {
                'challengeName': 'CUSTOM_CHALLENGE',
                'issueTokens': False,
                'failAuthentication': False
            }
            return event

        match last_challenge['challengeName']:
            case 'SRP_A':
                if last_challenge['challengeResult']:
                    print(f"{username} SRP_A challenge successful, starting password verification")
                    event['response'] = {
                        'challengeName': 'PASSWORD_VERIFIER',
                        'issueTokens': False,
                        'failAuthentication': False
                    }
                    return event
                else:
                    print(f"{username} SRP_A challenge failed, denying authentication")
                    event['response'] = {
                        'challengeName': None,
                        'issueTokens': False,
                        'failAuthentication': True
                    }
                    return event

            case 'PASSWORD_VERIFIER':
                if last_challenge['challengeResult']:
                    print(f"{username} Password verification successful, starting custom challenge")
                    event['response'] = {
                        'challengeName': 'CUSTOM_CHALLENGE',
                        'issueTokens': False,
                        'failAuthentication': False
                    }
                    return event
                else:
                    print(f"{username} Password verification failed, denying authentication")
                    event['response'] = {
                        'challengeName': None,
                        'issueTokens': False,
                        'failAuthentication': True
                    }
                    return event

            case 'CUSTOM_CHALLENGE':
                if last_challenge['challengeResult']:
                    print(f"{username} Custom challenge successful, issuing tokens")
                    event['response'] = {
                        'challengeName': None,
                        'issueTokens': True,
                        'failAuthentication': False
                    }
                    return event
                else:
                    print(f"{username} Custom challenge failed, denying authentication")
                    event['response'] = {
                        'challengeName': None,
                        'issueTokens': False,
                        'failAuthentication': True
                    }
                    return event

            case _:
                print(f"{username} Unknown challenge name: {last_challenge['challengeName']}, denying authentication")
                event['response'] = {
                    'challengeName': None,
                    'issueTokens': False,
                    'failAuthentication': True
                }
                return event

    except Exception as e:
        print(f"{username} Error in Define Auth Challenge: {str(e)}")
        event['response'] = {
            'challengeName': None,
            'issueTokens': False,
            'failAuthentication': True
        }
        return event
