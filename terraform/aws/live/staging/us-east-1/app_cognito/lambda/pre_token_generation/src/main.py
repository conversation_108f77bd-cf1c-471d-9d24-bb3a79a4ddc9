import json


def lambda_handler(event, context):
    """
    Cognito Pre Token Generation Lambda function for logging purposes.
    
    This function logs user authentication events and token generation details
    without modifying the token claims.
    """
    
    try:
        print(f"Pre Token Generation event: {json.dumps(event, default=str)}")
        
        return event
        
    except Exception as e:
        print(f"Error in pre-token generation function", extra={
            'error': str(e),
            'username': event.get('userName', 'Unknown'),
            'requestId': context.aws_request_id if context else 'Unknown'
        })
        
        # Return the original event even if logging fails
        return event
