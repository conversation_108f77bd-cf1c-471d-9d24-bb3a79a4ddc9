# Parent Terragrunt configuration for Cognito App
# This file provides a convenient way to manage all resources needed
#
# Usage from this directory:
#   terragrunt plan -all # Plan all sub-modules (Lambda functions + User Pool)
#   terragrunt apply -all # Deploy all sub-modules in correct order
#   terragrunt destroy -all # Destroy all sub-modules
#   terragrunt output -all # Show outputs from all sub-modules
#
# Individual module management:
#   cd to_folder/with_terragrunt_file && terragrunt apply

include {
  path = find_in_parent_folders("root.hcl")
}

# Skip this parent configuration when running individual commands
# This file is only used for run all operations
skip = true
