include {
  path = find_in_parent_folders("root.hcl")
}

terraform {
  source = "../../../../modules/cdn"
}

inputs = {
  environment   = "staging"
  bucket_prefix = "magie-app-assets"

  # Cache behavior optimized for development
  cache_behavior = {
    min_ttl     = 0
    default_ttl = 3600  # 1 hour for staging (shorter for faster updates)
    max_ttl     = 86400 # 1 day
  }

  # Image-specific cache behaviors
  image_cache_behaviors = [
    {
      path_pattern = "*.jpg"
      min_ttl      = 0
      default_ttl  = 3600 # 1 hour
      max_ttl      = 86400 # 1 day
    },
    {
      path_pattern = "*.jpeg"
      min_ttl      = 0
      default_ttl  = 3600 # 1 hour
      max_ttl      = 86400 # 1 day
    },
    {
      path_pattern = "*.png"
      min_ttl      = 0
      default_ttl  = 3600 # 1 hour
      max_ttl      = 86400 # 1 day
    },
    {
      path_pattern = "*.webp"
      min_ttl      = 0
      default_ttl  = 3600 # 1 hour
      max_ttl      = 86400 # 1 day
    },
    {
      path_pattern = "*.svg"
      min_ttl      = 0
      default_ttl  = 1800 # 30 minutes
      max_ttl      = 86400 # 1 day
    }
  ]

  # Use cost-effective price class for staging
  price_class = "PriceClass_100"

  # Route53 DNS record configuration
  create_dns_record = true
  custom_domain     = "staging.magie.services"
  subdomain         = "app-assets"

  tags = {
    product = "app"
    team    = "foundation"
  }
}
