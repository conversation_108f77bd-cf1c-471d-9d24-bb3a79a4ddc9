terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "5.81.0"
    }
  }
}

locals {
  default_vpc_policies = var.vpc_id != null ? {
    managenetwork = {
      effect = "Allow"
      actions = [
        "ec2:CreateNetworkInterface",
        "ec2:DescribeNetworkInterfaces",
        "ec2:DeleteNetworkInterface"
      ]
      resources = ["*"]
    }
  } : {}

  default_secrets_policies = var.enable_secrets_manager ? {
    getsecret = {
      effect = "Allow"
      actions = ["secretsmanager:GetSecretValue"]
      resources = [aws_secretsmanager_secret.lambda_secrets[0].arn]
    }
  } : {}

  default_log_policies = {
    loggroupcreate = {
      effect = "Allow"
      actions = ["logs:CreateLogGroup"]
      resources = ["arn:aws:logs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:*"]
    }
    logstreamwrite = {
      effect = "Allow"
      actions = [
        "logs:CreateLogStream",
        "logs:PutLogEvents"
      ]
      resources = [
        "arn:aws:logs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:log-group:/aws/lambda/${var.function_name}:*"
      ]
    }
  }

  all_policy_statements = merge(
    local.default_vpc_policies,
    local.default_secrets_policies,
    local.default_log_policies,
    var.additional_policy_statements
  )
}

data "aws_caller_identity" "current" {}

data "aws_region" "current" {}

data "aws_subnets" "private" {
  count = var.vpc_id != null ? 1 : 0

  filter {
    name = "vpc-id"
    values = [var.vpc_id]
  }

  filter {
    name = "tag:Type"
    values = ["private"]
  }
}

module "lambda_function" {
  source  = "terraform-aws-modules/lambda/aws"
  version = "7.21.0"

  function_name = var.function_name
  description   = var.description
  handler       = var.handler
  runtime       = var.runtime
  timeout       = var.timeout
  memory_size   = var.memory_size
  architectures = var.architectures

  # VPC configuration
  vpc_subnet_ids         = var.vpc_id != null ? data.aws_subnets.private[0].ids : null
  vpc_security_group_ids = var.vpc_id != null ? [aws_security_group.lambda_sg[0].id] : null

  trigger_on_package_timestamp = false

  # Policy statements
  attach_policy_statements = length(local.all_policy_statements) > 0
  policy_statements        = local.all_policy_statements

  source_path                       = var.source_path
  cloudwatch_logs_retention_in_days = 30
  environment_variables             = var.environment_variables
}

resource "aws_cloudwatch_event_rule" "schedule" {
  count = var.enable_eventbridge_schedule ? 1 : 0

  name                = "${var.function_name}_schedule"
  description         = "Trigger ${var.function_name}"
  state               = "ENABLED"
  schedule_expression = var.schedule_expression
}

resource "aws_cloudwatch_event_target" "schedule_target" {
  count = var.enable_eventbridge_schedule ? 1 : 0

  rule      = aws_cloudwatch_event_rule.schedule[0].name
  arn       = module.lambda_function.lambda_function_arn
  target_id = "${var.function_name}_target"
}

resource "aws_lambda_permission" "allow_eventbridge" {
  count = var.enable_eventbridge_schedule ? 1 : 0

  statement_id  = "AllowExecutionFromEventBridge"
  action        = "lambda:InvokeFunction"
  function_name = module.lambda_function.lambda_function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.schedule[0].arn
}

resource "aws_secretsmanager_secret" "lambda_secrets" {
  count = var.enable_secrets_manager ? 1 : 0

  name        = "${var.environment}/${var.function_name}_lambda/secrets"
  description = "Secrets for ${var.function_name} lambda"

  lifecycle {
    ignore_changes = all
  }
}

resource "aws_security_group" "lambda_sg" {
  count = var.vpc_id != null ? 1 : 0

  name_prefix = "lambda-${var.function_name}-"
  description = "Security group for Lambda ${var.function_name}"
  vpc_id      = var.vpc_id

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
  }
}

resource "aws_lambda_permission" "cognito_invoke" {
  count = var.enable_cognito_trigger ? 1 : 0

  statement_id  = "AllowExecutionFromCognito"
  action        = "lambda:InvokeFunction"
  function_name = module.lambda_function.lambda_function_name
  principal     = "cognito-idp.amazonaws.com"
  source_account = data.aws_caller_identity.current.account_id
}
