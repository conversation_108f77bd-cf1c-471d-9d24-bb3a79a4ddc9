variable "environment" {
  description = "Environment name"
  type        = string
}

variable "function_name" {
  description = "Name of the Lambda function"
  type        = string
}

variable "description" {
  description = "Description of the Lambda function"
  type        = string
  default     = "Lambda function created with Terraform"
}

variable "handler" {
  description = "Lambda function handler"
  type        = string
  default     = "index.lambda_handler"
}

variable "runtime" {
  description = "Lambda function runtime"
  type        = string
  default     = "python3.13"
}

variable "timeout" {
  description = "Lambda function timeout"
  type        = number
  default     = 30
}

variable "memory_size" {
  description = "Lambda function memory size"
  type        = number
  default     = 128
}

variable "source_path" {
  description = "Path to the Lambda function source code"
  type        = string
}

variable "environment_variables" {
  description = "Environment variables for the Lambda function"
  type = map(string)
  default = {}
}

variable "tags" {
  description = "Tags to apply to resources"
  type = map(string)
  default = {}
}

variable "vpc_id" {
  description = "VPC id (optional - if not provided, Lambda will not be deployed in VPC)"
  type        = string
  default     = null
}

variable "additional_policy_statements" {
  description = "Additional IAM policy statements for the Lambda function"
  type = map(object({
    effect    = string
    actions   = list(string)
    resources = list(string)
  }))
  default = {}
}

variable "enable_eventbridge_schedule" {
  description = "Enable EventBridge scheduled execution"
  type        = bool
  default     = false
}

variable "schedule_expression" {
  description = "EventBridge schedule expression (only used if enable_eventbridge_schedule is true)"
  type        = string
  default     = "cron(0 * * * ? *)"
}

variable "enable_secrets_manager" {
  description = "Enable Secrets Manager secret creation"
  type        = bool
  default     = false
}

variable "enable_cognito_trigger" {
  description = "Enable Cognito trigger permissions"
  type        = bool
  default     = false
}

variable "architectures" {
  description = "Lambda function architectures"
  type        = list(string)
  default     = ["arm64"]
}
