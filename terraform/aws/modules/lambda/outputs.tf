output "lambda_function_arn" {
  description = "The ARN of the Lambda function"
  value       = module.lambda_function.lambda_function_arn
}

output "lambda_function_name" {
  description = "The name of the Lambda function"
  value       = module.lambda_function.lambda_function_name
}

output "lambda_function_invoke_arn" {
  description = "The invoke ARN of the Lambda function"
  value       = module.lambda_function.lambda_function_invoke_arn
}

output "lambda_function_qualified_arn" {
  description = "The qualified ARN of the Lambda function"
  value       = module.lambda_function.lambda_function_qualified_arn
}

output "lambda_role_arn" {
  description = "The ARN of the IAM role created for the Lambda function"
  value       = module.lambda_function.lambda_role_arn
}

output "security_group_id" {
  description = "The ID of the security group (only for VPC functions)"
  value       = var.vpc_id != null ? aws_security_group.lambda_sg[0].id : null
}

output "secrets_manager_secret_arn" {
  description = "The ARN of the Secrets Manager secret (only if enabled)"
  value       = var.enable_secrets_manager ? aws_secretsmanager_secret.lambda_secrets[0].arn : null
}
