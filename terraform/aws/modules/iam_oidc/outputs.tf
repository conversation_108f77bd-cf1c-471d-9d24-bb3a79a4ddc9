output "oidc_provider_arn" {
  description = "The ARN of the OIDC provider"
  value       = aws_iam_openid_connect_provider.oidc_provider.arn
}

output "oidc_provider_url" {
  description = "The URL of the OIDC provider"
  value       = aws_iam_openid_connect_provider.oidc_provider.url
}

output "iam_role_arn" {
  description = "The ARN of the IAM role"
  value       = aws_iam_role.oidc_role.arn
}

output "iam_role_name" {
  description = "The name of the IAM role"
  value       = aws_iam_role.oidc_role.name
}

output "iam_policy_arn" {
  description = "The ARN of the IAM policy"
  value       = aws_iam_policy.oidc_policy.arn
}

output "iam_policy_name" {
  description = "The name of the IAM policy"
  value       = aws_iam_policy.oidc_policy.name
}
