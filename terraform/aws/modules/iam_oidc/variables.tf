variable "environment" {
  description = "Environment name"
  type        = string
}

variable "url" {
  type        = string
  description = "Token URL"
}

variable "client_id_list" {
  type        = list(string)
  description = "Client ID list"
}

variable "role_name" {
  type        = string
  description = "Role name"
}

variable "tags" {
  type        = map(string)
  description = "Tags to apply to resources"
  default     = {}
}