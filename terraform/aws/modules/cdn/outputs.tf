output "s3_bucket_name" {
  description = "Name of the S3 bucket"
  value       = aws_s3_bucket.this.bucket
}

output "s3_bucket_arn" {
  description = "ARN of the S3 bucket"
  value       = aws_s3_bucket.this.arn
}

output "s3_bucket_domain_name" {
  description = "Domain name of the S3 bucket"
  value       = aws_s3_bucket.this.bucket_domain_name
}

output "s3_bucket_regional_domain_name" {
  description = "Regional domain name of the S3 bucket"
  value       = aws_s3_bucket.this.bucket_regional_domain_name
}

output "cloudfront_distribution_id" {
  description = "ID of the CloudFront distribution"
  value       = aws_cloudfront_distribution.this.id
}

output "cloudfront_distribution_arn" {
  description = "ARN of the CloudFront distribution"
  value       = aws_cloudfront_distribution.this.arn
}

output "cloudfront_domain_name" {
  description = "Domain name of the CloudFront distribution"
  value       = aws_cloudfront_distribution.this.domain_name
}

output "cloudfront_hosted_zone_id" {
  description = "CloudFront distribution hosted zone ID"
  value       = aws_cloudfront_distribution.this.hosted_zone_id
}

output "cloudfront_distribution_status" {
  description = "Current status of the CloudFront distribution"
  value       = aws_cloudfront_distribution.this.status
}

output "origin_access_control_id" {
  description = "ID of the CloudFront Origin Access Control"
  value       = aws_cloudfront_origin_access_control.this.id
}

output "response_headers_policy_id" {
  description = "ID of the CloudFront Response Headers Policy"
  value       = aws_cloudfront_response_headers_policy.this.id
}

output "cdn_url" {
  description = "Full CDN URL for accessing content"
  value       = "https://${aws_cloudfront_distribution.this.domain_name}"
}

output "custom_domain_url" {
  description = "Custom domain URL (if configured)"
  value       = var.custom_domain != null ? "https://${var.custom_domain}" : null
}

output "route53_record_name" {
  description = "Name of the Route53 DNS record (if created)"
  value       = var.create_dns_record ? aws_route53_record.cdn[0].name : null
}

output "route53_record_fqdn" {
  description = "Fully qualified domain name of the Route53 DNS record (if created)"
  value       = var.create_dns_record ? aws_route53_record.cdn[0].fqdn : null
}

output "route53_hosted_zone_id" {
  description = "ID of the Route53 hosted zone (if DNS record created)"
  value       = var.create_dns_record ? data.aws_route53_zone.main[0].zone_id : null
}

output "dns_url" {
  description = "Full DNS URL for accessing content via custom DNS record (if created)"
  value       = var.create_dns_record ? "https://${aws_route53_record.cdn[0].fqdn}" : null
}
