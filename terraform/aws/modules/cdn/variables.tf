variable "environment" {
  description = "Environment name (e.g., staging, prod)"
  type        = string
  
  validation {
    condition     = can(regex("^[a-z0-9-]+$", var.environment))
    error_message = "Environment must contain only lowercase letters, numbers, and hyphens."
  }
}

variable "bucket_prefix" {
  description = "Prefix for the S3 bucket name"
  type        = string
  default     = "magie-flutter-images"
  
  validation {
    condition     = can(regex("^[a-z0-9-]+$", var.bucket_prefix))
    error_message = "Bucket prefix must contain only lowercase letters, numbers, and hyphens."
  }
}

variable "default_root_object" {
  description = "Default root object for CloudFront distribution"
  type        = string
  default     = null
}

variable "enable_logging" {
  description = "Enable CloudFront access logging"
  type        = bool
  default     = false
}

variable "logging_bucket" {
  description = "S3 bucket for CloudFront access logs (if not specified, will use default naming)"
  type        = string
  default     = null
}

variable "logging_prefix" {
  description = "Prefix for CloudFront access log files"
  type        = string
  default     = "cloudfront-logs/"
}

variable "cache_behavior" {
  description = "Default cache behavior settings"
  type = object({
    min_ttl     = number
    default_ttl = number
    max_ttl     = number
  })
  default = {
    min_ttl     = 0
    default_ttl = 86400    # 1 day
    max_ttl     = 31536000 # 1 year
  }
}

variable "image_cache_behaviors" {
  description = "Additional cache behaviors for specific image types"
  type = list(object({
    path_pattern = string
    min_ttl      = number
    default_ttl  = number
    max_ttl      = number
  }))
  default = [
    {
      path_pattern = "*.jpg"
      min_ttl      = 0
      default_ttl  = 604800   # 7 days
      max_ttl      = 31536000 # 1 year
    },
    {
      path_pattern = "*.jpeg"
      min_ttl      = 0
      default_ttl  = 604800   # 7 days
      max_ttl      = 31536000 # 1 year
    },
    {
      path_pattern = "*.png"
      min_ttl      = 0
      default_ttl  = 604800   # 7 days
      max_ttl      = 31536000 # 1 year
    },
    {
      path_pattern = "*.webp"
      min_ttl      = 0
      default_ttl  = 604800   # 7 days
      max_ttl      = 31536000 # 1 year
    },
    {
      path_pattern = "*.svg"
      min_ttl      = 0
      default_ttl  = 2592000  # 30 days
      max_ttl      = 31536000 # 1 year
    }
  ]
}

variable "price_class" {
  description = "CloudFront distribution price class"
  type        = string
  default     = "PriceClass_100"
  
  validation {
    condition = contains([
      "PriceClass_All",
      "PriceClass_200",
      "PriceClass_100"
    ], var.price_class)
    error_message = "Price class must be one of: PriceClass_All, PriceClass_200, PriceClass_100."
  }
}

variable "tags" {
  description = "Additional tags to apply to resources"
  type        = map(string)
  default     = {}
}

variable "create_dns_record" {
  description = "Whether to create a Route53 DNS record pointing to the CloudFront distribution"
  type        = bool
  default     = false
}

variable "custom_domain" {
  description = "Custom domain name for the CloudFront distribution"
  type        = string
  default     = null

  validation {
    condition = var.create_dns_record == false || (var.create_dns_record == true && var.custom_domain != null)
    error_message = "custom_domain must be provided when create_dns_record is true."
  }
}

variable "subdomain" {
  description = "Subdomain for the DNS record (e.g., 'cdn' for cdn.staging.magie.services)"
  type        = string
  default     = null

  validation {
    condition = var.create_dns_record == false || (var.create_dns_record == true && var.subdomain != null)
    error_message = "subdomain must be provided when create_dns_record is true."
  }
}
