terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "5.81.0"
    }
  }
}

resource "aws_cognito_user_pool" "main" {
  name = var.pool_name

  # Password policy
  password_policy {
    minimum_length                   = var.password_policy.minimum_length
    require_lowercase                = var.password_policy.require_lowercase
    require_numbers                  = var.password_policy.require_numbers
    require_symbols                  = var.password_policy.require_symbols
    require_uppercase                = var.password_policy.require_uppercase
    temporary_password_validity_days = var.password_policy.temporary_password_validity_days
  }

  # Custom attributes
  dynamic "schema" {
    for_each = var.custom_attributes
    content {
      name                = schema.value.name
      attribute_data_type = schema.value.attribute_data_type
      mutable             = schema.value.mutable
      required            = schema.value.required
      string_attribute_constraints {
        min_length = schema.value.string_attribute_constraints.min_length
        max_length = schema.value.string_attribute_constraints.max_length
      }
    }
  }

  # Auto-verified attributes
  auto_verified_attributes = var.auto_verified_attributes

  # Email configuration
  email_configuration {
    email_sending_account  = var.email_configuration.email_sending_account
    from_email_address     = var.email_configuration.from_email_address
    reply_to_email_address = var.email_configuration.reply_to_email_address
    source_arn             = var.email_configuration.source_arn
  }

  # MFA configuration
  mfa_configuration = var.enable_mfa ? var.mfa_configuration : "OFF"

  dynamic "software_token_mfa_configuration" {
    for_each = var.enable_mfa ? [1] : []
    content {
      enabled = true
    }
  }

  # Account recovery
  account_recovery_setting {
    recovery_mechanism {
      name     = "admin_only" // verified_email
      priority = 1
    }
  }

  # User pool add-ons (for plan configuration)
  user_pool_add_ons {
    advanced_security_mode = var.plan == "plus" ? "ENFORCED" : "OFF"
  }

  # Admin create user config
  admin_create_user_config {
    allow_admin_create_user_only = var.allow_admin_create_user_only
  }

  dynamic "lambda_config" {
    for_each = (
      var.lambda_triggers.define_auth_challenge != null ||
      var.lambda_triggers.create_auth_challenge != null ||
      var.lambda_triggers.verify_auth_challenge != null
    ) ? [1] : []
    content {
      define_auth_challenge          = var.lambda_triggers.define_auth_challenge
      create_auth_challenge          = var.lambda_triggers.create_auth_challenge
      verify_auth_challenge_response = var.lambda_triggers.verify_auth_challenge
      pre_token_generation           = var.lambda_triggers.pre_token_generation
    }
  }

  tags = var.tags
}

resource "aws_cognito_user_pool_client" "clients" {
  for_each = { for client in var.app_clients : client.client_name => client }

  name                   = each.value.client_name
  user_pool_id           = aws_cognito_user_pool.main.id
  generate_secret        = each.value.generate_secret
  access_token_validity  = each.value.access_token_validity
  id_token_validity      = each.value.id_token_validity
  refresh_token_validity = each.value.refresh_token_validity
  auth_session_validity  = each.value.auth_session_validity

  token_validity_units {
    access_token  = "minutes"
    id_token      = "minutes"
    refresh_token = "days"
  }

  explicit_auth_flows = each.value.explicit_auth_flows

  prevent_user_existence_errors = "ENABLED"

  read_attributes  = ["email", "email_verified", "custom:accountUuid"]
  write_attributes = ["email", "custom:accountUuid"]
}
