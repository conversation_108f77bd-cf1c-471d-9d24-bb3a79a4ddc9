output "user_pool_id" {
  description = "The ID of the Cognito User Pool"
  value       = aws_cognito_user_pool.main.id
}

output "user_pool_arn" {
  description = "The ARN of the Cognito User Pool"
  value       = aws_cognito_user_pool.main.arn
}

output "user_pool_name" {
  description = "The name of the Cognito User Pool"
  value       = aws_cognito_user_pool.main.name
}

output "user_pool_endpoint" {
  description = "The endpoint name of the Cognito User Pool"
  value       = aws_cognito_user_pool.main.endpoint
}

output "user_pool_creation_date" {
  description = "The date the user pool was created"
  value       = aws_cognito_user_pool.main.creation_date
}

output "user_pool_last_modified_date" {
  description = "The date the user pool was last modified"
  value       = aws_cognito_user_pool.main.last_modified_date
}

output "app_client_ids" {
  description = "Map of app client names to their IDs"
  value       = { for k, v in aws_cognito_user_pool_client.clients : k => v.id }
}

output "app_client_secrets" {
  description = "Map of app client names to their secrets (only for server-side clients)"
  value       = { for k, v in aws_cognito_user_pool_client.clients : k => v.client_secret if v.client_secret != null }
  sensitive   = true
}

output "app_clients" {
  description = "Map of app client details"
  value = {
    for k, v in aws_cognito_user_pool_client.clients : k => {
      id           = v.id
      name         = v.name
      user_pool_id = v.user_pool_id
    }
  }
}
