name: Terraform Commands
run-name: ${{ github.actor }} is ${{ github.event.inputs.command }}ing changes to ${{ github.event.inputs.environment }}

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to use'
        required: true
        default: staging
        type: choice
        options:
          - staging
          - prod
      command:
        description: 'Terraform command'
        required: true
        default: plan
        type: choice
        options:
          - plan
          - apply

permissions:
  contents: read
  id-token: write

jobs:
  terraform:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    env:
      AWS_ROLE: ${{ github.event.inputs.environment == 'prod' && 'arn:aws:iam::836929571495:role/github_oidc_role' || 'arn:aws:iam::992382535149:role/github_oidc_role' }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.AWS_ROLE }}
          aws-region: us-east-1
      
      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.13

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: 1.12.1

      - name: Install Terragrunt
        run: |
          TG_VERSION=0.80.1
          curl -L -o terragrunt https://github.com/gruntwork-io/terragrunt/releases/download/v${TG_VERSION}/terragrunt_linux_amd64
          chmod +x terragrunt
          sudo mv terragrunt /usr/local/bin/terragrunt

      - name: Terragrunt ${{ github.event.inputs.command }}
        run: |
          terragrunt ${{ github.event.inputs.command }} --all --working-dir terraform/aws/live/${{ github.event.inputs.environment }} --non-interactive
