import boto3

def list_latest_task_definitions():
    """
    Lista a última versão de cada definição de tarefa no ECS na região 'us-east-1'.
    """
    ecs_client = boto3.client('ecs', region_name='us-east-1')
    paginator = ecs_client.get_paginator('list_task_definitions')

    # Coletar todas as definições de tarefas com paginação
    task_definitions = []
    for page in paginator.paginate(sort='DESC'):
        task_definitions.extend(page['taskDefinitionArns'])

    # Dicionário para manter a última versão de cada tarefa
    latest_task_definitions = {}

    for task in task_definitions:
        # Extrair o nome da tarefa (sem a versão)
        task_name = task.split('/')[-1].split(':')[0]

        # Se a tarefa ainda não está no dicionário, adicionar
        if task_name not in latest_task_definitions:
            latest_task_definitions[task_name] = task

    # Exibir as definições de tarefas mais recentes
    print("Últimas versões de definições de tarefas disponíveis:")
    for i, task in enumerate(latest_task_definitions.values()):
        print(f"{i + 1}: {task}")

    return list(latest_task_definitions.values())

def choose_task_definitions(task_definitions):
    """
    Permite ao usuário selecionar múltiplas definições de tarefa para criar serviços.
    """
    print("\nDigite os números das tarefas que deseja usar para criar os serviços, separados por vírgula.")
    print("Para selecionar todas as tarefas, digite 'all'.")
    user_input = input("Sua escolha: ")

    if user_input.lower() == 'all':
        chosen_tasks = task_definitions
    else:
        indices = [int(i.strip()) - 1 for i in user_input.split(',') if i.strip().isdigit()]
        chosen_tasks = [task_definitions[i] for i in indices if 0 <= i < len(task_definitions)]

    chosen_tasks_info = []
    ecs_client = boto3.client('ecs', region_name='us-east-1')

    for chosen_task in chosen_tasks:
        # Obter a última versão da definição de tarefa
        response = ecs_client.describe_task_definition(taskDefinition=chosen_task)
        task_definition = response['taskDefinition']

        # Pegar o nome do container e a porta do container na definição de tarefa
        container_name = task_definition['containerDefinitions'][0]['name']
        container_port = task_definition['containerDefinitions'][0]['portMappings'][0]['containerPort']

        print(f"Definição de tarefa '{chosen_task}' selecionada. Container: '{container_name}', Porta: '{container_port}'")
        chosen_tasks_info.append((chosen_task, container_name, container_port))

    return chosen_tasks_info

def list_clusters():
    """
    Lista todos os clusters ECS disponíveis.
    """
    ecs_client = boto3.client('ecs', region_name='us-east-1')
    response = ecs_client.list_clusters()
    clusters = response['clusterArns']
    print("\nClusters disponíveis:")
    for i, cluster in enumerate(clusters):
        print(f"{i + 1}: {cluster}")
    return clusters

def choose_cluster(clusters):
    """
    Pede ao usuário para escolher um cluster.
    """
    choice = int(input("Escolha o número do cluster que deseja usar: ")) - 1
    return clusters[choice]

def get_capacity_provider_name(cluster_arn):
    """
    Obtém o nome do Capacity Provider associado ao cluster.
    """
    ecs_client = boto3.client('ecs', region_name='us-east-1')

    # Remover o parâmetro 'include' da chamada
    response = ecs_client.describe_clusters(clusters=[cluster_arn])
    cluster = response['clusters'][0]

    # Tentar obter os capacity providers
    capacity_providers = cluster.get('capacityProviders', [])
    if capacity_providers:
        print(f"Capacity Providers disponíveis no cluster: {capacity_providers}")
        return capacity_providers[0]
    else:
        print("Nenhum Capacity Provider associado ao cluster.")

        # Solicitar ao usuário que insira o nome do Capacity Provider
        capacity_provider_name = input("Por favor, insira o nome do Capacity Provider que deseja usar: ")
        return capacity_provider_name

def list_load_balancers():
    """
    Lista todos os Application Load Balancers (ALBs) disponíveis na região 'us-east-1'.
    """
    elb_client = boto3.client('elbv2', region_name='us-east-1')
    response = elb_client.describe_load_balancers()
    load_balancers = [lb for lb in response['LoadBalancers'] if lb['Type'] == 'application']
    print("\nApplication Load Balancers (ALB) disponíveis:")
    for i, lb in enumerate(load_balancers):
        print(f"{i + 1}: {lb['LoadBalancerArn']} - {lb['DNSName']}")
    return load_balancers

def choose_load_balancer(load_balancers):
    """
    Pede ao usuário para escolher um ALB.
    """
    choice = int(input("Escolha o número do ALB que deseja usar: ")) - 1
    return load_balancers[choice]

def get_vpc_id(load_balancer):
    """
    Obtém o ID da VPC associada ao ALB.
    """
    return load_balancer['VpcId']

def get_listener_arn(load_balancer_arn):
    """
    Obtém o ARN do listener existente na porta 443.
    """
    elb_client = boto3.client('elbv2', region_name='us-east-1')
    response = elb_client.describe_listeners(LoadBalancerArn=load_balancer_arn)
    listeners = [listener for listener in response['Listeners'] if listener['Port'] == 443]
    if listeners:
        print(f"Listener encontrado na porta 443: {listeners[0]['ListenerArn']}")
        return listeners[0]['ListenerArn']
    else:
        print("Nenhum listener encontrado na porta 443.")
        return None

def create_target_group(service_name, vpc_id, container_port, health_check_path):
    """
    Cria um grupo de destino para o serviço, ou atualiza o existente.
    """
    elb_client = boto3.client('elbv2', region_name='us-east-1')
    target_group_name = f"ecs-{service_name}"[:32]  # Limite de 32 caracteres
    try:
        # Verificar se o grupo de destino já existe
        response = elb_client.describe_target_groups(Names=[target_group_name])
        target_group = response['TargetGroups'][0]
        target_group_arn = target_group['TargetGroupArn']
        print(f"Grupo de destino '{target_group_arn}' já existe. Usando o existente.")

        # Verificar e atualizar o Health Check Path se necessário
        if target_group['HealthCheckPath'] != health_check_path:
            elb_client.modify_target_group(
                TargetGroupArn=target_group_arn,
                HealthCheckPath=health_check_path
            )
            print(f"Health Check Path do grupo de destino '{target_group_name}' atualizado para '{health_check_path}'.")
        return target_group_arn
    except elb_client.exceptions.TargetGroupNotFoundException:
        # Criar grupo de destino
        try:
            response = elb_client.create_target_group(
                Name=target_group_name,
                Protocol='HTTP',
                Port=80, 
                #Port=container_port,  # Usar a porta do container                
                VpcId=vpc_id,
                HealthCheckProtocol='HTTP',
                HealthCheckPath=health_check_path,  # Caminho de health check personalizado
                HealthCheckIntervalSeconds=30,
                HealthCheckTimeoutSeconds=5,
                HealthyThresholdCount=2,
                UnhealthyThresholdCount=2,
                TargetType='instance'
            )
            target_group_arn = response['TargetGroups'][0]['TargetGroupArn']
            print(f"Grupo de destino '{target_group_arn}' criado com sucesso.")
            return target_group_arn
        except Exception as e:
            print(f"Erro ao criar grupo de destino: {e}")
            return None
    except Exception as e:
        print(f"Erro ao verificar existência do grupo de destino: {e}")
        return None

def create_rule_for_service(listener_arn, full_domain_name, target_group_arn):
    """
    Cria uma regra no listener do ALB para encaminhar tráfego para o grupo de destino, ou usa a existente.
    """
    elb_client = boto3.client('elbv2', region_name='us-east-1')
    try:
        # Verificar se a regra já existe
        existing_rules = elb_client.describe_rules(ListenerArn=listener_arn)['Rules']
        for rule in existing_rules:
            conditions = rule['Conditions']
            host_header_match = any(
                cond['Field'] == 'host-header' and full_domain_name in cond['HostHeaderConfig']['Values']
                for cond in conditions if 'HostHeaderConfig' in cond
            )
            path_pattern_match = any(
                cond['Field'] == 'path-pattern' and '/*' in cond['PathPatternConfig']['Values']
                for cond in conditions if 'PathPatternConfig' in cond
            )
            if host_header_match and path_pattern_match:
                print(f"Regra já existe para o domínio '{full_domain_name}'. Usando a regra existente.")
                return rule['RuleArn']

        # Definir a prioridade da regra
        priorities = [int(rule['Priority']) for rule in existing_rules if rule['Priority'].isdigit()]
        next_priority = max(priorities) + 1 if priorities else 1

        # Remover o ponto final do domínio, se existir
        if full_domain_name.endswith('.'):
            full_domain_name = full_domain_name[:-1]

        # Criar a regra
        response = elb_client.create_rule(
            ListenerArn=listener_arn,
            Conditions=[
                {
                    'Field': 'host-header',
                    'HostHeaderConfig': {
                        'Values': [full_domain_name]
                    }
                },
                {
                    'Field': 'path-pattern',
                    'PathPatternConfig': {
                        'Values': ['/*']
                    }
                }
            ],
            Actions=[
                {
                    'Type': 'forward',
                    'ForwardConfig': {
                        'TargetGroups': [
                            {
                                'TargetGroupArn': target_group_arn,
                                'Weight': 1
                            }
                        ]
                    }
                }
            ],
            Priority=next_priority
        )
        print(f"Regra criada com sucesso para o serviço '{full_domain_name}' com prioridade {next_priority}.")
        return response['Rules'][0]['RuleArn']
    except Exception as e:
        print(f"Erro ao criar a regra para o serviço: {e}")
        return None

def list_hosted_zones():
    """
    Lista todas as zonas hospedadas no Route 53.
    """
    route53_client = boto3.client('route53', region_name='us-east-1')
    paginator = route53_client.get_paginator('list_hosted_zones')
    hosted_zones = []
    for page in paginator.paginate():
        hosted_zones.extend(page['HostedZones'])
    print("\nZonas hospedadas disponíveis:")
    for i, zone in enumerate(hosted_zones):
        print(f"{i + 1}: {zone['Name']} - {zone['Id']}")
    return hosted_zones

def choose_hosted_zone(hosted_zones):
    """
    Pede ao usuário para escolher uma zona hospedada no Route 53.
    """
    choice = int(input("Escolha o número da zona hospedada que deseja usar (ou digite 0 para não usar nenhuma): ")) - 1
    return hosted_zones[choice] if choice >= 0 else None

def get_elb_hosted_zone_id(load_balancer_arn):
    """
    Obtém o Hosted Zone ID para o ALB selecionado.
    """
    elb_client = boto3.client('elbv2', region_name='us-east-1')
    response = elb_client.describe_load_balancers(LoadBalancerArns=[load_balancer_arn])
    load_balancer = response['LoadBalancers'][0]
    hosted_zone_id = load_balancer['CanonicalHostedZoneId']
    print(f"Hosted Zone ID do ALB: {hosted_zone_id}")
    return hosted_zone_id

def create_route53_record(hosted_zone_id, domain_name, load_balancer_dns, elb_hosted_zone_id):
    """
    Cria ou atualiza um registro no Route 53 apontando para o ALB.
    """
    route53_client = boto3.client('route53', region_name='us-east-1')
    try:
        response = route53_client.change_resource_record_sets(
            HostedZoneId=hosted_zone_id,
            ChangeBatch={
                'Comment': 'Adicionando registro para novo serviço ECS',
                'Changes': [
                    {
                        'Action': 'UPSERT',
                        'ResourceRecordSet': {
                            'Name': domain_name,
                            'Type': 'A',
                            'AliasTarget': {
                                'HostedZoneId': elb_hosted_zone_id,
                                'DNSName': load_balancer_dns,
                                'EvaluateTargetHealth': False
                            }
                        }
                    }
                ]
            }
        )
        print(f"Registro DNS '{domain_name}' criado ou atualizado com sucesso no Route 53.")
    except Exception as e:
        print(f"Erro ao criar ou atualizar o registro DNS no Route 53: {e}")

def create_cloudwatch_log_group(log_group_name):
    """
    Cria um grupo de logs no CloudWatch se não existir.
    """
    logs_client = boto3.client('logs', region_name='us-east-1')
    try:
        logs_client.create_log_group(logGroupName=log_group_name)
        print(f"Grupo de logs '{log_group_name}' criado com sucesso no CloudWatch.")
    except logs_client.exceptions.ResourceAlreadyExistsException:
        print(f"Grupo de logs '{log_group_name}' já existe no CloudWatch. Usando o existente.")
    except Exception as e:
        print(f"Erro ao criar o grupo de logs: {e}")

def create_service(cluster_arn, task_definition, container_name, service_name, desired_count, target_group_arn, container_port, capacity_provider_name):
    """
    Cria o serviço no ECS, ou atualiza se já existir.
    """
    ecs_client = boto3.client('ecs', region_name='us-east-1')
    try:
        # Verificar se o serviço já existe
        existing_services = ecs_client.describe_services(cluster=cluster_arn, services=[service_name])
        service_exists = existing_services['services'] and existing_services['services'][0]['status'] != 'INACTIVE'

        if service_exists:
            print(f"Serviço '{service_name}' já existe. Atualizando o serviço.")
            response = ecs_client.update_service(
                cluster=cluster_arn,
                service=service_name,
                taskDefinition=task_definition,
                desiredCount=desired_count,
                capacityProviderStrategy=[
                    {
                        'capacityProvider': capacity_provider_name,
                        'weight': 1,
                        'base': 0
                    }
                ],
                forceNewDeployment=True
            )
            return response['service']['serviceArn']
        else:
            # Criar o serviço
            response = ecs_client.create_service(
                cluster=cluster_arn,
                serviceName=service_name,
                taskDefinition=task_definition,
                desiredCount=desired_count,
                capacityProviderStrategy=[
                    {
                        'capacityProvider': capacity_provider_name,
                        'weight': 1,
                        'base': 0
                    }
                ],
                deploymentConfiguration={'maximumPercent': 200, 'minimumHealthyPercent': 100},
                loadBalancers=[
                    {
                        'targetGroupArn': target_group_arn,
                        'containerName': container_name,
                        'containerPort': container_port  # Usar a porta do container recuperada
                    }
                ],
                enableECSManagedTags=True,
                enableExecuteCommand=True,
                propagateTags='TASK_DEFINITION'
            )
            print(f"\nServiço '{service_name}' criado com sucesso no cluster '{cluster_arn}'.")
            return response['service']['serviceArn']
    except Exception as e:
        print(f"Erro ao criar ou atualizar o serviço: {e}")
        return None

def delete_resources(resources):
    """
    Exclui todos os recursos criados em caso de erro ou cancelamento.
    """
    ecs_client = boto3.client('ecs', region_name='us-east-1')
    elb_client = boto3.client('elbv2', region_name='us-east-1')
    route53_client = boto3.client('route53', region_name='us-east-1')
    logs_client = boto3.client('logs', region_name='us-east-1')

    if 'serviceArns' in resources:
        for service_arn in resources['serviceArns']:
            try:
                ecs_client.delete_service(cluster=resources['clusterArn'], service=service_arn, force=True)
                print(f"Serviço '{service_arn}' excluído.")
            except Exception as e:
                print(f"Erro ao excluir o serviço '{service_arn}': {e}")

    if 'ruleArns' in resources:
        for rule_arn in resources['ruleArns']:
            try:
                elb_client.delete_rule(RuleArn=rule_arn)
                print(f"Regra '{rule_arn}' excluída.")
            except Exception as e:
                print(f"Erro ao excluir a regra '{rule_arn}': {e}")

    if 'targetGroupArns' in resources:
        for tg_arn in resources['targetGroupArns']:
            try:
                elb_client.delete_target_group(TargetGroupArn=tg_arn)
                print(f"Grupo de destino '{tg_arn}' excluído.")
            except Exception as e:
                print(f"Erro ao excluir o grupo de destino '{tg_arn}': {e}")

    if 'dnsRecords' in resources:
        for dns_record in resources['dnsRecords']:
            try:
                route53_client.change_resource_record_sets(
                    HostedZoneId=resources['hostedZoneId'],
                    ChangeBatch={
                        'Comment': 'Removendo registro do serviço ECS',
                        'Changes': [
                            {
                                'Action': 'DELETE',
                                'ResourceRecordSet': {
                                    'Name': dns_record,
                                    'Type': 'A',
                                    'AliasTarget': {
                                        'HostedZoneId': resources['elbHostedZoneId'],
                                        'DNSName': resources['loadBalancerDNS'],
                                        'EvaluateTargetHealth': False
                                    }
                                }
                            }
                        ]
                    }
                )
                print(f"Registro DNS '{dns_record}' excluído do Route 53.")
            except Exception as e:
                print(f"Erro ao excluir o registro DNS '{dns_record}': {e}")

    if 'logGroupNames' in resources:
        for log_group_name in resources['logGroupNames']:
            try:
                logs_client.delete_log_group(logGroupName=log_group_name)
                print(f"Grupo de logs '{log_group_name}' excluído do CloudWatch.")
            except Exception as e:
                print(f"Erro ao excluir o grupo de logs '{log_group_name}': {e}")

def choose_environment():
    """
    Permite ao usuário selecionar um ambiente ou inserir um nome personalizado.
    """
    environments = ['prod', 'dev', 'staging', 'loadtest']
    print("\nAmbientes disponíveis:")
    for i, env in enumerate(environments, 1):
        print(f"{i}: {env}")
    print(f"{len(environments) + 1}: Inserir nome personalizado")

    choice = int(input("Escolha o número do ambiente que deseja usar: ")) - 1

    if 0 <= choice < len(environments):
        return environments[choice]
    elif choice == len(environments):
        custom_env = input("Insira o nome personalizado do ambiente: ").strip()
        return custom_env
    else:
        print("Opção inválida. Usando 'dev' como padrão.")
        return 'dev'

def main():
    resources_created = {
        'serviceArns': [],
        'ruleArns': [],
        'targetGroupArns': [],
        'dnsRecords': [],
        'logGroupNames': []
    }

    try:
        # Listar definições de tarefas ECS e escolher uma ou mais
        task_definitions = list_latest_task_definitions()
        chosen_tasks_info = choose_task_definitions(task_definitions)

        # Listar clusters ECS e escolher um
        clusters = list_clusters()
        chosen_cluster = choose_cluster(clusters)

        # Obter o nome do Capacity Provider associado ao cluster
        capacity_provider_name = get_capacity_provider_name(chosen_cluster)
        if not capacity_provider_name:
            print("Não foi possível obter o nome do Capacity Provider. Operação abortada.")
            return

        # Listar ALBs e escolher um
        load_balancers = list_load_balancers()
        chosen_lb = choose_load_balancer(load_balancers)

        # Obter ID da VPC e ARN do Listener na porta 443
        vpc_id = get_vpc_id(chosen_lb)
        listener_arn = get_listener_arn(chosen_lb['LoadBalancerArn'])
        if not listener_arn:
            print("Listener na porta 443 não encontrado. Operação abortada.")
            return

        # Listar zonas do Route 53 e escolher uma (ou nenhuma)
        hosted_zones = list_hosted_zones()
        chosen_zone = choose_hosted_zone(hosted_zones)
        if not chosen_zone:
            print("Zona hospedada não selecionada. Operação abortada.")
            return

        # Obter o Hosted Zone ID do ALB
        elb_hosted_zone_id = get_elb_hosted_zone_id(chosen_lb['LoadBalancerArn'])
        resources_created['elbHostedZoneId'] = elb_hosted_zone_id
        resources_created['loadBalancerDNS'] = chosen_lb['DNSName']
        resources_created['hostedZoneId'] = chosen_zone['Id']

        # Pedir ao usuário para inserir o caminho do health check
        health_check_path = input("Insira o caminho do health check (Padrão: /health): ") or "/health"
        if not health_check_path.startswith('/'):
            health_check_path = '/' + health_check_path

        # Definir número desejado de réplicas
        desired_count = int(input(f"Quantas réplicas deseja para cada serviço? (Padrão: 1): ") or "1")

        # Escolher o ambiente
        environment = choose_environment()

        # Preparar implantações para cada definição de tarefa selecionada
        deployments = []
        for chosen_task, container_name, container_port in chosen_tasks_info:
            service_name = chosen_task.split('/')[-1].split(':')[0]  # Nome do serviço
            domain_name = f"{service_name}.{chosen_zone['Name']}".rstrip('.')

            # Usar o ambiente selecionado ao construir o nome do grupo de logs
            default_log_group_name = f"/ecs/{environment}/{service_name}"
            log_group_choice = input(f"Deseja usar o nome padrão do grupo de logs '{default_log_group_name}' para '{service_name}'? (y/n): ").lower()
            if log_group_choice == 'y':
                log_group_name = default_log_group_name
            else:
                log_group_name = input(f"Insira o nome desejado para o grupo de logs de '{service_name}': ")

            deployment = {
                'task_definition': chosen_task,
                'container_name': container_name,
                'container_port': container_port,
                'service_name': service_name,
                'domain_name': domain_name,
                'log_group_name': log_group_name
            }
            deployments.append(deployment)

        # Exibir resumo das implantações
        print("\nResumo das implantações:")
        for deployment in deployments:
            print(f"\nServiço: {deployment['service_name']}")
            print(f"Tarefa: {deployment['task_definition']}")
            print(f"Cluster: {chosen_cluster}")
            print(f"Número de Réplicas: {desired_count}")
            print(f"ALB: {chosen_lb['LoadBalancerArn']}")
            print(f"Listener: {listener_arn} (Porta 443)")
            print(f"Domínio: {deployment['domain_name']}")
            print(f"Health Check Path: {health_check_path}")
            print(f"Grupo de Logs: {deployment['log_group_name']}")

        # Confirmar antes de criar os serviços
        confirm = input("\nConfirma a criação dos serviços listados com essas configurações? (y/n): ")
        if confirm.lower() != 'y':
            print("Operação cancelada.")
            return

        # Criar recursos e serviços
        for deployment in deployments:
            service_name = deployment['service_name']
            task_definition = deployment['task_definition']
            container_name = deployment['container_name']
            container_port = deployment['container_port']
            domain_name = deployment['domain_name']
            log_group_name = deployment['log_group_name']

            # Criar grupo de logs (se não existir)
            create_cloudwatch_log_group(log_group_name)
            resources_created['logGroupNames'].append(log_group_name)

            # Criar grupo de destino (ou obter existente)
            target_group_arn = create_target_group(service_name, vpc_id, container_port, health_check_path)
            if not target_group_arn:
                print(f"Falha ao criar ou obter o grupo de destino para '{service_name}'. Operação abortada.")
                continue  # Pula para a próxima tarefa
            resources_created['targetGroupArns'].append(target_group_arn)

            # Criar regra no ALB (ou usar existente)
            rule_arn = create_rule_for_service(listener_arn, domain_name, target_group_arn)
            if not rule_arn:
                print(f"Falha ao criar a regra para '{service_name}'. Operação abortada.")
                continue
            resources_created['ruleArns'].append(rule_arn)

            # Criar registro no Route 53
            create_route53_record(chosen_zone['Id'], domain_name, chosen_lb['DNSName'], elb_hosted_zone_id)
            resources_created['dnsRecords'].append(domain_name)

            # Criar ou atualizar o serviço
            service_arn = create_service(
                chosen_cluster,
                task_definition,
                container_name,
                service_name,
                desired_count,
                target_group_arn,
                container_port,
                capacity_provider_name  # Passa o nome do Capacity Provider
            )
            if service_arn:
                resources_created['serviceArns'].append(service_arn)
                resources_created['clusterArn'] = chosen_cluster
            else:
                print(f"Falha ao criar ou atualizar o serviço '{service_name}'.")
                continue

    except Exception as e:
        print(f"\nOcorreu um erro durante a implantação: {e}")
        rollback = input("Deseja excluir os recursos criados? (y/n): ").lower()
        if rollback == 'y':
            delete_resources(resources_created)
        else:
            print("Os recursos criados foram mantidos.")

if __name__ == "__main__":
    main()
