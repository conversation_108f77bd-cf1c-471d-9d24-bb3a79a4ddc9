# Script de criação de serviços AWS ECS

Este script Python facilita a criação de múltiplos serviços no AWS Elastic Container Service (ECS), integrando-se automaticamente com o Application Load Balancer (ALB) e o Amazon Route 53. O script é dinâmico e pode ser utilizado em qualquer conta AWS, sem dependências de valores fixos.

## Funcionalidades
- Seleção de múltiplas definições de tarefas ECS:
    - Lista as últimas versões de cada definição de tarefa disponível no ECS.
    - Permite selecionar uma ou mais definições de tarefa para criar serviços correspondentes.
- Configuração unificada:
    - Permite selecionar um cluster ECS, um ALB e uma zona hospedada do Route 53 que serão utilizados para todos os serviços selecionados.
    - Solicita o caminho de health check e o número desejado de réplicas para aplicar a todos os serviços.
- Integração com ALB:
    - Cria grupos de destino (Target Groups) para cada serviço, ou reutiliza se já existirem.
    - Cria regras no ALB para direcionar o tráfego para os serviços, ou reutiliza regras existentes.
- Integração com Route 53:
    - Cria registros DNS para cada serviço na zona hospedada selecionada, apontando para o ALB.
- Gerenciamento de Grupos de Logs:
    - Cria grupos de logs no CloudWatch para cada serviço, ou reutiliza se já existirem.
- Confirmação antes da criação:
    - Exibe um resumo das implantações planejadas e solicita confirmação antes de criar os recursos.
- Tratamento de recursos existentes:
    - Verifica se os recursos necessários já existem e os reutiliza, evitando duplicações.
- Rollback em caso de erro:
    - Em caso de falha durante a implantação, o script oferece a opção de excluir os recursos já criados.

## Requisitos
- Python 3.x
- Boto3 (biblioteca AWS SDK para Python)
- Credenciais AWS configuradas no ambiente (arquivo ~/.aws/credentials ou variáveis de ambiente)
- Permissões AWS adequadas:
    - Permissões para criar, modificar e excluir recursos ECS, ELB, Route 53 e CloudWatch Logs.

## Instalação

1. Clone o repositório ou copie o script para o seu ambiente local.

2. Instale a biblioteca Boto3, se ainda não estiver instalada:

```bash
pip install boto3
```

## Uso
Execute o script com Python 3:

```bash
python3 create-service.py
```

O script solicitará as informações necessárias para criar os serviços:

1. Seleção das definições de tarefa ECS:
    - Digite os números das tarefas que deseja usar, separados por vírgula.
    - Para selecionar todas as tarefas listadas, digite all.
2. Seleção do cluster ECS:
    - Escolha o número do cluster onde os serviços serão implantados.
3. Seleção do ALB:
    - Escolha o número do Application Load Balancer que será usado para todos os serviços.
4. Seleção da zona hospedada no Route 53:
    - Escolha o número da zona hospedada onde os registros DNS serão criados.
5. Configurações adicionais:
    - Insira o caminho do health check (padrão: /health).
    - Defina o número desejado de réplicas para cada serviço (padrão: 1).
6. Configuração dos grupos de logs:
    - Para cada serviço, escolha se deseja usar o nome padrão do grupo de logs ou especificar um nome personalizado.
7. Confirmação:
    - O script exibirá um resumo das implantações planejadas.
    - Confirme se deseja proceder com a criação dos serviços e recursos associados.

Após a confirmação, o script irá:
- Criar grupos de logs no CloudWatch para cada serviço (se não existirem).
- Criar grupos de destino no ALB para cada serviço (ou reutilizar existentes).
- Criar ou atualizar regras no ALB para direcionar o tráfego.
- Criar registros DNS no Route 53 para cada serviço.
- Criar ou atualizar os serviços no ECS.

## Notas
- Região AWS:
    - O script está configurado para operar na região us-east-1. Se desejar usar outra região, modifique o parâmetro region_name nos clientes do Boto3.
- Recursos Existentes:
    - O script verifica se os recursos necessários já existem e os reutiliza quando possível.
    - Se um serviço ECS com o mesmo nome já existir, ele será atualizado com a nova definição de tarefa e número de réplicas especificado.
- Rollback:
    - Em caso de erro durante a implantação, o script oferece a opção de realizar um rollback, excluindo os recursos que foram criados até o ponto do erro.
- Permissões AWS:
    - Certifique-se de que as credenciais AWS utilizadas têm as permissões necessárias para listar, criar, modificar e excluir recursos ECS, ELB, Route 53 e CloudWatch Logs.

## Personalização

- Alterar a região AWS:
    - Modifique o parâmetro region_name nos clientes do Boto3 se desejar operar em outra região.
- Modificar parâmetros padrão:
    - Você pode ajustar os valores padrão para o caminho de health check, número de réplicas e outros parâmetros diretamente no código, se necessário.

## Exemplo de Execução
```bash
python3 create-service.py
```

```bash
Últimas versões de definições de tarefas disponíveis:
1: arn:aws:ecs:us-east-1:**********12:task-definition/serviceA:1
2: arn:aws:ecs:us-east-1:**********12:task-definition/serviceB:1
3: arn:aws:ecs:us-east-1:**********12:task-definition/serviceC:1

Digite os números das tarefas que deseja usar para criar os serviços, separados por vírgula.
Para selecionar todas as tarefas, digite 'all'.
Sua escolha: 1,3

Definição de tarefa 'arn:aws:ecs:us-east-1:**********12:task-definition/serviceA:1' selecionada. Container: 'serviceA-container', Porta: '80'
Definição de tarefa 'arn:aws:ecs:us-east-1:**********12:task-definition/serviceC:1' selecionada. Container: 'serviceC-container', Porta: '8080'

Clusters disponíveis:
1: arn:aws:ecs:us-east-1:**********12:cluster/clusterA
2: arn:aws:ecs:us-east-1:**********12:cluster/clusterB
Escolha o número do cluster que deseja usar: 1

Application Load Balancers (ALB) disponíveis:
1: arn:aws:elasticloadbalancing:us-east-1:**********12:loadbalancer/app/albA/**********abcdef - albA-**********.us-east-1.elb.amazonaws.com
2: arn:aws:elasticloadbalancing:us-east-1:**********12:loadbalancer/app/albB/abcdef********** - albB-abcdef1234.us-east-1.elb.amazonaws.com
Escolha o número do ALB que deseja usar: 1

Zonas hospedadas disponíveis:
1: example.com. - /hostedzone/Z1D633PJN98FT9
2: example.org. - /hostedzone/Z3AADJGX6KTTL2
Escolha o número da zona hospedada que deseja usar (ou digite 0 para não usar nenhuma): 1

Insira o caminho do health check (Padrão: /health):
Quantas réplicas deseja para cada serviço? (Padrão: 1):

Deseja usar o nome padrão do grupo de logs '/ecs/loadtest/serviceA' para 'serviceA'? (y/n): y
Deseja usar o nome padrão do grupo de logs '/ecs/loadtest/serviceC' para 'serviceC'? (y/n): y

Resumo das implantações:

Serviço: serviceA
Tarefa: arn:aws:ecs:us-east-1:**********12:task-definition/serviceA:1
Cluster: arn:aws:ecs:us-east-1:**********12:cluster/clusterA
Número de Réplicas: 1
ALB: arn:aws:elasticloadbalancing:us-east-1:**********12:loadbalancer/app/albA/**********abcdef
Listener: arn:aws:elasticloadbalancing:us-east-1:**********12:listener/app/albA/**********abcdef/abcdef********** (Porta 443)
Domínio: serviceA.example.com
Health Check Path: /health
Grupo de Logs: /ecs/loadtest/serviceA

Serviço: serviceC
Tarefa: arn:aws:ecs:us-east-1:**********12:task-definition/serviceC:1
Cluster: arn:aws:ecs:us-east-1:**********12:cluster/clusterA
Número de Réplicas: 1
ALB: arn:aws:elasticloadbalancing:us-east-1:**********12:loadbalancer/app/albA/**********abcdef
Listener: arn:aws:elasticloadbalancing:us-east-1:**********12:listener/app/albA/**********abcdef/abcdef********** (Porta 443)
Domínio: serviceC.example.com
Health Check Path: /health
Grupo de Logs: /ecs/loadtest/serviceC

Confirma a criação dos serviços listados com essas configurações? (y/n): y

# O script prosseguirá com a criação dos recursos e serviços.
```

## Contribuição
Sinta-se à vontade para contribuir com melhorias para este script. Faça um fork do repositório, realize as alterações desejadas e abra um pull request.