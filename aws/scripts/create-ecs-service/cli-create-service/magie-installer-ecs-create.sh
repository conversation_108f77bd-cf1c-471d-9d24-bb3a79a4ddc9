#!/bin/bash

# Nome do script Python e do executável
PYTHON_SCRIPT="create_service-v2.py"
EXECUTABLE_NAME="magie-create-service"

# Função para verificar o sistema operacional
check_os() {
    OS_TYPE="$(uname -s)"
    case "$OS_TYPE" in
        Darwin)
            echo "Sistema operacional detectado: macOS"
            BIN_DIR="/usr/local/bin"
            ;;
        Linux)
            echo "Sistema operacional detectado: Linux"
            BIN_DIR="/usr/local/bin"
            ;;
        *)
            echo "Sistema operacional não suportado: $OS_TYPE"
            exit 1
            ;;
    esac
}

# Função para instalar as dependências necessárias
install_dependencies() {
    echo "Verificando se o PyInstaller está instalado..."
    if ! python3 -m PyInstaller --version &> /dev/null; then
        echo "PyInstaller não encontrado. Instalando..."
        pip3 install --user pyinstaller
    else
        echo "PyInstaller já está instalado."
    fi
}

# Função para criar o executável
build_executable() {
    echo "Criando o executável usando PyInstaller..."
    python3 -m PyInstaller --onefile --name $EXECUTABLE_NAME $PYTHON_SCRIPT
}

# Função para instalar o executável
install_executable() {
    echo "Instalando o executável..."
    sudo cp dist/$EXECUTABLE_NAME $BIN_DIR/
    sudo chmod +x $BIN_DIR/$EXECUTABLE_NAME
    echo "Instalação concluída. Você pode executar o comando '$EXECUTABLE_NAME' no terminal."
}

# Função para desinstalar o executável
uninstall_executable() {
    echo "Desinstalando o executável..."
    if [ -f "$BIN_DIR/$EXECUTABLE_NAME" ]; then
        sudo rm $BIN_DIR/$EXECUTABLE_NAME
        echo "Desinstalação concluída."
    else
        echo "O executável não está instalado."
    fi
}

# Função para limpar arquivos temporários
clean_up() {
    echo "Limpando arquivos temporários..."
    rm -rf build/ dist/ __pycache__/ $EXECUTABLE_NAME.spec
}

# Início do script
check_os

# Menu principal
echo "Selecione uma opção:"
echo "1) Instalar"
echo "2) Desinstalar"
echo "3) Atualizar"
echo "4) Sair"
read -p "Opção: " choice

case $choice in
    1)
        install_dependencies
        build_executable
        install_executable
        clean_up
        ;;
    2)
        uninstall_executable
        ;;
    3)
        uninstall_executable
        install_dependencies
        build_executable
        install_executable
        clean_up
        ;;
    4)
        echo "Saindo..."
        ;;
    *)
        echo "Opção inválida. Saindo..."
        ;;
esac
