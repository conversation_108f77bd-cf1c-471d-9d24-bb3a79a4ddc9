#!/usr/bin/env python3

import boto3
import sys

def list_latest_task_definitions():
    ecs_client = boto3.client('ecs', region_name='us-east-1')
    paginator = ecs_client.get_paginator('list_task_definitions')
    task_definitions = []
    for page in paginator.paginate(sort='DESC'):
        task_definitions.extend(page['taskDefinitionArns'])
    latest_task_definitions = {}
    for task in task_definitions:
        task_name = task.split('/')[-1].split(':')[0]
        if task_name not in latest_task_definitions:
            latest_task_definitions[task_name] = task
    print("Últimas versões de definições de tarefas disponíveis:")
    for i, task in enumerate(latest_task_definitions.values()):
        print(f"{i + 1}: {task}")
    return list(latest_task_definitions.values())

def choose_task_definitions(task_definitions):
    print("\nDigite os números das tarefas que deseja usar para criar os serviços, separados por vírgula.")
    print("Para selecionar todas as tarefas, digite 'all'.")
    user_input = input("Sua escolha: ")
    if user_input.lower() == 'all':
        chosen_tasks = task_definitions
    else:
        try:
            indices = [int(i.strip()) - 1 for i in user_input.split(',') if i.strip().isdigit()]
            chosen_tasks = [task_definitions[i] for i in indices if 0 <= i < len(task_definitions)]
        except (ValueError, IndexError):
            print("Entrada inválida. Selecionando todas as tarefas por padrão.")
            chosen_tasks = task_definitions
    chosen_tasks_info = []
    ecs_client = boto3.client('ecs', region_name='us-east-1')
    for chosen_task in chosen_tasks:
        response = ecs_client.describe_task_definition(taskDefinition=chosen_task)
        task_definition = response['taskDefinition']
        container_name = task_definition['containerDefinitions'][0]['name']
        container_port = task_definition['containerDefinitions'][0]['portMappings'][0]['containerPort']
        print(f"Definição de tarefa '{chosen_task}' selecionada. Container: '{container_name}', Porta: '{container_port}'")
        chosen_tasks_info.append((chosen_task, container_name, container_port))
    return chosen_tasks_info

def list_clusters():
    ecs_client = boto3.client('ecs', region_name='us-east-1')
    response = ecs_client.list_clusters()
    clusters = response['clusterArns']
    print("\nClusters disponíveis:")
    for i, cluster in enumerate(clusters):
        print(f"{i + 1}: {cluster}")
    return clusters

def choose_cluster(clusters):
    while True:
        try:
            choice = int(input("Escolha o número do cluster que deseja usar: ")) - 1
            if 0 <= choice < len(clusters):
                return clusters[choice]
            else:
                print("Número inválido. Tente novamente.")
        except ValueError:
            print("Entrada inválida. Por favor, insira um número.")

def get_capacity_provider_name(cluster_arn):
    ecs_client = boto3.client('ecs', region_name='us-east-1')
    response = ecs_client.describe_clusters(clusters=[cluster_arn])
    cluster = response['clusters'][0]
    capacity_providers = cluster.get('capacityProviders', [])
    if capacity_providers:
        print("\nCapacity Providers associados ao cluster:")
        for i, cp in enumerate(capacity_providers, 1):
            print(f"{i}: {cp}")
        while True:
            try:
                choice = int(input("Escolha o número do Capacity Provider que deseja usar: ")) - 1
                if 0 <= choice < len(capacity_providers):
                    return capacity_providers[choice]
                else:
                    print("Número inválido. Tente novamente.")
            except ValueError:
                print("Entrada inválida. Por favor, insira um número.")
    else:
        print("\nNenhum Capacity Provider associado ao cluster.")
        response = ecs_client.list_capacity_providers()
        all_capacity_providers = response.get('capacityProviders', [])
        if all_capacity_providers:
            print("\nCapacity Providers disponíveis na região:")
            for i, cp in enumerate(all_capacity_providers, 1):
                print(f"{i}: {cp}")
            while True:
                try:
                    choice = int(input("Escolha o número do Capacity Provider que deseja usar: ")) - 1
                    if 0 <= choice < len(all_capacity_providers):
                        return all_capacity_providers[choice]
                    else:
                        print("Número inválido. Tente novamente.")
                except ValueError:
                    print("Entrada inválida. Por favor, insira um número.")
        else:
            print("Nenhum Capacity Provider disponível na região.")
            return None

def validate_capacity_provider(capacity_provider_name):
    ecs_client = boto3.client('ecs', region_name='us-east-1')
    response = ecs_client.describe_capacity_providers(capacityProviders=[capacity_provider_name], include=['TAGS'])
    capacity_provider = response['capacityProviders'][0]
    if capacity_provider['status'] != 'ACTIVE':
        print(f"Capacity Provider '{capacity_provider_name}' não está ativo.")
        return False
    if 'autoScalingGroupProvider' in capacity_provider:
        print(f"Capacity Provider '{capacity_provider_name}' está configurado corretamente.")
        return True
    else:
        print(f"Capacity Provider '{capacity_provider_name}' não está associado a um Auto Scaling Group.")
        return False

def list_load_balancers():
    elb_client = boto3.client('elbv2', region_name='us-east-1')
    response = elb_client.describe_load_balancers()
    load_balancers = [lb for lb in response['LoadBalancers'] if lb['Type'] == 'application']
    print("\nApplication Load Balancers (ALB) disponíveis:")
    for i, lb in enumerate(load_balancers):
        print(f"{i + 1}: {lb['LoadBalancerArn']} - {lb['DNSName']}")
    return load_balancers

def choose_load_balancer(load_balancers):
    while True:
        try:
            choice = int(input("Escolha o número do ALB que deseja usar: ")) - 1
            if 0 <= choice < len(load_balancers):
                return load_balancers[choice]
            else:
                print("Número inválido. Tente novamente.")
        except ValueError:
            print("Entrada inválida. Por favor, insira um número.")

def get_vpc_id(load_balancer):
    return load_balancer['VpcId']

def get_listener_arn(load_balancer_arn):
    elb_client = boto3.client('elbv2', region_name='us-east-1')
    response = elb_client.describe_listeners(LoadBalancerArn=load_balancer_arn)
    listeners = [listener for listener in response['Listeners'] if listener['Port'] == 443]
    if listeners:
        print(f"Listener encontrado na porta 443: {listeners[0]['ListenerArn']}")
        return listeners[0]['ListenerArn']
    else:
        print("Nenhum listener encontrado na porta 443.")
        return None

def create_target_group(service_name, vpc_id, container_port, health_check_path):
    elb_client = boto3.client('elbv2', region_name='us-east-1')
    target_group_name = f"ecs-{service_name}"[:32]
    try:
        response = elb_client.describe_target_groups(Names=[target_group_name])
        target_group = response['TargetGroups'][0]
        target_group_arn = target_group['TargetGroupArn']
        print(f"Grupo de destino '{target_group_arn}' já existe. Usando o existente.")
        if target_group['HealthCheckPath'] != health_check_path:
            elb_client.modify_target_group(
                TargetGroupArn=target_group_arn,
                HealthCheckPath=health_check_path
            )
            print(f"Health Check Path do grupo de destino '{target_group_name}' atualizado para '{health_check_path}'.")
        return target_group_arn
    except elb_client.exceptions.TargetGroupNotFoundException:
        try:
            response = elb_client.create_target_group(
                Name=target_group_name,
                Protocol='HTTP',
                Port=80,
                VpcId=vpc_id,
                HealthCheckProtocol='HTTP',
                HealthCheckPath=health_check_path,
                HealthCheckIntervalSeconds=30,
                HealthCheckTimeoutSeconds=5,
                HealthyThresholdCount=2,
                UnhealthyThresholdCount=2,
                TargetType='instance'
            )
            target_group_arn = response['TargetGroups'][0]['TargetGroupArn']
            print(f"Grupo de destino '{target_group_arn}' criado com sucesso.")
            return target_group_arn
        except Exception as e:
            print(f"Erro ao criar grupo de destino: {e}")
            return None
    except Exception as e:
        print(f"Erro ao verificar existência do grupo de destino: {e}")
        return None

def create_rule_for_service(listener_arn, full_domain_name, target_group_arn):
    elb_client = boto3.client('elbv2', region_name='us-east-1')
    try:
        existing_rules = elb_client.describe_rules(ListenerArn=listener_arn)['Rules']
        for rule in existing_rules:
            conditions = rule['Conditions']
            host_header_match = any(
                cond['Field'] == 'host-header' and full_domain_name in cond['HostHeaderConfig']['Values']
                for cond in conditions if 'HostHeaderConfig' in cond
            )
            path_pattern_match = any(
                cond['Field'] == 'path-pattern' and '/*' in cond['PathPatternConfig']['Values']
                for cond in conditions if 'PathPatternConfig' in cond
            )
            if host_header_match and path_pattern_match:
                print(f"Regra já existe para o domínio '{full_domain_name}'. Usando a regra existente.")
                return rule['RuleArn']
        priorities = [int(rule['Priority']) for rule in existing_rules if rule['Priority'].isdigit()]
        next_priority = max(priorities) + 1 if priorities else 1
        if full_domain_name.endswith('.'):
            full_domain_name = full_domain_name[:-1]
        response = elb_client.create_rule(
            ListenerArn=listener_arn,
            Conditions=[
                {
                    'Field': 'host-header',
                    'HostHeaderConfig': {
                        'Values': [full_domain_name]
                    }
                },
                {
                    'Field': 'path-pattern',
                    'PathPatternConfig': {
                        'Values': ['/*']
                    }
                }
            ],
            Actions=[
                {
                    'Type': 'forward',
                    'ForwardConfig': {
                        'TargetGroups': [
                            {
                                'TargetGroupArn': target_group_arn,
                                'Weight': 1
                            }
                        ]
                    }
                }
            ],
            Priority=next_priority
        )
        print(f"Regra criada com sucesso para o serviço '{full_domain_name}' com prioridade {next_priority}.")
        return response['Rules'][0]['RuleArn']
    except Exception as e:
        print(f"Erro ao criar a regra para o serviço: {e}")
        return None

def list_hosted_zones():
    route53_client = boto3.client('route53', region_name='us-east-1')
    paginator = route53_client.get_paginator('list_hosted_zones')
    hosted_zones = []
    for page in paginator.paginate():
        hosted_zones.extend(page['HostedZones'])
    print("\nZonas hospedadas disponíveis:")
    for i, zone in enumerate(hosted_zones):
        print(f"{i + 1}: {zone['Name']} - ID: {zone['Id']}")
    return hosted_zones

def choose_hosted_zone(hosted_zones):
    while True:
        try:
            choice = int(input("Escolha o número da zona hospedada que deseja usar (ou digite 0 para não usar nenhuma): ")) - 1
            if 0 <= choice < len(hosted_zones):
                return hosted_zones[choice]
            elif choice == -1:
                return None
            else:
                print("Número inválido. Tente novamente.")
        except ValueError:
            print("Entrada inválida. Por favor, insira um número.")

def get_elb_hosted_zone_id(load_balancer_arn):
    elb_client = boto3.client('elbv2', region_name='us-east-1')
    response = elb_client.describe_load_balancers(LoadBalancerArns=[load_balancer_arn])
    load_balancer = response['LoadBalancers'][0]
    hosted_zone_id = load_balancer['CanonicalHostedZoneId']
    print(f"Hosted Zone ID do ALB: {hosted_zone_id}")
    return hosted_zone_id

def create_route53_record(hosted_zone_id, domain_name, load_balancer_dns, elb_hosted_zone_id):
    route53_client = boto3.client('route53', region_name='us-east-1')
    try:
        response = route53_client.change_resource_record_sets(
            HostedZoneId=hosted_zone_id,
            ChangeBatch={
                'Comment': 'Adicionando registro para novo serviço ECS',
                'Changes': [
                    {
                        'Action': 'UPSERT',
                        'ResourceRecordSet': {
                            'Name': domain_name,
                            'Type': 'A',
                            'AliasTarget': {
                                'HostedZoneId': elb_hosted_zone_id,
                                'DNSName': load_balancer_dns,
                                'EvaluateTargetHealth': False
                            }
                        }
                    }
                ]
            }
        )
        print(f"Registro DNS '{domain_name}' criado ou atualizado com sucesso no Route 53.")
    except Exception as e:
        print(f"Erro ao criar ou atualizar o registro DNS no Route 53: {e}")

def create_cloudwatch_log_group(log_group_name):
    logs_client = boto3.client('logs', region_name='us-east-1')
    try:
        logs_client.create_log_group(logGroupName=log_group_name)
        print(f"Grupo de logs '{log_group_name}' criado com sucesso no CloudWatch.")
    except logs_client.exceptions.ResourceAlreadyExistsException:
        print(f"Grupo de logs '{log_group_name}' já existe no CloudWatch. Usando o existente.")
    except Exception as e:
        print(f"Erro ao criar o grupo de logs: {e}")

def list_namespaces():
    service_discovery_client = boto3.client('servicediscovery', region_name='us-east-1')
    paginator = service_discovery_client.get_paginator('list_namespaces')
    namespaces = []
    for page in paginator.paginate():
        namespaces.extend(page['Namespaces'])
    print("\nNamespaces disponíveis no AWS Cloud Map:")
    for i, ns in enumerate(namespaces):
        print(f"{i + 1}: {ns['Name']} - ID: {ns['Id']}")
    return namespaces

def choose_or_create_namespace():
    service_discovery_client = boto3.client('servicediscovery', region_name='us-east-1')
    namespaces = list_namespaces()
    print("\nDeseja usar um namespace existente ou criar um novo?")
    print("1: Usar namespace existente")
    print("2: Criar novo namespace")
    print("3: Não usar namespace")
    while True:
        try:
            choice = int(input("Escolha uma opção: "))
            if choice == 1:
                if not namespaces:
                    print("Nenhum namespace existente encontrado.")
                    continue
                while True:
                    try:
                        ns_choice = int(input("Escolha o número do namespace: ")) - 1
                        if 0 <= ns_choice < len(namespaces):
                            return namespaces[ns_choice]
                        else:
                            print("Número inválido. Tente novamente.")
                    except ValueError:
                        print("Entrada inválida. Por favor, insira um número.")
            elif choice == 2:
                namespace_name = input("Digite o nome do novo namespace: ").strip()
                if not namespace_name:
                    print("Nome do namespace não pode ser vazio.")
                    continue
                try:
                    response = service_discovery_client.create_namespace(
                        Name=namespace_name,
                        CreatorRequestId=namespace_name  # Pode ser um UUID para garantir unicidade
                    )
                    namespace = response['Namespace']
                    print(f"Namespace '{namespace_name}' criado com sucesso. ID: {namespace['Id']}")
                    return namespace
                except Exception as e:
                    print(f"Erro ao criar namespace: {e}")
            elif choice == 3:
                return None
            else:
                print("Opção inválida. Tente novamente.")
        except ValueError:
            print("Entrada inválida. Por favor, insira um número.")

def create_service_registry(namespace_id, service_name):
    service_discovery_client = boto3.client('servicediscovery', region_name='us-east-1')
    try:
        response = service_discovery_client.create_service(
            Name=service_name,
            NamespaceId=namespace_id,
            DnsConfig={
                'NamespaceId': namespace_id,
                'DnsRecords': [
                    {
                        'Type': 'A',
                        'TTL': 60
                    }
                ]
            },
            HealthCheckCustomConfig={
                'FailureThreshold': 1
            }
        )
        service_registry = response['Service']
        print(f"Serviço de registro criado com sucesso: {service_registry['Arn']}")
        return service_registry['Arn']
    except Exception as e:
        print(f"Erro ao criar serviço de registro: {e}")
        return None

def create_service(cluster_arn, task_definition, container_name, service_name, desired_count, target_group_arn, container_port, capacity_provider_name=None, launch_type=None, service_registry_arn=None):
    ecs_client = boto3.client('ecs', region_name='us-east-1')
    try:
        existing_services = ecs_client.describe_services(cluster=cluster_arn, services=[service_name])
        service_exists = existing_services['services'] and existing_services['services'][0]['status'] != 'INACTIVE'
        service_params = {
            'cluster': cluster_arn,
            'serviceName': service_name,
            'taskDefinition': task_definition,
            'desiredCount': desired_count,
            'deploymentConfiguration': {'maximumPercent': 200, 'minimumHealthyPercent': 100},
            'loadBalancers': [
                {
                    'targetGroupArn': target_group_arn,
                    'containerName': container_name,
                    'containerPort': container_port
                }
            ],
            'enableECSManagedTags': True,
            'enableExecuteCommand': True,
            'propagateTags': 'TASK_DEFINITION'
        }
        if capacity_provider_name:
            service_params['capacityProviderStrategy'] = [
                {
                    'capacityProvider': capacity_provider_name,
                    'weight': 1,
                    'base': 0
                }
            ]
        elif launch_type:
            service_params['launchType'] = launch_type
        if service_registry_arn:
            service_params['serviceRegistries'] = [
                {
                    'registryArn': service_registry_arn
                }
            ]
            # Adicionando Service Connect Configuration
            service_params['serviceConnectConfiguration'] = {
                'enabled': True,
                'namespace': {
                    'name': service_registry_arn.split('/')[-1]  # Extrair o nome do namespace a partir do ARN
                },
                'services': [
                    {
                        'portName': 'http',  # Nome da porta para Service Connect
                        'discoveryName': service_name,
                        'clientAliases': [
                            {
                                'port': container_port
                            }
                        ]
                    }
                ]
            }
        if service_exists:
            print(f"Serviço '{service_name}' já existe. Atualizando o serviço.")
            response = ecs_client.update_service(
                **service_params,
                forceNewDeployment=True
            )
        else:
            print(f"Criando serviço '{service_name}' no cluster '{cluster_arn}'.")
            response = ecs_client.create_service(**service_params)
        print(f"Serviço '{service_name}' criado ou atualizado com sucesso.")
        return response['service']['serviceArn']
    except Exception as e:
        print(f"Erro ao criar ou atualizar o serviço: {e}")
        return None

def delete_resources(resources):
    ecs_client = boto3.client('ecs', region_name='us-east-1')
    elb_client = boto3.client('elbv2', region_name='us-east-1')
    route53_client = boto3.client('route53', region_name='us-east-1')
    logs_client = boto3.client('logs', region_name='us-east-1')
    service_discovery_client = boto3.client('servicediscovery', region_name='us-east-1')

    if 'serviceArns' in resources:
        for service_arn in resources['serviceArns']:
            try:
                ecs_client.delete_service(cluster=resources['clusterArn'], service=service_arn, force=True)
                print(f"Serviço '{service_arn}' excluído.")
            except Exception as e:
                print(f"Erro ao excluir o serviço '{service_arn}': {e}")
    
    if 'ruleArns' in resources:
        for rule_arn in resources['ruleArns']:
            try:
                elb_client.delete_rule(RuleArn=rule_arn)
                print(f"Regra '{rule_arn}' excluída.")
            except Exception as e:
                print(f"Erro ao excluir a regra '{rule_arn}': {e}")
    
    if 'targetGroupArns' in resources:
        for tg_arn in resources['targetGroupArns']:
            try:
                elb_client.delete_target_group(TargetGroupArn=tg_arn)
                print(f"Grupo de destino '{tg_arn}' excluído.")
            except Exception as e:
                print(f"Erro ao excluir o grupo de destino '{tg_arn}': {e}")
    
    if 'dnsRecords' in resources:
        for dns_record in resources['dnsRecords']:
            try:
                route53_client.change_resource_record_sets(
                    HostedZoneId=resources['hostedZoneId'],
                    ChangeBatch={
                        'Comment': 'Removendo registro do serviço ECS',
                        'Changes': [
                            {
                                'Action': 'DELETE',
                                'ResourceRecordSet': {
                                    'Name': dns_record,
                                    'Type': 'A',
                                    'AliasTarget': {
                                        'HostedZoneId': resources['elbHostedZoneId'],
                                        'DNSName': resources['loadBalancerDNS'],
                                        'EvaluateTargetHealth': False
                                    }
                                }
                            }
                        ]
                    }
                )
                print(f"Registro DNS '{dns_record}' excluído do Route 53.")
            except Exception as e:
                print(f"Erro ao excluir o registro DNS '{dns_record}': {e}")
    
    if 'logGroupNames' in resources:
        for log_group_name in resources['logGroupNames']:
            try:
                logs_client.delete_log_group(logGroupName=log_group_name)
                print(f"Grupo de logs '{log_group_name}' excluído do CloudWatch.")
            except Exception as e:
                print(f"Erro ao excluir o grupo de logs '{log_group_name}': {e}")
    
    if 'serviceRegistryArns' in resources:
        for registry_arn in resources['serviceRegistryArns']:
            try:
                service_discovery_client.delete_service(Id=registry_arn.split('/')[-1])
                print(f"Serviço de registro '{registry_arn}' excluído.")
            except Exception as e:
                print(f"Erro ao excluir o serviço de registro '{registry_arn}': {e}")
    
    if 'namespaceIds' in resources:
        for namespace_id in resources['namespaceIds']:
            try:
                service_discovery_client.delete_namespace(Id=namespace_id)
                print(f"Namespace '{namespace_id}' excluído.")
            except Exception as e:
                print(f"Erro ao excluir o namespace '{namespace_id}': {e}")

def choose_environment():
    environments = ['prod', 'dev', 'staging', 'loadtest']
    print("\nAmbientes disponíveis:")
    for i, env in enumerate(environments, 1):
        print(f"{i}: {env}")
    print(f"{len(environments) + 1}: Inserir nome personalizado")
    while True:
        try:
            choice = int(input("Escolha o número do ambiente que deseja usar: ")) - 1
            if 0 <= choice < len(environments):
                return environments[choice]
            elif choice == len(environments):
                custom_env = input("Insira o nome personalizado do ambiente: ").strip()
                if custom_env:
                    return custom_env
                else:
                    print("Nome personalizado não pode ser vazio.")
            else:
                print("Número inválido. Tente novamente.")
        except ValueError:
            print("Entrada inválida. Por favor, insira um número.")

def main():
    resources_created = {
        'serviceArns': [],
        'ruleArns': [],
        'targetGroupArns': [],
        'dnsRecords': [],
        'logGroupNames': [],
        'serviceRegistryArns': [],
        'namespaceIds': []
    }
    try:
        task_definitions = list_latest_task_definitions()
        chosen_tasks_info = choose_task_definitions(task_definitions)
        clusters = list_clusters()
        if not clusters:
            print("Nenhum cluster disponível. Operação abortada.")
            return
        chosen_cluster = choose_cluster(clusters)
        capacity_provider_name = get_capacity_provider_name(chosen_cluster)
        launch_type = None
        if capacity_provider_name:
            is_valid = validate_capacity_provider(capacity_provider_name)
            if not is_valid:
                capacity_provider_name = None
        else:
            print("Nenhum Capacity Provider válido selecionado.")
        if not capacity_provider_name:
            print("\nSelecione o tipo de lançamento (launch type):")
            print("1: EC2")
            print("2: FARGATE")
            while True:
                try:
                    choice = int(input("Sua escolha: "))
                    if choice == 1:
                        launch_type = 'EC2'
                        break
                    elif choice == 2:
                        launch_type = 'FARGATE'
                        break
                    else:
                        print("Opção inválida. Tente novamente.")
                except ValueError:
                    print("Entrada inválida. Por favor, insira um número.")
        load_balancers = list_load_balancers()
        if not load_balancers:
            print("Nenhum ALB disponível. Operação abortada.")
            return
        chosen_lb = choose_load_balancer(load_balancers)
        vpc_id = get_vpc_id(chosen_lb)
        listener_arn = get_listener_arn(chosen_lb['LoadBalancerArn'])
        if not listener_arn:
            print("Listener na porta 443 não encontrado. Operação abortada.")
            return
        hosted_zones = list_hosted_zones()
        chosen_zone = choose_hosted_zone(hosted_zones)
        if not chosen_zone:
            print("Zona hospedada não selecionada. Operação abortada.")
            return
        elb_hosted_zone_id = get_elb_hosted_zone_id(chosen_lb['LoadBalancerArn'])
        resources_created['elbHostedZoneId'] = elb_hosted_zone_id
        resources_created['loadBalancerDNS'] = chosen_lb['DNSName']
        resources_created['hostedZoneId'] = chosen_zone['Id']
        # Opção de Namespace com Service Connect
        namespace = choose_or_create_namespace()
        service_registry_arn = None
        namespace_id = None
        if namespace:
            namespace_id = namespace['Id']
            # Cria um serviço de registro para Service Connect
            service_registry_arn = create_service_registry(namespace_id, service_name="ecs-service-registry")
            if service_registry_arn:
                resources_created['serviceRegistryArns'].append(service_registry_arn)
                resources_created['namespaceIds'].append(namespace_id)
        health_check_path = input("Insira o caminho do health check (Padrão: /health): ") or "/health"
        if not health_check_path.startswith('/'):
            health_check_path = '/' + health_check_path
        while True:
            try:
                desired_count = int(input(f"Quantas réplicas deseja para cada serviço? (Padrão: 1): ") or "1")
                break
            except ValueError:
                print("Entrada inválida. Por favor, insira um número.")
        environment = choose_environment()
        deployments = []
        for chosen_task, container_name, container_port in chosen_tasks_info:
            service_name = chosen_task.split('/')[-1].split(':')[0]
            domain_name = f"{service_name}.{chosen_zone['Name']}".rstrip('.')
            default_log_group_name = f"/ecs/{environment}/{service_name}"
            while True:
                log_group_choice = input(f"Deseja usar o nome padrão do grupo de logs '{default_log_group_name}' para '{service_name}'? (y/n): ").lower()
                if log_group_choice == 'y':
                    log_group_name = default_log_group_name
                    break
                elif log_group_choice == 'n':
                    log_group_name = input(f"Insira o nome desejado para o grupo de logs de '{service_name}': ").strip()
                    if log_group_name:
                        break
                    else:
                        print("Nome do grupo de logs não pode ser vazio.")
                else:
                    print("Opção inválida. Por favor, responda com 'y' ou 'n'.")
            deployment = {
                'task_definition': chosen_task,
                'container_name': container_name,
                'container_port': container_port,
                'service_name': service_name,
                'domain_name': domain_name,
                'log_group_name': log_group_name
            }
            deployments.append(deployment)
        print("\nResumo das implantações:")
        for deployment in deployments:
            print(f"\nServiço: {deployment['service_name']}")
            print(f"Tarefa: {deployment['task_definition']}")
            print(f"Cluster: {chosen_cluster}")
            print(f"Número de Réplicas: {desired_count}")
            print(f"ALB: {chosen_lb['LoadBalancerArn']}")
            print(f"Listener: {listener_arn} (Porta 443)")
            print(f"Domínio: {deployment['domain_name']}")
            print(f"Health Check Path: {health_check_path}")
            print(f"Grupo de Logs: {deployment['log_group_name']}")
            if namespace:
                print(f"Namespace: {namespace['Name']} (Service Connect ativado)")
    
        confirm = input("\nConfirma a criação dos serviços listados com essas configurações? (y/n): ")
        if confirm.lower() != 'y':
            print("Operação cancelada.")
            return
        for deployment in deployments:
            service_name = deployment['service_name']
            task_definition = deployment['task_definition']
            container_name = deployment['container_name']
            container_port = deployment['container_port']
            domain_name = deployment['domain_name']
            log_group_name = deployment['log_group_name']
            create_cloudwatch_log_group(log_group_name)
            resources_created['logGroupNames'].append(log_group_name)
            target_group_arn = create_target_group(service_name, vpc_id, container_port, health_check_path)
            if not target_group_arn:
                print(f"Falha ao criar ou obter o grupo de destino para '{service_name}'. Operação abortada.")
                continue
            resources_created['targetGroupArns'].append(target_group_arn)
            rule_arn = create_rule_for_service(listener_arn, domain_name, target_group_arn)
            if not rule_arn:
                print(f"Falha ao criar a regra para '{service_name}'. Operação abortada.")
                continue
            resources_created['ruleArns'].append(rule_arn)
            create_route53_record(chosen_zone['Id'], domain_name, chosen_lb['DNSName'], elb_hosted_zone_id)
            resources_created['dnsRecords'].append(domain_name)
            service_registry = None
            if namespace:
                # O serviço de registro já foi criado anteriormente
                service_registry = service_registry_arn
            service_arn = create_service(
                chosen_cluster,
                task_definition,
                container_name,
                service_name,
                desired_count,
                target_group_arn,
                container_port,
                capacity_provider_name=capacity_provider_name,
                launch_type=launch_type,
                service_registry_arn=service_registry
            )
            if service_arn:
                resources_created['serviceArns'].append(service_arn)
                resources_created['clusterArn'] = chosen_cluster
            else:
                print(f"Falha ao criar ou atualizar o serviço '{service_name}'.")
                continue
    except Exception as e:
        print(f"\nOcorreu um erro durante a implantação: {e}")
        rollback = input("Deseja excluir os recursos criados? (y/n): ").lower()
        if rollback == 'y':
            delete_resources(resources_created)
        else:
            print("Os recursos criados foram mantidos.")

if __name__ == "__main__":
    main()
