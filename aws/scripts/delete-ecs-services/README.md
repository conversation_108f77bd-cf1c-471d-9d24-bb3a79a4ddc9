# Script de exclusão de serviços AWS ECS

Este script Python facilita a exclusão de múltiplos serviços no AWS Elastic Container Service (ECS), removendo também automaticamente os recursos associados, como Application Load Balancer (ALB), registros do Amazon Route 53 e grupos de logs no CloudWatch Logs. O script é dinâmico e pode ser utilizado em qualquer conta AWS, sem dependências de valores fixos.

## Funcionalidades

- Listagem de serviços ECS:
    - Lista todos os clusters ECS disponíveis na região.
    - Lista todos os serviços disponíveis em cada cluster, mostrando detalhes relevantes.
- Seleção de serviços para exclusão:
    - Permite excluir todos os serviços ou selecionar serviços específicos para exclusão.
- Exclusão de recursos associados:
    - Remove os serviços ECS selecionados.
    - Exclui grupos de destino (Target Groups) do ALB associados aos serviços.
    - Remove regras no ALB que direcionam tráfego para os serviços excluídos.
    - Exclui registros DNS no Route 53 associados aos serviços.
    - Remove grupos de logs no CloudWatch Logs relacionados aos serviços.
- Confirmação antes da exclusão:
    - Exibe um resumo dos serviços e recursos que serão excluídos.
    - Solicita confirmação antes de proceder com a exclusão.
- Tratamento de recursos inexistentes:
    - Verifica se os recursos existem antes de tentar excluí-los, evitando erros.
    - Trata exceções e informa caso algum recurso não possa ser excluído.

## Requisitos

- Python 3.x
- Boto3 (biblioteca AWS SDK para Python)
- Credenciais AWS configuradas no ambiente (arquivo ~/.aws/credentials ou variáveis de ambiente)
- Permissões AWS adequadas:
    - Permissões para listar, modificar e excluir recursos ECS, ELB, Route 53 e CloudWatch Logs.

## Instalação

1. Clone o repositório ou copie o script para o seu ambiente local.
2. Instale a biblioteca Boto3, se ainda não estiver instalada:

```bash
pip install boto3
```

## Uso

Execute o script com Python 3:
```bash
python3 delete-service.py
```

O script irá:

1. Listar todos os clusters ECS disponíveis na região.

2. Listar todos os serviços disponíveis em cada cluster, mostrando detalhes como:
    - Nome do serviço
    - ARN do serviço
    - Definição de tarefa associada
    - Grupos de destino do ELB associados
    - Grupos de logs do CloudWatch
    - Regras do ALB e domínios associados

3. Oferecer opções de exclusão:
    - Excluir todos os serviços listados e seus recursos associados.
    - Selecionar um ou mais serviços específicos para exclusão.
    - Sair sem realizar nenhuma ação.

4. Solicitar confirmação antes de proceder com a exclusão.

5. Excluir os serviços selecionados e os recursos associados, exibindo mensagens sobre o progresso e status de cada exclusão.

## Passo a Passo
1. Execução do script:

```bash
python3 delete-service.py
```

2. Visualização dos serviços e recursos:

O script listará os serviços encontrados e detalhes dos recursos associados.

3. Escolha da opção de exclusão:
```bash
Opções disponíveis:
1 - Excluir todos os recursos
2 - Excluir um ou mais serviços
3 - Sair
Escolha uma opção:
```
- Digite o número correspondente à opção desejada.

4. Seleção de serviços para exclusão (se aplicável):
    - Se escolher a opção 2, o script listará os serviços disponíveis para exclusão.
    - Digite os números dos serviços que deseja excluir, separados por vírgula.

5. Confirmação:
```bash
Tem certeza de que deseja excluir os serviços selecionados e todos os seus recursos associados? (y/n):
```
- Digite y para confirmar ou n para cancelar.

6. Processo de exclusão:
    - O script iniciará a exclusão dos serviços e recursos associados.
    - Mensagens serão exibidas indicando o progresso e eventuais erros.

## Notas

- Região AWS:
    - O script está configurado para operar na região us-east-1. Se desejar usar outra região, modifique o parâmetro region_name nos clientes do Boto3.
- Recursos Associados:
    - O script tenta excluir todos os recursos associados aos serviços ECS, mas depende de permissões adequadas e da existência desses recursos.
    - Se algum recurso não puder ser excluído, uma mensagem de erro será exibida.
- Permissões AWS:
    - Certifique-se de que as credenciais AWS utilizadas têm as permissões necessárias para listar, modificar e excluir recursos ECS, ELB, Route 53 e CloudWatch Logs.

## Personalização
- Alterar a região AWS:
    - Modifique o parâmetro region_name nos clientes do Boto3 se desejar operar em outra região.
- Modificar parâmetros padrão:
    - Você pode ajustar tempos de espera ou outros parâmetros diretamente no código, se necessário.

## Exemplo de Execução
```bash
python3 delete-service.py
```
```bash
Clusters disponíveis:
1: arn:aws:ecs:us-east-1:123456789012:cluster/clusterA
2: arn:aws:ecs:us-east-1:123456789012:cluster/clusterB

Serviço encontrado: serviceA
ARN do Serviço: arn:aws:ecs:us-east-1:123456789012:service/serviceA
Definição de Tarefa: arn:aws:ecs:us-east-1:123456789012:task-definition/serviceA:1
Grupos de Destino: ['arn:aws:elasticloadbalancing:us-east-1:123456789012:targetgroup/ecs-serviceA/abc123']
Containers: ['serviceA-container']
Grupos de Logs: ['/ecs/serviceA']
Regras do ALB associadas: ['arn:aws:elasticloadbalancing:us-east-1:123456789012:listener-rule/app/albA/abc123/def456']
Domínios associados: ['serviceA.example.com']

Opções disponíveis:
1 - Excluir todos os recursos
2 - Excluir um ou mais serviços
3 - Sair
Escolha uma opção: 2

Serviços disponíveis para exclusão:
1 - serviceA
2 - serviceB
Escolha o(s) número(s) do(s) serviço(s) que deseja excluir, separados por vírgula: 1,2

Você selecionou os seguintes serviços para exclusão:
- serviceA
- serviceB

Tem certeza de que deseja excluir os serviços selecionados e todos os seus recursos associados? (y/n): y

Excluindo serviço 'serviceA'...
Serviço 'serviceA' excluído.
Regra do ALB 'arn:aws:elasticloadbalancing:us-east-1:123456789012:listener-rule/app/albA/abc123/def456' excluída.
Grupo de destino 'arn:aws:elasticloadbalancing:us-east-1:123456789012:targetgroup/ecs-serviceA/abc123' excluído.
Registro DNS 'serviceA.example.com' excluído do Route 53.
Grupo de logs '/ecs/serviceA' excluído.

Excluindo serviço 'serviceB'...
Serviço 'serviceB' excluído.
Regra do ALB 'arn:aws:elasticloadbalancing:us-east-1:123456789012:listener-rule/app/albA/abc123/ghi789' excluída.
Grupo de destino 'arn:aws:elasticloadbalancing:us-east-1:123456789012:targetgroup/ecs-serviceB/def456' excluído.
Registro DNS 'serviceB.example.com' excluído do Route 53.
Grupo de logs '/ecs/serviceB' excluído.

Processo de exclusão concluído.
```

## Contribuição

Sinta-se à vontade para contribuir com melhorias para este script. Faça um fork do repositório, realize as alterações desejadas e abra um pull request.