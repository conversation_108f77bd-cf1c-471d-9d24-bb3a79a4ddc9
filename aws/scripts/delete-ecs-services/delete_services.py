import boto3
import time

def list_clusters():
    """
    Lista todos os clusters ECS disponíveis.
    """
    ecs_client = boto3.client('ecs', region_name='us-east-1')
    clusters = []
    paginator = ecs_client.get_paginator('list_clusters')
    for page in paginator.paginate():
        clusters.extend(page['clusterArns'])
    print("\nClusters disponíveis:")
    for i, cluster in enumerate(clusters):
        print(f"{i + 1}: {cluster}")
    return clusters

def list_services(cluster_arn):
    """
    Lista todos os serviços em um cluster ECS.
    """
    ecs_client = boto3.client('ecs', region_name='us-east-1')
    services = []
    paginator = ecs_client.get_paginator('list_services')
    for page in paginator.paginate(cluster=cluster_arn):
        services.extend(page['serviceArns'])
    return services

def describe_service(cluster_arn, service_name):
    """
    Descreve um serviço ECS.
    """
    ecs_client = boto3.client('ecs', region_name='us-east-1')
    response = ecs_client.describe_services(cluster=cluster_arn, services=[service_name])
    return response['services'][0]

def get_target_group_arns(service):
    """
    Obtém os ARNs dos grupos de destino associados a um serviço ECS.
    """
    load_balancers = service.get('loadBalancers', [])
    target_group_arns = [lb['targetGroupArn'] for lb in load_balancers]
    return target_group_arns

def get_task_definition(service):
    """
    Obtém a definição de tarefa associada a um serviço.
    """
    return service['taskDefinition']

def get_container_names(task_definition_arn):
    """
    Obtém os nomes dos containers na definição de tarefa.
    """
    ecs_client = boto3.client('ecs', region_name='us-east-1')
    response = ecs_client.describe_task_definition(taskDefinition=task_definition_arn)
    container_definitions = response['taskDefinition']['containerDefinitions']
    container_names = [container['name'] for container in container_definitions]
    return container_names

def get_log_group_names(task_definition_arn):
    """
    Obtém os nomes dos grupos de logs associados à definição de tarefa.
    """
    ecs_client = boto3.client('ecs', region_name='us-east-1')
    response = ecs_client.describe_task_definition(taskDefinition=task_definition_arn)
    container_definitions = response['taskDefinition']['containerDefinitions']
    log_group_names = []
    for container in container_definitions:
        if 'logConfiguration' in container:
            log_config = container['logConfiguration']
            if log_config['logDriver'] == 'awslogs':
                options = log_config.get('options', {})
                log_group_name = options.get('awslogs-group')
                if log_group_name:
                    log_group_names.append(log_group_name)
    return log_group_names

def get_alb_rules_and_domains(target_group_arn):
    """
    Obtém as regras do ALB e os domínios associados a um grupo de destino.
    """
    elb_client = boto3.client('elbv2', region_name='us-east-1')
    alb_rules = []
    domains = []
    # Obter todos os listeners associados ao ALB
    load_balancer_arn = get_load_balancer_arn(target_group_arn)
    if not load_balancer_arn:
        return alb_rules, domains

    listeners = elb_client.describe_listeners(LoadBalancerArn=load_balancer_arn)['Listeners']
    for listener in listeners:
        paginator = elb_client.get_paginator('describe_rules')
        for page in paginator.paginate(ListenerArn=listener['ListenerArn']):
            for rule in page['Rules']:
                for action in rule['Actions']:
                    if action['Type'] == 'forward':
                        tg_arns = []
                        if 'TargetGroupArn' in action:
                            tg_arns.append(action['TargetGroupArn'])
                        elif 'ForwardConfig' in action:
                            for tg in action['ForwardConfig'].get('TargetGroups', []):
                                tg_arns.append(tg['TargetGroupArn'])
                        if target_group_arn in tg_arns:
                            alb_rules.append(rule['RuleArn'])
                            # Obter os domínios associados às condições da regra
                            for condition in rule['Conditions']:
                                if condition['Field'] == 'host-header':
                                    domains.extend(condition['Values'])
    return alb_rules, domains

def get_load_balancer_arn(target_group_arn):
    """
    Obtém o ARN do Load Balancer associado a um grupo de destino.
    """
    elb_client = boto3.client('elbv2', region_name='us-east-1')
    response = elb_client.describe_target_groups(TargetGroupArns=[target_group_arn])
    load_balancer_arns = response['TargetGroups'][0].get('LoadBalancerArns', [])
    if load_balancer_arns:
        return load_balancer_arns[0]
    else:
        return None

def get_load_balancer_dns_and_zone(load_balancer_arn):
    """
    Obtém o DNS e o Hosted Zone ID do Load Balancer.
    """
    elb_client = boto3.client('elbv2', region_name='us-east-1')
    response = elb_client.describe_load_balancers(LoadBalancerArns=[load_balancer_arn])
    lb = response['LoadBalancers'][0]
    return lb['DNSName'], lb['CanonicalHostedZoneId']

def list_all_resources():
    """
    Lista todos os recursos ECS e associados.
    """
    clusters = list_clusters()
    all_resources = []
    for cluster in clusters:
        services = list_services(cluster)
        for service_arn in services:
            service_name = service_arn.split('/')[-1]
            service = describe_service(cluster, service_name)
            service_name = service['serviceName']
            print(f"\nServiço encontrado: {service_name}")
            print(f"ARN do Serviço: {service_arn}")
            task_definition_arn = get_task_definition(service)
            print(f"Definição de Tarefa: {task_definition_arn}")
            target_group_arns = get_target_group_arns(service)
            print(f"Grupos de Destino: {target_group_arns}")
            container_names = get_container_names(task_definition_arn)
            print(f"Containers: {container_names}")
            log_group_names = get_log_group_names(task_definition_arn)
            print(f"Grupos de Logs: {log_group_names}")
            # Obter regras do ALB e domínios associados
            alb_rules = []
            domains = []
            for tg_arn in target_group_arns:
                rules, rule_domains = get_alb_rules_and_domains(tg_arn)
                print(f"Regras do ALB associadas ao Target Group {tg_arn}: {rules}")
                print(f"Domínios associados: {rule_domains}")
                alb_rules.extend(rules)
                domains.extend(rule_domains)
            # Remover duplicatas
            alb_rules = list(set(alb_rules))
            domains = list(set(domains))
            # Adicionar ao all_resources
            all_resources.append({
                'clusterArn': cluster,
                'serviceArn': service_arn,
                'serviceName': service_name,
                'taskDefinitionArn': task_definition_arn,
                'targetGroupArns': target_group_arns,
                'containerNames': container_names,
                'logGroupNames': log_group_names,
                'albRules': alb_rules,
                'domains': domains,
                'loadBalancerArn': get_load_balancer_arn(target_group_arns[0]) if target_group_arns else None
            })
    return all_resources

def delete_service_resources(resource):
    """
    Exclui os recursos associados a um serviço.
    """
    ecs_client = boto3.client('ecs', region_name='us-east-1')
    elb_client = boto3.client('elbv2', region_name='us-east-1')
    logs_client = boto3.client('logs', region_name='us-east-1')
    route53_client = boto3.client('route53', region_name='us-east-1')

    # Deletar serviço ECS
    try:
        ecs_client.update_service(cluster=resource['clusterArn'], service=resource['serviceName'], desiredCount=0)
        ecs_client.delete_service(cluster=resource['clusterArn'], service=resource['serviceName'])
        print(f"Serviço '{resource['serviceName']}' excluído.")
    except Exception as e:
        print(f"Erro ao excluir o serviço: {e}")

    # Deletar regras do ALB (antes de excluir o target group)
    for rule_arn in resource['albRules']:
        try:
            elb_client.delete_rule(RuleArn=rule_arn)
            print(f"Regra do ALB '{rule_arn}' excluída.")
        except Exception as e:
            print(f"Erro ao excluir a regra do ALB '{rule_arn}': {e}")

    # Aguardar a exclusão completa do serviço, com limite de tempo de 10 segundos
    start_time = time.time()
    while True:
        try:
            services = ecs_client.describe_services(cluster=resource['clusterArn'], services=[resource['serviceName']])
            if not services['services'] or services['services'][0]['status'] == 'INACTIVE':
                print(f"Serviço '{resource['serviceName']}' removido completamente.")
                break
        except Exception as e:
            print(f"Erro ao verificar o status do serviço '{resource['serviceName']}': {e}")
            break  # Se houver um erro, saímos do loop

        elapsed_time = time.time() - start_time
        if elapsed_time > 10:
            print(f"Tempo limite de espera atingido para a exclusão do serviço '{resource['serviceName']}'. Continuando...")
            break
        time.sleep(2)  # Espera 2 segundos antes de verificar novamente

    # Deletar grupos de destino
    for tg_arn in resource['targetGroupArns']:
        try:
            elb_client.delete_target_group(TargetGroupArn=tg_arn)
            print(f"Grupo de destino '{tg_arn}' excluído.")
        except Exception as e:
            print(f"Erro ao excluir o grupo de destino '{tg_arn}': {e}")

    # Deletar registros do Route 53
    for domain in resource['domains']:
        # Obter a zona hospedada correspondente ao domínio
        try:
            hosted_zone_id = get_hosted_zone_id_for_domain(domain)
            if hosted_zone_id:
                # Obter o DNS e Hosted Zone ID do Load Balancer
                lb_dns, lb_hosted_zone_id = get_load_balancer_dns_and_zone(resource['loadBalancerArn'])
                # Deletar o registro DNS
                delete_route53_record(hosted_zone_id, domain, lb_dns, lb_hosted_zone_id)
            else:
                print(f"Zona hospedada não encontrada para o domínio '{domain}'.")
        except Exception as e:
            print(f"Erro ao excluir o registro DNS '{domain}': {e}")

    # Deletar grupos de logs
    for log_group_name in resource['logGroupNames']:
        try:
            logs_client.delete_log_group(logGroupName=log_group_name)
            print(f"Grupo de logs '{log_group_name}' excluído.")
        except Exception as e:
            print(f"Erro ao excluir o grupo de logs '{log_group_name}': {e}")

def get_hosted_zone_id_for_domain(domain_name):
    """
    Obtém o ID da zona hospedada para um domínio específico.
    """
    route53_client = boto3.client('route53', region_name='us-east-1')
    paginator = route53_client.get_paginator('list_hosted_zones')
    for page in paginator.paginate():
        for zone in page['HostedZones']:
            if domain_name.endswith(zone['Name'].rstrip('.')):
                return zone['Id']
    return None

def delete_route53_record(hosted_zone_id, domain_name, load_balancer_dns, elb_hosted_zone_id):
    """
    Deleta um registro no Route 53 apontando para o ALB.
    """
    route53_client = boto3.client('route53', region_name='us-east-1')
    try:
        response = route53_client.change_resource_record_sets(
            HostedZoneId=hosted_zone_id,
            ChangeBatch={
                'Comment': 'Removendo registro do serviço ECS',
                'Changes': [
                    {
                        'Action': 'DELETE',
                        'ResourceRecordSet': {
                            'Name': domain_name,
                            'Type': 'A',
                            'AliasTarget': {
                                'HostedZoneId': elb_hosted_zone_id,
                                'DNSName': load_balancer_dns,
                                'EvaluateTargetHealth': False
                            }
                        }
                    }
                ]
            }
        )
        print(f"Registro DNS '{domain_name}' excluído do Route 53.")
    except Exception as e:
        print(f"Erro ao excluir o registro DNS '{domain_name}': {e}")

def delete_selected_resources(selected_resources):
    """
    Exclui os recursos dos serviços selecionados.
    """
    for resource in selected_resources:
        delete_service_resources(resource)

def delete_all_resources(all_resources):
    """
    Exclui todos os recursos listados.
    """
    for resource in all_resources:
        delete_service_resources(resource)

def main():
    all_resources = list_all_resources()

    if not all_resources:
        print("Nenhum serviço encontrado para exclusão.")
        return

    print("\nOpções disponíveis:")
    print("1 - Excluir todos os recursos")
    print("2 - Excluir um ou mais serviços")
    print("3 - Sair")
    choice = input("Escolha uma opção: ")

    if choice == '1':
        confirm = input("Tem certeza de que deseja excluir TODOS os recursos listados? (y/n): ")
        if confirm.lower() == 'y':
            delete_all_resources(all_resources)
        else:
            print("Operação cancelada.")
    elif choice == '2':
        print("\nServiços disponíveis para exclusão:")
        for idx, resource in enumerate(all_resources):
            print(f"{idx + 1} - {resource['serviceName']}")
        service_choices = input("Escolha o(s) número(s) do(s) serviço(s) que deseja excluir, separados por vírgula: ")
        indices = [int(num.strip()) -1 for num in service_choices.split(',') if num.strip().isdigit()]
        selected_resources = [all_resources[i] for i in indices if 0 <= i < len(all_resources)]
        if selected_resources:
            print("Você selecionou os seguintes serviços para exclusão:")
            for resource in selected_resources:
                print(f"- {resource['serviceName']}")
            confirm = input(f"Tem certeza de que deseja excluir os serviços selecionados e todos os seus recursos associados? (y/n): ")
            if confirm.lower() == 'y':
                delete_selected_resources(selected_resources)
            else:
                print("Operação cancelada.")
        else:
            print("Nenhum serviço válido foi selecionado.")
    else:
        print("Saindo...")

if __name__ == "__main__":
    main()
