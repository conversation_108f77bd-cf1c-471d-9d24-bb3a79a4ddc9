# AWS Secrets Manager Migration Script

Este script em Python usa a biblioteca `boto3` para gerenciar segredos no AWS Secrets Manager. Ele permite listar, exportar e importar segredos entre contas da AWS, com a opção de filtrar os segredos que começam com prefixos específicos, como "prod" ou "staging".

## Funcionalidades

- **Listar Segredos**: Lista todos os segredos na região especificada.
- **Exportar Segredos**: Exporta segredos que começam com um prefixo específico ("prod" ou "staging") para arquivos JSON ou texto.
- **Importar Segredos**: Importa segredos de arquivos JSON para outra conta, mantendo o nome e o formato original.

## Pré-requisitos

- Python 3.6 ou superior
- Biblioteca `boto3` instalada
- Credenciais configuradas para acesso ao AWS Secrets Manager (use `aws configure` para configurar)

## Instalação

Clone este repositório e instale os requisitos:

```bash
git clone <URL_DO_REPOSITORIO>
cd <DIRETORIO_DO_REPOSITORIO>
pip install boto3
```

## Uso

### Listar Segredos

Para listar todos os segredos na região `us-east-1`:

```bash
python3.10 secret-migration.py --option list
```

### Exportar Segredos

Para exportar segredos que começam com "prod":

```bash
python3.10 secret-migration.py --option export --prefix prod
```

Para exportar segredos que começam com "staging":

```bash
python3.10 secret-migration.py --option export --prefix staging
```

### Importar Segredos

Para importar segredos que começam com "prod":

```bash
python3.10 secret-migration.py --option import --prefix prod
```

Para importar segredos que começam com "staging":

```bash
python3.10 secret-migration.py --option import --prefix staging
```

## Notas

- O script cria um diretório `secrets` automaticamente para armazenar os arquivos exportados.
- Certifique-se de que você tenha permissões adequadas para acessar e manipular segredos nas contas de origem e destino.
- O script respeita a nomenclatura original dos segredos durante a importação, substituindo caracteres que não são permitidos em nomes de arquivos locais.
- AO EXPORTAR OS SEGREDOS, NÃO ENVIE PARA O GITHUB E MANIPULE COM CUIDADO!