import boto3
import argparse
import json
import os
import re

def list_secrets(client):
    """
    Função para listar todos os segredos na região especificada.
    """
    secrets = []
    next_token = None

    print("Listando segredos na região us-east-1...")

    while True:
        if next_token:
            response = client.list_secrets(NextToken=next_token)
        else:
            response = client.list_secrets()

        for secret in response.get('SecretList', []):
            secrets.append(secret['Name'])
            print(f"Encontrado segredo: {secret['Name']}")

        next_token = response.get('NextToken')
        if not next_token:
            break

    return secrets

def get_secret_value(client, secret_name):
    """
    Função para obter o valor de um segredo.
    """
    try:
        response = client.get_secret_value(SecretId=secret_name)
        return response['SecretString']
    except client.exceptions.ResourceNotFoundException:
        print(f"Segredo '{secret_name}' não encontrado.")
        return None

def is_valid_json(value):
    """
    Função para verificar se uma string é um JSON válido.
    """
    try:
        json.loads(value)
        return True
    except ValueError:
        return False

def sanitize_filename(secret_name):
    """
    Função para limpar caracteres inválidos do nome do arquivo.
    """
    # Substitui qualquer caractere inválido por '_'
    sanitized_name = re.sub(r'[\\/:"*?<>|]', '_', secret_name)
    return sanitized_name

def export_secrets_to_json(client, prefix):
    """
    Função para exportar todos os segredos como arquivos JSON separados ou texto simples.
    """
    secrets = list_secrets(client)

    if not os.path.exists('secrets'):
        os.makedirs('secrets')

    for secret_name in secrets:
        # Filtra apenas segredos que começam com o prefixo especificado
        if not secret_name.startswith(prefix):
            continue

        secret_value = get_secret_value(client, secret_name)
        if secret_value:
            sanitized_secret_name = sanitize_filename(secret_name)
            file_path = f"secrets/{sanitized_secret_name}.json"
            
            try:
                # Verifica se o segredo é JSON válido
                if is_valid_json(secret_value):
                    secret_data = json.loads(secret_value)
                    with open(file_path, 'w') as file:
                        json.dump(secret_data, file, indent=4)
                else:
                    # Salva como texto simples se não for JSON
                    with open(file_path, 'w') as file:
                        file.write(secret_value)

                print(f"Segredo '{secret_name}' exportado para '{file_path}'.")
            except Exception as e:
                print(f"Erro ao exportar segredo '{secret_name}': {e}")

def import_secrets_from_json(client, prefix):
    """
    Função para importar segredos de arquivos JSON para a conta de destino.
    """
    if not os.path.exists('secrets'):
        print("Diretório 'secrets' não encontrado. Execute a exportação primeiro.")
        return

    for filename in os.listdir('secrets'):
        if filename.endswith('.json'):
            # Recupera o nome original do segredo removendo a extensão e restaurando '/'
            secret_name = filename.replace('_', '/').replace('.json', '')

            # Filtra apenas segredos que começam com o prefixo especificado
            if not secret_name.startswith(prefix):
                continue

            with open(f'secrets/{filename}', 'r') as file:
                secret_value = file.read()
                try:
                    # Verifica se o segredo já existe na conta de destino
                    if not get_secret_value(client, secret_name):
                        client.create_secret(Name=secret_name, SecretString=secret_value)
                        print(f"Segredo '{secret_name}' criado com sucesso na conta de destino.")
                    else:
                        print(f"Segredo '{secret_name}' já existe na conta de destino, ignorando.")
                except Exception as e:
                    print(f"Erro ao importar segredo '{secret_name}': {e}")

def main():
    parser = argparse.ArgumentParser(description='Gerenciar segredos do AWS Secrets Manager.')
    parser.add_argument('--option', choices=['list', 'export', 'import'], required=True, help='Opção: "list" para listar segredos, "export" para exportar para arquivos JSON ou "import" para importar para outra conta.')
    parser.add_argument('--prefix', choices=['prod', 'staging'], required=True, help='Prefixo dos segredos para exportar ou importar ("prod" ou "staging").')

    args = parser.parse_args()

    # Cliente para a conta de origem (região us-east-1)
    source_client = boto3.client('secretsmanager', region_name='us-east-1')

    # Cliente para a conta de destino (região us-east-1)
    destination_client = boto3.client('secretsmanager', region_name='us-east-1')

    if args.option == 'list':
        list_secrets(source_client)
    
    elif args.option == 'export':
        export_secrets_to_json(source_client, args.prefix)
    
    elif args.option == 'import':
        import_secrets_from_json(destination_client, args.prefix)

if __name__ == "__main__":
    main()
