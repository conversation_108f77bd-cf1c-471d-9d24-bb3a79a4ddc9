import pulumi
import pulumi_aws as aws

def create_sqs_queue():
    # Cria uma chave KMS gerenciada pelo cliente para criptografia
    kms_key = aws.kms.Key(
        "prod-delayed-events-key",
        description="KMS Key for SQS prod-delayed-events",
        deletion_window_in_days=30,
        tags={"Environment": "prod"}
    )

    # Define uma fila SQS com criptografia ativada usando a chave KMS
    sqs_queue = aws.sqs.Queue(
        "prod-delayed-events",
        name="prod-delayed-events",
        kms_master_key_id=kms_key.arn,  # Associa a chave KMS para criptografia
        message_retention_seconds=1209600,  # Retenção de mensagens (14 dias)
        visibility_timeout_seconds=30,  # Tempo de espera de visibilidade
        tags={"Environment": "prod"}
    )

    # Exporta os detalhes da fila SQS
    pulumi.export("sqs_queue_name", sqs_queue.name)
    pulumi.export("sqs_queue_arn", sqs_queue.arn)
    pulumi.export("kms_key_arn", kms_key.arn)

    return sqs_queue, kms_key
