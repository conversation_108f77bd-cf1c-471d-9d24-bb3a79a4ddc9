import pulumi
import pulumi_aws as aws

def create_amazon_mq(vpc, private_subnets):
    # Security Group para o Amazon MQ
    mq_sg = aws.ec2.SecurityGroup(
        "magie-prod-mq-sg",
        vpc_id=vpc.id,
        description="Security group for Amazon MQ",
        ingress=[
            aws.ec2.SecurityGroupIngressArgs(
                protocol="tcp",
                from_port=5671,  # Porta segura padrão para RabbitMQ
                to_port=5671,
                cidr_blocks=["0.0.0.0/0"]
            )
        ],
        egress=[
            aws.ec2.SecurityGroupEgressArgs(
                protocol="-1",
                from_port=0,
                to_port=0,
                cidr_blocks=["0.0.0.0/0"]
            )
        ],
        tags={"Name": "magie-prod-mq-sg"}
    )

    # Seleciona um único subnet para o modo SINGLE_INSTANCE
    selected_subnet = private_subnets[0]

    # Configuração mínima válida para RabbitMQ usando Cuttlefish
    mq_configuration = aws.mq.Configuration(
        "magie-prod-mq-configuration",
        engine_type="RabbitMQ",
        engine_version="3.11.20",
        data="""
listeners.ssl.default = 5671
management.listener.ssl = true
management.listener.port = 15672
""",
        tags={"Name": "magie-prod-mq-configuration"}
    )

    # Criação do Broker para RabbitMQ
    mq_broker = aws.mq.Broker(
        "magie-prod-mq-broker",
        broker_name="magie-prod-rabbitmq",
        engine_type="RabbitMQ",
        engine_version="3.11.20",
        host_instance_type="mq.m5.large",
        deployment_mode="SINGLE_INSTANCE",
        publicly_accessible=False,
        subnet_ids=[selected_subnet.id],
        security_groups=[mq_sg.id],
        configuration=aws.mq.BrokerConfigurationArgs(
            id=mq_configuration.id,
            revision=mq_configuration.latest_revision
        ),
        logs=aws.mq.BrokerLogsArgs(
            general=True
        ),
        users=[aws.mq.BrokerUserArgs(
            username="magie",
            password="Uvwn893my35yYBw5jvymi3"
        )],
        tags={"Name": "magie-prod-rabbitmq"}
    )

    # Configurar entradas no Route 53 para o Amazon MQ
    zone_id = "Z0416647302S97RPLHCRL"
    domain_name = "magie.services"

    mq_endpoint = mq_broker.instances.apply(
        lambda instances: instances[0]["endpoints"][0].split("//")[-1]
    )

    dns_record = aws.route53.Record(
        "mq-dns-record",
        zone_id=zone_id,
        name=f"mq-1.{domain_name}",
        type="CNAME",
        ttl=300,
        records=[mq_endpoint]
    )

    # Exportar informações do Amazon MQ
    pulumi.export("mq_broker_arn", mq_broker.arn)
    pulumi.export("mq_security_group_id", mq_sg.id)
    pulumi.export("route53_record", dns_record.fqdn)

    return mq_broker, mq_sg

