import pulumi_aws as aws
import pulumi
import json

def create_secret():
    # Lista de chaves para o segredo
    secret_keys = [
        "spring.profiles.active", "ACCOUNTING_BASE_URI", "BANKING_BASE_URI",
        "BFF_BASE_URI", "BACKOFFICE_BASE_URI", "MEDIA_BASE_URI", "AUTH_BASE_URI",
        "WEBHOOK_BASE_URI", "ONBOARDING_BASE_URI", "OP<PERSON><PERSON>NANCE_BASE_URI",
        "MGM_BASE_URI", "OTEL_COLLECTOR_ENDPOINT", "CELCOIN_BASIC_USERNAME",
        "CELCOIN_BASIC_PASSWORD", "RETOOL_API_KEY", "UNICO_API_KEY",
        "JAVA_TOOL_OPTIONS", "JAVA_OPTS", "INICIADOR_HMAC"
    ]

    # Valores vazios para o segredo
    secret_values = {key: "" for key in secret_keys}

    secret = aws.secretsmanager.Secret("gateway-secret",
        name="prod/gateway/secrets",
        tags={"Environment": "prod"}
    )

    secret_version = aws.secretsmanager.SecretVersion("gateway-secret-version",
        secret_id=secret.id,
        secret_string=pulumi.Output.secret(
            pulumi.Output.all(secret_values).apply(lambda values: json.dumps(values))
        )
    )

    return secret