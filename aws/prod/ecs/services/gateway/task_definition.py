import pulumi
import pulumi_aws as aws
import json

# Defina a lista de chaves de segredo que será usada no task definition
secret_keys = [
    "spring.profiles.active", "ACCOUNTING_BASE_URI", "BANKING_BASE_URI",
    "BFF_BASE_URI", "BACKOFFICE_BASE_URI", "MEDIA_BASE_URI", "AUTH_BASE_URI",
    "WEBHOOK_BASE_URI", "ONBOARDING_BASE_URI", "OPENFINANCE_BASE_URI",
    "MGM_BASE_URI", "OTEL_COLLECTOR_ENDPOINT", "CELCOIN_BASIC_USERNAME",
    "CELCOIN_BASIC_PASSWORD", "RETOOL_API_KEY", "UNICO_API_KEY",
    "JAVA_TOOL_OPTIONS", "JAVA_OPTS", "INICIADOR_HMAC"
]

# Defina a função para criar a definição da tarefa ECS
def create_task_definition(execution_role_arn, task_role_arn, ecr_repository_uri, secret_arn, log_group_name):
    container_definitions = pulumi.Output.all(ecr_repository_uri, secret_arn, log_group_name).apply(
        lambda args: json.dumps([{
            "name": "gateway-container",
            "image": args[0],
            "essential": True,
            "memoryReservation": 1024,
            "memory": 1024,
            "portMappings": [{"containerPort": 8080, "protocol": "tcp"}],
            "secrets": [
                {"name": key, "valueFrom": f"{args[1]}:{key}::"}
                for key in secret_keys
            ],
            "logConfiguration": {
                "logDriver": "awslogs",
                "options": {
                    "awslogs-group": args[2],
                    "awslogs-region": "us-east-1",
                    "awslogs-stream-prefix": "ecs"
                }
            }
        }])
    )

    task_definition = aws.ecs.TaskDefinition("gateway-prod-task",
        family="gateway-prod",
        network_mode="awsvpc",
        requires_compatibilities=["EC2"],
        execution_role_arn=execution_role_arn,
        task_role_arn=task_role_arn,
        container_definitions=container_definitions
    )

    return task_definition
