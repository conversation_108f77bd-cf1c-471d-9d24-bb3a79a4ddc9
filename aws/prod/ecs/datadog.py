import pulumi
import pulumi_aws as aws

def create_datadog_task(ecs_cluster, private_subnets, datadog_sg, vpc_id, service_discovery_namespace_id):
    """
    Cria uma tarefa do Datadog Agent em um cluster ECS.

    Args:
        ecs_cluster: O cluster ECS onde a tarefa será criada.
        private_subnets: Uma lista de sub-redes privadas onde a tarefa será executada.
        datadog_sg: O grupo de segurança para a tarefa do Datadog Agent.
        vpc_id: O ID da VPC.
        service_discovery_namespace_id: O ID do namespace do serviço de descoberta.

    Returns:
        Uma tupla contendo o grupo de logs, a definição da tarefa, o serviço ECS e o registro de serviço.
    """

    log_group = aws.cloudwatch.LogGroup(
        "datadog-agent-log-group",
        name="/ecs/datadog-agent",
        retention_in_days=7,
        tags={"Name": "datadog-agent-log-group"}
    )

    datadog_task_definition = aws.ecs.TaskDefinition(
        "datadog-agent-task",
        family="datadog-agent",
        task_role_arn="arn:aws:iam::992382535149:role/ecs-tasks",
        execution_role_arn="arn:aws:iam::992382535149:role/ecs-tasks",
        network_mode="awsvpc",
        container_definitions="""[
            {
                "name": "datadog-agent",
                "image": "public.ecr.aws/datadog/agent:7-jmx",
                "memory": 1024,
                "essential": true,
                "portMappings": [
                    {"containerPort": 8125, "protocol": "udp"},
                    {"containerPort": 8126, "protocol": "tcp"}
                ],
                "environment": [
                    {"name": "DD_API_KEY", "value": "********************************"},
                    {"name": "DD_SITE", "value": "datadoghq.com"},
                    {"name": "DD_LOGS_ENABLED", "value": "true"},
                    {"name": "DD_PROCESS_AGENT_ENABLED", "value": "true"},
                    {"name": "DD_APM_ENABLED", "value": "true"},
                    {"name": "DD_LOG_LEVEL", "value": "INFO"},
                    {"name": "ECS_FARGATE", "value": "false"}
                ],
                "logConfiguration": {
                    "logDriver": "awslogs",
                    "options": {
                        "awslogs-group": "/ecs/datadog-agent",
                        "awslogs-region": "us-east-1",
                        "awslogs-stream-prefix": "datadog"
                    }
                }
            }
        ]""",
        requires_compatibilities=["EC2"],
        memory="1024",
        cpu="512",
        tags={"Name": "datadog-agent-task"}
    )

    service_registry = aws.servicediscovery.Service(
        "datadog-service-registry",
        name="datadog-service",
        namespace_id=service_discovery_namespace_id,
        dns_config=aws.servicediscovery.ServiceDnsConfigArgs(
            namespace_id=service_discovery_namespace_id,
            dns_records=[
                aws.servicediscovery.ServiceDnsConfigDnsRecordArgs(
                    type="A",
                    ttl=60
                )
            ],
            routing_policy="MULTIVALUE"
        )
    )

    datadog_service = aws.ecs.Service(
        "datadog-agent-service",
        cluster=ecs_cluster.id,
        task_definition=datadog_task_definition.arn,
        desired_count=1,
        launch_type="EC2",
        network_configuration=aws.ecs.ServiceNetworkConfigurationArgs(
            subnets=[subnet.id for subnet in private_subnets],
            security_groups=[datadog_sg.id]
        ),
        service_registries=[
            aws.ecs.ServiceServiceRegistryArgs(  # Corrigido
                registry_arn=service_registry.arn,
                container_name="datadog-agent",
                container_port=8126
            )
        ],
        tags={"Name": "datadog-agent-service"}
    )

    return log_group, datadog_task_definition, datadog_service, service_registry