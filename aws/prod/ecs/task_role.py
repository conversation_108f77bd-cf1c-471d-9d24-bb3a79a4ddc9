import pulumi
import pulumi_aws as aws

def create_task_role():
    # Define a política para a task role
    task_role_policy = {
        "Version": "2012-10-17",
        "Statement": [
            {
                "Effect": "Allow",
                "Action": [
                    "cognito-idp:*",
                    "cognito-identity:*",
                    "ec2:*",
                    "ecr:*",
                    "ecs:*",
                    "elasticfilesystem:*",
                    "mq:*",
                    "msk:*",
                    "route53:*",
                    "sns:*",
                    "sqs:*",
                    "ssm:*",
                    "applicationdiscovery:*",
                    "servicediscovery:*",
                    "secretsmanager:*",
                    "cloudwatch:*"
                ],
                "Resource": "*"
            },
            {
                "Effect": "Allow",
                "Action": [
                    "ecs:RunTask",
                    "ecs:StartTask",
                    "ecs:StopTask",
                    "ecs:DescribeServices",
                    "ecs:DescribeTasks",
                    "ecs:DescribeTaskDefinition",
                    "ecs:RegisterTaskDefinition",
                    "ecs:UpdateService",
                    "ecs:ListTasks",
                    "ecs:ListServices",
                    "ecs:CreateCluster",
                    "ecs:DeleteCluster",
                    "ecs:DescribeClusters",
                    "logs:CreateLogStream",
                    "logs:PutLogEvents"
                ],
                "Resource": "*"
            },
            {
                "Effect": "Allow",
                "Action": [
                    "ssm:GetParameter",
                    "ssm:GetParameters",
                    "ssm:GetParameterHistory"
                ],
                "Resource": "arn:aws:ssm:*:*:parameter/*"
            },
            {
                "Effect": "Allow",
                "Action": [
                    "secretsmanager:GetSecretValue",
                    "secretsmanager:DescribeSecret"
                ],
                "Resource": "arn:aws:secretsmanager:*:*:secret:*"
            },
            {
                "Effect": "Allow",
                "Action": [
                    "ecr:GetAuthorizationToken",
                    "ecr:BatchCheckLayerAvailability",
                    "ecr:GetDownloadUrlForLayer",
                    "ecr:BatchGetImage"
                ],
                "Resource": "*"
            },
            {
                "Effect": "Allow",
                "Action": [
                    "autoscaling:DescribeAutoScalingGroups",
                    "autoscaling:UpdateAutoScalingGroup",
                    "autoscaling:CreateOrUpdateTags"
                ],
                "Resource": "*"
            },
            {
                "Effect": "Allow",
                "Action": [
                    "bedrock:InvokeModel"
                ],
                "Resource": "*"
            },
            {
                "Effect": "Allow",
                "Action": [
                    "ecs:TagResource",
                    "ecs:UntagResource"
                ],
                "Resource": "*"
            }
        ]
    }

    # Cria a role para ECS tasks
    task_role = aws.iam.Role(
        "ecs-tasks-role",
        assume_role_policy="""{
            "Version": "2012-10-17",
            "Statement": [
                {
                    "Effect": "Allow",
                    "Principal": {
                        "Service": "ecs-tasks.amazonaws.com"
                    },
                    "Action": "sts:AssumeRole"
                }
            ]
        }""",
        tags={"Name": "ecs-tasks"}
    )

    # Anexa a política à role
    task_policy_attachment = aws.iam.RolePolicy(
        "ecs-tasks-policy",
        role=task_role.name,
        policy=task_role_policy
    )

    pulumi.export("ecs_task_role_arn", task_role.arn)

    return task_role
