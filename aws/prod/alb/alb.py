import pulumi
import pulumi_aws as aws

def create_albs(vpc, public_subnets, private_subnets, alb_sg):
    # ALB público
    public_alb = aws.lb.LoadBalancer("public-app-lb",
                                     internal=False,
                                     security_groups=[alb_sg.id],
                                     subnets=[subnet.id for subnet in public_subnets],
                                     load_balancer_type="application",
                                     enable_deletion_protection=False,
                                     idle_timeout=400,
                                     tags={"Name": "public-app-lb"})

    # ALB privado
    private_alb = aws.lb.LoadBalancer("private-app-lb",
                                      internal=True,
                                      security_groups=[alb_sg.id],
                                      subnets=[subnet.id for subnet in private_subnets],
                                      load_balancer_type="application",
                                      enable_deletion_protection=False,
                                      idle_timeout=400,
                                      tags={"Name": "private-app-lb"})

    # Target Group para o ALB público
    public_target_group = aws.lb.TargetGroup("public-app-tg",
                                             port=80,
                                             protocol="HTTP",
                                             target_type="instance",
                                             vpc_id=vpc.id,
                                             health_check=aws.lb.TargetGroupHealthCheckArgs(
                                                 protocol="HTTP",
                                                 path="/",
                                                 interval=30,
                                                 timeout=5,
                                                 healthy_threshold=5,
                                                 unhealthy_threshold=2
                                             ),
                                             tags={"Name": "public-app-tg"})

    # Target Group para o ALB privado
    private_target_group = aws.lb.TargetGroup("private-app-tg",
                                              port=80,
                                              protocol="HTTP",
                                              target_type="instance",
                                              vpc_id=vpc.id,
                                              health_check=aws.lb.TargetGroupHealthCheckArgs(
                                                  protocol="HTTP",
                                                  path="/",
                                                  interval=30,
                                                  timeout=5,
                                                  healthy_threshold=5,
                                                  unhealthy_threshold=2
                                              ),
                                              tags={"Name": "private-app-tg"})

    # Listener HTTP para redirecionar para HTTPS no ALB público
    public_http_listener = aws.lb.Listener("public-http-listener",
                                           load_balancer_arn=public_alb.arn,
                                           port=80,
                                           default_actions=[aws.lb.ListenerDefaultActionArgs(
                                               type="redirect",
                                               redirect=aws.lb.ListenerDefaultActionRedirectArgs(
                                                   protocol="HTTPS",
                                                   port="443",
                                                   status_code="HTTP_301"
                                               )
                                           )])

    # Listener HTTP para redirecionar para HTTPS no ALB privado
    private_http_listener = aws.lb.Listener("private-http-listener",
                                            load_balancer_arn=private_alb.arn,
                                            port=80,
                                            default_actions=[aws.lb.ListenerDefaultActionArgs(
                                                type="redirect",
                                                redirect=aws.lb.ListenerDefaultActionRedirectArgs(
                                                    protocol="HTTPS",
                                                    port="443",
                                                    status_code="HTTP_301"
                                                )
                                            )])

    # Listener HTTPS no ALB público
    public_https_listener = aws.lb.Listener("public-https-listener",
                                            load_balancer_arn=public_alb.arn,
                                            port=443,
                                            protocol="HTTPS",
                                            ssl_policy="ELBSecurityPolicy-TLS13-1-2-2021-06",
                                            certificate_arn="arn:aws:acm:us-east-1:339712932331:certificate/5b0c6961-ac43-43a8-94ca-588323e8542e",
                                            default_actions=[aws.lb.ListenerDefaultActionArgs(
                                                type="fixed-response",
                                                fixed_response=aws.lb.ListenerDefaultActionFixedResponseArgs(
                                                    content_type="text/plain",
                                                    message_body="Service Unavailable",
                                                    status_code="503"
                                                )
                                            )])

    # Listener HTTPS no ALB privado
    private_https_listener = aws.lb.Listener("private-https-listener",
                                             load_balancer_arn=private_alb.arn,
                                             port=443,
                                             protocol="HTTPS",
                                             ssl_policy="ELBSecurityPolicy-TLS13-1-2-2021-06",
                                             certificate_arn="arn:aws:acm:us-east-1:339712932331:certificate/5b0c6961-ac43-43a8-94ca-588323e8542e",
                                             default_actions=[aws.lb.ListenerDefaultActionArgs(
                                                 type="fixed-response",
                                                 fixed_response=aws.lb.ListenerDefaultActionFixedResponseArgs(
                                                     content_type="text/plain",
                                                     message_body="Service Unavailable",
                                                     status_code="503"
                                                 )
                                             )])

    pulumi.export("public_alb_dns_name", public_alb.dns_name)
    pulumi.export("private_alb_dns_name", private_alb.dns_name)

    # Retorna ambos os ALBs, Target Groups e Listeners
    return (public_alb, public_target_group, public_http_listener.arn, public_https_listener.arn,
            private_alb, private_target_group, private_http_listener.arn, private_https_listener.arn)
