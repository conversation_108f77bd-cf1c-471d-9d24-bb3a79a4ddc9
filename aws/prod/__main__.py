import pulumi
import pulumi_aws as aws
from vpc.vpc import create_vpc
from vpc.subnets import create_subnets
from vpc.security_groups import create_security_groups
from ecs.cluster import create_ecs_cluster, create_ecs_instance_resources
from alb.alb import create_albs
from ecs.task_role import create_task_role
from s3.buckets import create_s3_buckets
from rds.rds import create_rds_instance
from msk.msk import create_msk_cluster
from amazon_mq.amazon_mq import create_amazon_mq
from sqs.sqs import create_sqs_queue
from ecr.ecr import create_ecr_repositories

# Criação da VPC e Gateway de Internet
vpc, internet_gateway = create_vpc()
public_subnets, private_subnets = create_subnets(vpc, internet_gateway)

# Criação dos Grupos de Segurança
alb_sg, ecs_sg, datadog_sg, nlb_sg = create_security_groups(vpc)

# Criação de Recursos Diversos
create_s3_buckets()
public_alb, public_target_group, public_http_listener_arn, public_https_listener_arn, \
private_alb, private_target_group, private_http_listener_arn, private_https_listener_arn = create_albs(
    vpc, public_subnets, private_subnets, alb_sg
)
ecs_cluster = create_ecs_cluster()
task_role = create_task_role()
asg, capacity_provider = create_ecs_instance_resources(vpc, private_subnets, ecs_sg, ecs_cluster)
rds_instance, rds_sg = create_rds_instance(vpc, private_subnets, ecs_sg)
msk_cluster, msk_sg = create_msk_cluster(vpc, private_subnets)
mq_broker, mq_sg = create_amazon_mq(vpc, private_subnets)
sqs_queue, kms_key = create_sqs_queue()
create_ecr_repositories()

# Exportações dos Recursos Criados
pulumi.export("vpc_id", vpc.id)
pulumi.export("internet_gateway_id", internet_gateway.id)
pulumi.export("public_alb_dns_name", public_alb.dns_name)
pulumi.export("private_alb_dns_name", private_alb.dns_name)
pulumi.export("ecs_cluster_id", ecs_cluster.id)
pulumi.export("capacity_provider_name", capacity_provider.name)
pulumi.export("task_role_arn", task_role.arn)
pulumi.export("rds_instance_id", rds_instance.id)
pulumi.export("rds_endpoint", rds_instance.endpoint)
pulumi.export("rds_security_group_id", rds_sg.id)
pulumi.export("msk_cluster_arn", msk_cluster.arn)
pulumi.export("msk_security_group_id", msk_sg.id)
pulumi.export("mq_broker_arn", mq_broker.arn)
pulumi.export("mq_security_group_id", mq_sg.id)
pulumi.export("sqs_queue_name", sqs_queue.name)
pulumi.export("sqs_queue_arn", sqs_queue.arn)
pulumi.export("kms_key_arn", kms_key.arn)
