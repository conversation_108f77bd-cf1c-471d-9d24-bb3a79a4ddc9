import pulumi
import pulumi_aws as aws

def create_security_groups(vpc):
    alb_sg = aws.ec2.SecurityGroup(
        "magie-prod-alb-sg",
        vpc_id=vpc.id,
        description="Security group for ALB",
        ingress=[
            aws.ec2.SecurityGroupIngressArgs(
                protocol="tcp",
                from_port=80,
                to_port=80,
                cidr_blocks=["0.0.0.0/0"]
            ),
            aws.ec2.SecurityGroupIngressArgs(
                protocol="tcp",
                from_port=443,
                to_port=443,
                cidr_blocks=["0.0.0.0/0"]
            ),
        ],
        egress=[
            aws.ec2.SecurityGroupEgressArgs(
                protocol="-1",
                from_port=0,
                to_port=0,
                cidr_blocks=["0.0.0.0/0"]
            ),
        ],
        tags={"Name": "magie-prod-alb-sg"}
    )

    ecs_sg = aws.ec2.SecurityGroup(
        "magie-prod-ecs-sg",
        vpc_id=vpc.id,
        description="Security group for ECS instances",
        ingress=[],
        egress=[
            aws.ec2.SecurityGroupEgressArgs(
                protocol="-1",
                from_port=0,
                to_port=0,
                cidr_blocks=["0.0.0.0/0"]
            ),
        ],
        tags={"Name": "magie-prod-ecs-sg"}
    )

    aws.ec2.SecurityGroupRule(
        "ecs-loopback",
        security_group_id=ecs_sg.id,
        type="ingress",
        protocol="-1",
        from_port=0,
        to_port=0,
        source_security_group_id=ecs_sg.id
    )

    aws.ec2.SecurityGroupRule(
        "alb-to-ecs",
        security_group_id=ecs_sg.id,
        type="ingress",
        protocol="-1",
        from_port=0,
        to_port=0,
        source_security_group_id=alb_sg.id
    )

    datadog_sg = aws.ec2.SecurityGroup(
        "datadog-agent-sg",
        vpc_id=vpc.id,
        description="Security group for Datadog Agent",
        ingress=[
            aws.ec2.SecurityGroupIngressArgs(
                protocol="udp",
                from_port=8125,
                to_port=8125,
                cidr_blocks=["0.0.0.0/0"]
            ),
            aws.ec2.SecurityGroupIngressArgs(
                protocol="tcp",
                from_port=8126,
                to_port=8126,
                cidr_blocks=["0.0.0.0/0"]
            ),
        ],
        egress=[
            aws.ec2.SecurityGroupEgressArgs(
                protocol="-1",
                from_port=0,
                to_port=0,
                cidr_blocks=["0.0.0.0/0"]
            ),
        ],
        tags={"Name": "datadog-agent-sg"}
    )

    aws.ec2.SecurityGroupRule(
        "datadog-loopback",
        security_group_id=datadog_sg.id,
        type="ingress",
        protocol="-1",
        from_port=0,
        to_port=0,
        source_security_group_id=datadog_sg.id
    )

    nlb_sg = aws.ec2.SecurityGroup(
        "magie-prod-nlb-sg",
        vpc_id=vpc.id,
        description="Security group for NLB",
        ingress=[
            aws.ec2.SecurityGroupIngressArgs(
                protocol="tcp",
                from_port=8126,
                to_port=8126,
                cidr_blocks=["0.0.0.0/0"]
            ),
            aws.ec2.SecurityGroupIngressArgs(
                protocol="udp",
                from_port=8125,
                to_port=8125,
                cidr_blocks=["0.0.0.0/0"]
            ),
        ],
        egress=[
            aws.ec2.SecurityGroupEgressArgs(
                protocol="-1",
                from_port=0,
                to_port=0,
                cidr_blocks=["0.0.0.0/0"]
            ),
        ],
        tags={"Name": "magie-prod-nlb-sg"}
    )

    aws.ec2.SecurityGroupRule(
        "nlb-loopback",
        security_group_id=nlb_sg.id,
        type="ingress",
        protocol="-1",
        from_port=0,
        to_port=0,
        source_security_group_id=nlb_sg.id
    )

    return alb_sg, ecs_sg, datadog_sg, nlb_sg
