# Pulumi Infraestrutura como Código (IaC) para Implantação do Ambiente de prod Magie

Este repositório contém o código Pulumi para implantar toda a infraestrutura necessária para o ambiente de prod Magie na AWS. A implantação inclui clusters ECS, instâncias RDS, clusters MSK, buckets S3, ALB, grupos de segurança e recursos associados.

## Pré-requisitos

- **Pulumi**: Certifique-se de que o Pulumi esteja instalado. Você pode baixá-lo do site oficial do Pulumi.
- **AWS CLI**: Configure suas credenciais AWS usando o AWS CLI. Você pode instalá-lo na página oficial do AWS CLI.
- **Python e ambiente virtual**: Certifique-se de que o Python está instalado (preferencialmente 3.7 ou superior). Use um ambiente virtual para gerenciamento de dependências.

## Configuração

### Clonar o Repositório:

```sh
git clone <your-repository-url>
cd your-repository-directory
```

### Configurar Ambiente Virtual:

```sh
python3 -m venv venv
source venv/bin/activate   # No Windows use `venv\Scripts\activate`
```

### Instalar Pacotes Necessários:

```sh
pip install -r requirements.txt
```

### Configurar Credenciais AWS: 

Certifique-se de que suas credenciais AWS estão configuradas corretamente no seu sistema:

```sh
aws configure
```

## Implantar a Infraestrutura

### Inicializar Pulumi: 

Selecione ou crie um novo stack:

```sh
pulumi stack init magie-prod
```

### Visualizar Mudanças: 

Execute o comando a seguir para visualizar as mudanças e garantir que tudo está configurado corretamente:

```sh
pulumi preview
```

### Implantar: 

Aplique a implantação para criar ou atualizar os recursos:

```sh
pulumi up
```

### Destruir: 

Se precisar desmontar a infraestrutura, execute:

```sh
pulumi destroy
```

## Visão Geral da Estrutura

O repositório contém os seguintes arquivos:

- `__main__.py`: Ponto de entrada principal para o Pulumi, orquestrando a implantação.
- `vpc`: Contém scripts para criar VPC, sub-redes e grupos de segurança.
- `ecs`: Scripts para implantar o cluster ECS, serviços, definições de tarefas e roles.
- `alb`: Script para criar o Application Load Balancer e listeners.
- `rds`: Script para implantar a instância RDS.
- `msk`: Script para implantar o MSK (Managed Streaming for Apache Kafka).
- `amazon_mq`: Script para implantar o RabbitMQ da Amazon (Amazon MQ).
- `s3`: Scripts para criar os buckets S3 necessários.
- `requirements.txt`: Dependências Python necessárias para o projeto.