import pulumi_aws as aws

def create_s3_buckets():
    bucket_names = [
        "magie-site-storage-prod",
        "magie-retool-medias-prod",
        "magie-rds-backups-prod",
        "magie-medias-prod",
        "magie-receipts-prod",
        "magie-assets-prod"
    ]

    buckets = []
    for name in bucket_names:
        bucket = aws.s3.Bucket(name,
            bucket=name,
            acl="private",
            tags={"Environment": "prod"}
        )
        buckets.append(bucket)

    return buckets
