import pulumi
import pulumi_aws as aws

ECR_REPOSITORIES = [
    "account",
    "auth",
    "backoffice",
    "banking",
    "bff",
    "chatbot",
    "data-processor",
    "datadog-agent",
    "gateway",
    "kafka-ui",
    "llm",
    "proder",
    "media-interpreter-service",
    "media",
    "mgm",
    "mockserver",
    "notification",
    "onboarding",
    "openfinance",
    "reminder",
    "scheduler",
    "unleash",
    "userfeatures",
    "waitinglist",
    "webhook",
    "workflow"
]

def create_ecr_repositories():
    """
    Cria repositórios ECR com base na lista definida e configura como imutáveis e protegidos contra destruição.
    """
    ecr_repositories = []

    for repo_name in ECR_REPOSITORIES:
        ecr = aws.ecr.Repository(
            resource_name=f"{repo_name}-ecr",
            name=f"{repo_name}",
            image_scanning_configuration=aws.ecr.RepositoryImageScanningConfigurationArgs(
                scan_on_push=True
            ),
            image_tag_mutability="IMMUTABLE",  # Define o repositório como imutável
            tags={"Name": f"{repo_name}-ecr"},
            opts=pulumi.ResourceOptions(protect=True)  # Protege contra destruição
        )
        pulumi.export(f"{repo_name}_ecr_uri", ecr.repository_url)
        ecr_repositories.append(ecr)

    return ecr_repositories
