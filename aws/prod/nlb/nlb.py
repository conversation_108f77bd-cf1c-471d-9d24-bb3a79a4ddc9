import pulumi
import pulumi_aws as aws

def create_shared_nlb(vpc, private_subnets, nlb_sg):
    datadog_nlb = aws.lb.LoadBalancer(
        "datadog-nlb",
        internal=True,
        security_groups=[nlb_sg.id],
        subnets=[subnet.id for subnet in private_subnets],
        load_balancer_type="network",
        enable_deletion_protection=False,
        tags={"Name": "datadog-nlb"}
    )

    datadog_target_group_8126 = aws.lb.TargetGroup(
        "datadog-target-group-8126",
        name="dd-tg-8126",
        port=8126,
        protocol="TCP",
        target_type="ip",
        vpc_id=vpc.id,
        health_check=aws.lb.TargetGroupHealthCheckArgs(
            protocol="TCP",
            interval=30,
            healthy_threshold=3,
            unhealthy_threshold=2,
        ),
        tags={"Name": "datadog-tg-8126"}
    )

    datadog_target_group_8125 = aws.lb.TargetGroup(
        "datadog-target-group-8125",
        name="dd-tg-8125",
        port=8125,
        protocol="UDP",
        target_type="ip",
        vpc_id=vpc.id,
        tags={"Name": "datadog-tg-8125"}
    )

    datadog_listener_8126 = aws.lb.Listener(
        "datadog-listener-8126",
        load_balancer_arn=datadog_nlb.arn,
        port=8126,
        protocol="TCP",
        default_actions=[
            aws.lb.ListenerDefaultActionArgs(
                type="forward",
                target_group_arn=datadog_target_group_8126.arn
            )
        ]
    )

    return datadog_nlb, datadog_target_group_8126, datadog_target_group_8125



