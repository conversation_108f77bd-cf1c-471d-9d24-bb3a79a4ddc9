import pulumi
import pulumi_aws as aws

def create_msk_cluster(vpc, private_subnets):
    # Criação do Security Group para o MSK
    msk_sg = aws.ec2.SecurityGroup(
        "magie-loadtest-msk-sg",
        vpc_id=vpc.id,
        description="Security group for MSK",
        ingress=[
            aws.ec2.SecurityGroupIngressArgs(
                protocol="tcp",
                from_port=9092,
                to_port=9092,
                cidr_blocks=["0.0.0.0/0"]  # Acesso público ao MSK
            )
        ],
        egress=[
            aws.ec2.SecurityGroupEgressArgs(
                protocol="-1",
                from_port=0,
                to_port=0,
                cidr_blocks=["0.0.0.0/0"]  # Saída irrestrita
            )
        ],
        tags={"Name": "magie-loadtest-msk-sg"}
    )

    # Seleciona duas sub-redes privadas
    selected_subnets = [private_subnets[0].id, private_subnets[1].id]

    # Criação do cluster MSK
    msk_cluster = aws.msk.Cluster(
        "magie-loadtest-msk-cluster",
        cluster_name="magie-loadtest-msk",
        kafka_version="3.6.0",
        number_of_broker_nodes=2,
        broker_node_group_info=aws.msk.ClusterBrokerNodeGroupInfoArgs(
            instance_type="kafka.m5.large",
            client_subnets=selected_subnets,
            security_groups=[msk_sg.id]
        ),
        encryption_info=aws.msk.ClusterEncryptionInfoArgs(
            encryption_in_transit=aws.msk.ClusterEncryptionInfoEncryptionInTransitArgs(
                client_broker="PLAINTEXT",  # Comunicação em texto simples
                in_cluster=True
            )
        ),
        tags={"Name": "magie-loadtest-msk"}
    )

    # Configurar entradas no Route 53 para os brokers
    zone_id = "Z08230811FSXHJZGW5D7E"
    domain_name = "loadtest.magie.services"

    def create_broker_records(brokers):
        records = []
        for i, broker in enumerate(brokers):
            # Remover a porta do broker
            broker_address = broker.split(":")[0]
            broker_name = f"msk-{i + 1}"  # Apenas subdomínio (msk-1, msk-2)
            record = aws.route53.Record(
                f"msk-broker-{i + 1}-record",
                zone_id=zone_id,
                name=broker_name,  # Apenas o subdomínio
                type="CNAME",
                ttl=300,
                records=[broker_address]
            )
            records.append(record)
        return records

    # Processar brokers e criar registros
    def process_brokers(brokers):
        broker_list = brokers.split(",")
        create_broker_records(broker_list)

    msk_cluster.bootstrap_brokers.apply(process_brokers)

    # Exportar informações do MSK
    pulumi.export("msk_cluster_arn", msk_cluster.arn)
    pulumi.export("msk_security_group_id", msk_sg.id)

    return msk_cluster, msk_sg
