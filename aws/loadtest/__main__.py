import pulumi
import pulumi_aws as aws
from vpc.vpc import create_vpc
from vpc.subnets import create_subnets
from vpc.security_groups import create_security_groups
from ecs.cluster import create_ecs_cluster, create_ecs_instance_resources
from alb.alb import create_alb
from ecs.task_execution_role import create_task_execution_role
from ecs.task_role import create_task_role
from s3.buckets import create_s3_buckets
from rds.rds import create_rds_instance, create_bastion_host
from msk.msk import create_msk_cluster
from amazon_mq.amazon_mq import create_amazon_mq
from ecr.ecr import create_ecr_repositories
from sqs.sqs import create_sqs_queue  # Import do SQS

# Criar VPC e Internet Gateway
vpc, internet_gateway = create_vpc()

# Criar sub-redes públicas e privadas
public_subnets, private_subnets = create_subnets(vpc, internet_gateway)

# Criar grupos de segurança para ALB, ECS e Bastion
security_groups = create_security_groups(vpc)
alb_sg = security_groups[0]
ecs_sg = security_groups[1]

# Criar buckets S3
create_s3_buckets()

# Criar Application Load Balancer, Target Group padrão e Listeners HTTP/HTTPS
alb, target_group, http_listener_arn, https_listener_arn = create_alb(vpc, public_subnets, alb_sg)

# Criar Cluster ECS
ecs_cluster = create_ecs_cluster()

# Criar funções de execução e tarefa para ECS
execution_role = create_task_execution_role()
task_role = create_task_role()

# Criar recursos de instância ECS (Auto Scaling Group e Capacity Provider)
asg, capacity_provider = create_ecs_instance_resources(vpc, private_subnets, ecs_sg, ecs_cluster)

# Criar Bastion Host vinculado ao Security Group
bastion_instance, bastion_sg = create_bastion_host(vpc, public_subnets)

# Criar instância RDS e seu Security Group, incluindo o bastion_sg
rds_instance, rds_sg = create_rds_instance(vpc, private_subnets, ecs_sg, bastion_sg)

# Criar cluster MSK
msk_cluster, msk_sg = create_msk_cluster(vpc, private_subnets)

# Criar Amazon MQ
mq_broker, mq_sg = create_amazon_mq(vpc, private_subnets)

# Criar fila SQS com chave KMS
sqs_queue, kms_key = create_sqs_queue()

# Criar repositórios ECR
create_ecr_repositories()

# Exportar os recursos criados
pulumi.export("vpc_id", vpc.id)
pulumi.export("alb_dns_name", alb.dns_name)
pulumi.export("ecs_cluster_id", ecs_cluster.id)
pulumi.export("capacity_provider_name", capacity_provider.name)
pulumi.export("execution_role_arn", execution_role.arn)
pulumi.export("task_role_arn", task_role.arn)
pulumi.export("rds_instance_id", rds_instance.id)
pulumi.export("rds_endpoint", rds_instance.endpoint)
pulumi.export("rds_security_group_id", rds_sg.id)
pulumi.export("bastion_public_ip", bastion_instance.public_ip)
pulumi.export("bastion_security_group_id", bastion_sg.id)
pulumi.export("msk_cluster_arn", msk_cluster.arn)
pulumi.export("msk_security_group_id", msk_sg.id)
pulumi.export("mq_broker_arn", mq_broker.arn)
pulumi.export("mq_security_group_id", mq_sg.id)
pulumi.export("sqs_queue_name", sqs_queue.name)
pulumi.export("sqs_queue_arn", sqs_queue.arn)
pulumi.export("kms_key_arn", kms_key.arn)
