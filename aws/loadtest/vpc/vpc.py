import pulumi
import pulumi_aws as aws

def create_vpc():
    vpc = aws.ec2.Vpc("magie-loadtest-vpc",
                      cidr_block="********/16",
                      enable_dns_hostnames=True,
                      enable_dns_support=True,
                      tags={"Name": "magie-loadtest-vpc"})

    internet_gateway = aws.ec2.InternetGateway("main-igw",
                                               vpc_id=vpc.id,
                                               tags={"Name": "main-igw"})

    pulumi.export("vpc_id", vpc.id)
    pulumi.export("internet_gateway_id", internet_gateway.id)

    return vpc, internet_gateway
