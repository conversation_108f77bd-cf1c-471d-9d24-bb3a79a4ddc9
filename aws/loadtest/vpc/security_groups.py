import pulumi
import pulumi_aws as aws

def create_security_groups(vpc):
    # Security Group for ALB
    alb_sg = aws.ec2.SecurityGroup(
        "magie-loadtest-alb-sg",
        vpc_id=vpc.id,
        description="Security group for ALB",
        ingress=[
            aws.ec2.SecurityGroupIngressArgs(
                protocol="tcp",
                from_port=80,
                to_port=80,
                cidr_blocks=["0.0.0.0/0"]  # HTTP de qualquer lugar
            ),
            aws.ec2.SecurityGroupIngressArgs(
                protocol="tcp",
                from_port=443,
                to_port=443,
                cidr_blocks=["0.0.0.0/0"]  # HTTPS de qualquer lugar
            ),
        ],
        egress=[
            aws.ec2.SecurityGroupEgressArgs(
                protocol="-1",
                from_port=0,
                to_port=0,
                cidr_blocks=["0.0.0.0/0"]  # Saída irrestrita
            ),
        ],
        tags={"Name": "magie-loadtest-alb-sg"}
    )

    # Security Group for ECS instances
    ecs_sg = aws.ec2.SecurityGroup(
        "magie-loadtest-ecs-sg",
        vpc_id=vpc.id,
        description="Security group for ECS instances",
        ingress=[
            # Permitir tráfego total do ALB para ECS
            aws.ec2.SecurityGroupIngressArgs(
                protocol="-1",
                from_port=0,
                to_port=0,
                security_groups=[alb_sg.id]  # Acesso do ALB
            ),
        ],
        egress=[
            # Permitir saída irrestrita
            aws.ec2.SecurityGroupEgressArgs(
                protocol="-1",
                from_port=0,
                to_port=0,
                cidr_blocks=["0.0.0.0/0"]
            ),
        ],
        tags={"Name": "magie-loadtest-ecs-sg"}
    )

    # Regras dinâmicas para garantir o tráfego entre ALB e ECS
    aws.ec2.SecurityGroupRule(
        "alb-allow-ecs",
        security_group_id=alb_sg.id,
        type="ingress",
        protocol="-1",
        from_port=0,
        to_port=0,
        source_security_group_id=ecs_sg.id
    )

    # Regras de loopback para ALB
    aws.ec2.SecurityGroupRule(
        "alb-loopback",
        security_group_id=alb_sg.id,
        type="ingress",
        protocol="-1",
        from_port=0,
        to_port=0,
        source_security_group_id=alb_sg.id
    )

    # Regras de loopback para ECS
    aws.ec2.SecurityGroupRule(
        "ecs-loopback",
        security_group_id=ecs_sg.id,
        type="ingress",
        protocol="-1",
        from_port=0,
        to_port=0,
        source_security_group_id=ecs_sg.id
    )

    return alb_sg, ecs_sg