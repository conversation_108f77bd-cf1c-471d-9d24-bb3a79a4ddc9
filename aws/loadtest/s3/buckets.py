import pulumi_aws as aws

def create_s3_buckets():
    bucket_names = [
        "magie-site-storage-loadtest",
        "magie-retool-medias-loadtest",
        "magie-rds-backups-loadtest",
        "magie-medias-loadtest",
        "magie-receipts-loadtest",
        "magie-assets-loadtest"
    ]

    buckets = []
    for name in bucket_names:
        bucket = aws.s3.Bucket(name,
            bucket=name,
            acl="private",
            tags={"Environment": "loadtest"}
        )
        buckets.append(bucket)

    return buckets
