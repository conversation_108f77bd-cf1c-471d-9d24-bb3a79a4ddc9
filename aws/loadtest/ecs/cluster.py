import pulumi
import pulumi_aws as aws
import base64

def create_ecs_cluster():
    ecs_cluster = aws.ecs.Cluster("magie-loadtest-cluster", name="magie-loadtest-cluster")
    pulumi.export('ecs_cluster_id', ecs_cluster.id)
    return ecs_cluster

def create_ecs_instance_resources(vpc, private_subnets, ecs_sg, ecs_cluster):
    user_data = """#!/bin/bash
echo ECS_CLUSTER=magie-loadtest-cluster >> /etc/ecs/ecs.config;

if ! command -v amazon-ssm-agent &> /dev/null; then
  yum install -y amazon-ssm-agent
  systemctl enable amazon-ssm-agent
  systemctl start amazon-ssm-agent
fi
"""
    user_data_base64 = base64.b64encode(user_data.encode('utf-8')).decode('utf-8')

    # IAM Role para instâncias ECS
    ecs_instance_role = aws.iam.Role("magie-loadtest-ecs-instance-role", assume_role_policy="""{
        "Version": "2012-10-17",
        "Statement": [
            {
                "Effect": "Allow",
                "Principal": {
                    "Service": "ec2.amazonaws.com"
                },
                "Action": "sts:AssumeRole"
            }
        ]
    }""")

    # Políticas do IAM Role
    aws.iam.RolePolicyAttachment("ecs-instance-role-policy", role=ecs_instance_role.name,
                                 policy_arn="arn:aws:iam::aws:policy/service-role/AmazonEC2ContainerServiceforEC2Role")
    aws.iam.RolePolicyAttachment("ecs-instance-full-access-policy", role=ecs_instance_role.name,
                                 policy_arn="arn:aws:iam::aws:policy/AmazonEC2FullAccess")

    ecs_instance_profile = aws.iam.InstanceProfile("magie-loadtest-ecs-instance-profile", role=ecs_instance_role.name)

    # Verificar se há sub-redes disponíveis
    if not private_subnets:
        raise ValueError("Nenhuma sub-rede privada disponível na região us-east-1.")

    # Launch Template para instâncias ECS
    launch_template = aws.ec2.LaunchTemplate("magie-loadtest-ecs-launch-template",
                                             name_prefix="magie-loadtest-ecs-",
                                             image_id="ami-09a95bb11a08df1bd",  # Certifique-se de que esta AMI é compatível com o ECS
                                             instance_type="m7i.xlarge",
                                             user_data=user_data_base64,
                                             iam_instance_profile=aws.ec2.LaunchTemplateIamInstanceProfileArgs(
                                                 arn=ecs_instance_profile.arn
                                             ),
                                             vpc_security_group_ids=[ecs_sg.id])

    # Auto Scaling Group
    asg = aws.autoscaling.Group("magie-loadtest-ecs-autoscaling-group",
                                vpc_zone_identifiers=[subnet.id for subnet in private_subnets],
                                launch_template=aws.autoscaling.GroupLaunchTemplateArgs(
                                    id=launch_template.id,
                                    version="$Latest",
                                ),
                                min_size=0,
                                max_size=3,
                                desired_capacity=2,
                                health_check_type="EC2",
                                health_check_grace_period=120,
                                termination_policies=["Default"],
                                tags=[aws.autoscaling.GroupTagArgs(
                                    key="Name",
                                    value="magie-loadtest-ecs-instance",
                                    propagate_at_launch=True
                                )])

    # Provedor de Capacidade ECS
    capacity_provider = aws.ecs.CapacityProvider("magie-loadtest-capacity-provider",
        auto_scaling_group_provider=aws.ecs.CapacityProviderAutoScalingGroupProviderArgs(
            auto_scaling_group_arn=asg.arn,
            managed_scaling=aws.ecs.CapacityProviderAutoScalingGroupProviderManagedScalingArgs(
                status="ENABLED",
                target_capacity=100,
                minimum_scaling_step_size=1,
                maximum_scaling_step_size=10,
                instance_warmup_period=300
            ),
            managed_termination_protection="DISABLED"
        ),
        opts=pulumi.ResourceOptions(depends_on=[asg])  # Garante que o ASG seja criado antes do Capacity Provider
    )

    # Adicionar Provedor de Capacidade ao Cluster ECS
    cluster_capacity_providers = aws.ecs.ClusterCapacityProviders("magie-loadtest-cluster-capacity-provider",
        cluster_name=ecs_cluster.name,
        capacity_providers=[capacity_provider.name],
        default_capacity_provider_strategies=[aws.ecs.ClusterCapacityProvidersDefaultCapacityProviderStrategyArgs(
            capacity_provider=capacity_provider.name,
            weight=1,
            base=0
        )],
        opts=pulumi.ResourceOptions(depends_on=[capacity_provider])  # Garante a ordem correta
    )

    # Exportar recursos
    pulumi.export('ecs_cluster_id', ecs_cluster.id)
    pulumi.export('capacity_provider_name', capacity_provider.name)

    return asg, capacity_provider
