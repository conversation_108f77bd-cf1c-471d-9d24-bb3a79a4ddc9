import pulumi
import pulumi_aws as aws
import json

def create_task_definition(execution_role_arn):
    task_definition = aws.ecs.TaskDefinition("magie-loadtest-task",
                                             family="magie-loadtest-task",
                                             cpu="256",
                                             memory="512",
                                             network_mode="bridge",
                                             requires_compatibilities=["EC2"],
                                             execution_role_arn=execution_role_arn,
                                             container_definitions=pulumi.Output.all(execution_role_arn).apply(lambda execution_role_arn: json.dumps([{
                                                 "name": "my-container",
                                                 "image": "nginx",
                                                 "essential": True,
                                                 "memory": 512,
                                                 "cpu": 256,
                                                 "portMappings": [{
                                                     "containerPort": 80,
                                                     "hostPort": 80,
                                                     "protocol": "tcp"
                                                 }]
                                             }])))

    return task_definition
