import pulumi_aws as aws

def create_task_execution_role():
    # Cria a Task Execution Role
    role = aws.iam.Role("magie-loadtest-task-execution-role",
        assume_role_policy="""{
            "Version": "2012-10-17",
            "Statement": [
                {
                    "Effect": "Allow",
                    "Principal": {
                        "Service": "ecs-tasks.amazonaws.com"
                    },
                    "Action": "sts:AssumeRole"
                }
            ]
        }"""
    )

    # Define a policy que inclui todas as permissões necessárias
    policy = aws.iam.Policy("magie-loadtest-task-execution-policy",
        policy="""{
            "Version": "2012-10-17",
            "Statement": [
                {
                    "Effect": "Allow",
                    "Action": [
                        "ec2:*",
                        "ecs:*",
                        "ecr:*",
                        "logs:*",
                        "s3:*",
                        "ssm:*",
                        "route53:*",
                        "secretsmanager:*"
                    ],
                    "Resource": "*"
                }
            ]
        }"""
    )

    # Anexa a policy à Task Execution Role
    aws.iam.RolePolicyAttachment("magie-loadtest-task-execution-policy-attachment",
        policy_arn=policy.arn,
        role=role.name
    )

    return role
