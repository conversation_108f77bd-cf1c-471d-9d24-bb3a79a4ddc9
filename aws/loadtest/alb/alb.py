import pulumi
import pulumi_aws as aws

def create_alb(vpc, public_subnets, alb_sg):
    # Cria o Load Balancer
    alb = aws.lb.LoadBalancer("app-lb",
                              internal=False,
                              security_groups=[alb_sg.id],
                              subnets=[subnet.id for subnet in public_subnets],
                              load_balancer_type="application",
                              enable_deletion_protection=False,
                              idle_timeout=400,
                              tags={"Name": "app-lb"})

    # Cria o Target Group
    target_group = aws.lb.TargetGroup("app-tg",
                                      port=80,
                                      protocol="HTTP",
                                      target_type="instance",
                                      vpc_id=vpc.id,
                                      health_check=aws.lb.TargetGroupHealthCheckArgs(
                                          protocol="HTTP",
                                          path="/",
                                          interval=30,
                                          timeout=5,
                                          healthy_threshold=5,
                                          unhealthy_threshold=2
                                      ),
                                      tags={"Name": "app-tg"})

    # Cria o Listener HTTP que redireciona para HTTPS
    http_listener = aws.lb.Listener("http-listener",
                                    load_balancer_arn=alb.arn,
                                    port=80,
                                    default_actions=[aws.lb.ListenerDefaultActionArgs(
                                        type="redirect",
                                        redirect=aws.lb.ListenerDefaultActionRedirectArgs(
                                            protocol="HTTPS",
                                            port="443",
                                            status_code="HTTP_301"
                                        )
                                    )])

    # Cria o Listener HTTPS com resposta fixa 503 para requisições sem regra
    https_listener = aws.lb.Listener("https-listener",
                                     load_balancer_arn=alb.arn,
                                     port=443,
                                     protocol="HTTPS",
                                     ssl_policy="ELBSecurityPolicy-TLS13-1-2-2021-06",
                                     certificate_arn="arn:aws:acm:us-east-1:650251699819:certificate/d5e44eeb-8a1f-43bf-bafa-5d3a2ffc94e7",
                                     default_actions=[aws.lb.ListenerDefaultActionArgs(
                                         type="fixed-response",
                                         fixed_response=aws.lb.ListenerDefaultActionFixedResponseArgs(
                                             content_type="text/plain",
                                             message_body="Service Unavailable",
                                             status_code="503"
                                         )
                                     )])

    pulumi.export("alb_dns_name", alb.dns_name)

    # Retorna o ALB, Target Group e os ARNs dos Listeners HTTP e HTTPS
    return alb, target_group, http_listener.arn, https_listener.arn
