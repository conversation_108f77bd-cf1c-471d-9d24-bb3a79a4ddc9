import pulumi
import pulumi_aws as aws

def create_rds_instance(vpc, private_subnets, ecs_sg):
    # Cria o Security Group para o RDS
    rds_sg = aws.ec2.SecurityGroup("magie-staging-rds-sg",
                                   vpc_id=vpc.id,
                                   description="Security group for RDS",
                                   ingress=[
                                       aws.ec2.SecurityGroupIngressArgs(
                                           protocol="tcp",
                                           from_port=5432,
                                           to_port=5432,
                                           security_groups=[ecs_sg.id]  # Permitir acesso do ECS SG
                                       )
                                   ],
                                   egress=[
                                       aws.ec2.SecurityGroupEgressArgs(
                                           protocol="-1",
                                           from_port=0,
                                           to_port=0,
                                           cidr_blocks=["0.0.0.0/0"]  # Saída irrestrita
                                       )
                                   ],
                                   tags={"Name": "magie-staging-rds-sg"})

    # Cria o Subnet Group para o RDS
    rds_subnet_group = aws.rds.SubnetGroup("magie-staging-rds-subnet-group",
                                           subnet_ids=[subnet.id for subnet in private_subnets],
                                           tags={"Name": "magie-staging-rds-subnet-group"})

    # Cria um Parameter Group para o RDS PostgreSQL
    rds_parameter_group = aws.rds.ParameterGroup("magie-staging-rds-parameter-group",
                                                 family="postgres15",  # Família corrigida para Postgres 15
                                                 parameters=[
                                                     {
                                                         "name": "rds.force_ssl",
                                                         "value": "0"  # Desativa SSL
                                                     }
                                                 ],
                                                 tags={"Name": "magie-staging-rds-parameter-group"})

    # Cria o RDS PostgreSQL
    db_instance = aws.rds.Instance("magie-staging-rds",
                                   engine="postgres",
                                   engine_version="15.7",
                                   instance_class="db.t4g.large",
                                   allocated_storage=100,
                                   storage_type="gp3",
                                   db_subnet_group_name=rds_subnet_group.name,
                                   vpc_security_group_ids=[rds_sg.id],
                                   parameter_group_name=rds_parameter_group.name,  # Associa o Parameter Group
                                   multi_az=False,
                                   publicly_accessible=False,
                                   backup_retention_period=7,
                                   skip_final_snapshot=True,
                                   deletion_protection=False,
                                   storage_encrypted=True,
                                   username="postgres_user",
                                   password="7aY7F2FERJ3YF87V8PHX",
                                   tags={"Name": "magie-staging-rds"},
                                   iam_database_authentication_enabled=True)

    # Extrai apenas o domínio do endpoint (removendo a porta, se presente)
    rds_endpoint_domain = db_instance.endpoint.apply(lambda endpoint: endpoint.split(":")[0] if ":" in endpoint else endpoint)

    # Configura o registro no Route 53 para o RDS
    zone_id = "Z04171101GOKWJGXBHBL1"

    rds_record = aws.route53.Record("magie-staging-rds-record",
                                    zone_id=zone_id,
                                    name="db-01.staging.magie.services",
                                    type="CNAME",
                                    ttl=300,
                                    records=[rds_endpoint_domain])

    # Exportar informações do RDS
    pulumi.export("rds_instance_id", db_instance.id)
    pulumi.export("rds_endpoint", db_instance.endpoint)
    pulumi.export("rds_security_group_id", rds_sg.id)
    pulumi.export("rds_route53_record", rds_record.fqdn)

    return db_instance, rds_sg
