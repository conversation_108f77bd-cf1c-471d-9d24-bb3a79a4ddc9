import pulumi
import pulumi_aws as aws

def create_msk_cluster(vpc, private_subnets):
    msk_sg = aws.ec2.SecurityGroup(
        "magie-staging-msk-sg",
        vpc_id=vpc.id,
        description="Security group for MSK",
        ingress=[
            aws.ec2.SecurityGroupIngressArgs(
                protocol="tcp",
                from_port=9092,
                to_port=9092,
                cidr_blocks=["0.0.0.0/0"]
            )
        ],
        egress=[
            aws.ec2.SecurityGroupEgressArgs(
                protocol="-1",
                from_port=0,
                to_port=0,
                cidr_blocks=["0.0.0.0/0"]
            )
        ],
        tags={"Name": "magie-staging-msk-sg"}
    )

    selected_subnets = [private_subnets[0].id, private_subnets[1].id, private_subnets[2].id]

    msk_cluster = aws.msk.Cluster(
        "magie-staging-msk-cluster",
        cluster_name="magie-staging-msk",
        kafka_version="3.6.0",
        number_of_broker_nodes=3,
        broker_node_group_info=aws.msk.ClusterBrokerNodeGroupInfoArgs(
            instance_type="kafka.m5.large",
            client_subnets=selected_subnets,
            security_groups=[msk_sg.id]
        ),
        encryption_info=aws.msk.ClusterEncryptionInfoArgs(
            encryption_in_transit=aws.msk.ClusterEncryptionInfoEncryptionInTransitArgs(
                client_broker="PLAINTEXT",
                in_cluster=True
            )
        ),
        tags={"Name": "magie-staging-msk"}
    )

    zone_id = "Z04171101GOKWJGXBHBL1"
    domain_name = "staging.magie.services"

    # Função para criar registros DNS no Route 53
    def create_broker_records(brokers):
        records = []
        for i, broker in enumerate(brokers):
            broker_address = broker.split(":")[0]
            broker_name = f"msk-{i + 1}.staging.{domain_name}"
            record = aws.route53.Record(
                f"msk-broker-{i + 1}-record",
                zone_id=zone_id,
                name=broker_name,
                type="CNAME",
                ttl=300,
                records=[broker_address]
            )
            records.append(record)
        return records

    # Função para processar brokers e criar os registros
    def process_brokers(brokers):
        if brokers:
            broker_list = brokers.split(",")
            return create_broker_records(broker_list)
        else:
            pulumi.log.warn("Nenhum broker encontrado para criar registros DNS.")
            return []

    # Garantir a criação dos registros DNS
    brokers_creation = msk_cluster.bootstrap_brokers.apply(lambda brokers: process_brokers(brokers))

    pulumi.export("msk_cluster_arn", msk_cluster.arn)
    pulumi.export("msk_security_group_id", msk_sg.id)
    pulumi.export("msk_brokers_dns_creation", brokers_creation)

    return msk_cluster, msk_sg
