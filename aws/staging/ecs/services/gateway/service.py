import pulumi
import pulumi_aws as aws
import json

# Definição das chaves de segredo
secret_keys = [
    "spring.profiles.active", "ACCOUNTING_BASE_URI", "BANKING_BASE_URI",
    "BFF_BASE_URI", "BACKOFFICE_BASE_URI", "MEDIA_BASE_URI", "AUTH_BASE_URI",
    "WEBHOOK_BASE_URI", "ONBOARDING_BASE_URI", "OPENFINANCE_BASE_URI",
    "MGM_BASE_URI", "OTEL_COLLECTOR_ENDPOINT", "CELCOIN_BASIC_USERNAME",
    "CELCOIN_BASIC_PASSWORD", "RETOOL_API_KEY", "UNICO_API_KEY",
    "JAVA_TOOL_OPTIONS", "JAVA_OPTS", "INICIADOR_HMAC"
]

# Função para definir a tarefa ECS
def create_task_definition(execution_role_arn, task_role_arn, ecr_repository_uri, secret_arn, log_group_name):
    container_definitions = pulumi.Output.all(ecr_repository_uri, secret_arn, log_group_name).apply(
        lambda args: json.dumps([{
            "name": "gateway-container",
            "image": args[0],
            "essential": True,
            "memoryReservation": 1024,
            "memory": 1024,
            "portMappings": [{"containerPort": 8080, "protocol": "tcp"}],
            "secrets": [
                {"name": key, "valueFrom": f"{args[1]}:{key}::"}
                for key in secret_keys
            ],
            "logConfiguration": {
                "logDriver": "awslogs",
                "options": {
                    "awslogs-group": args[2],
                    "awslogs-region": "us-east-1",
                    "awslogs-stream-prefix": "ecs"
                }
            }
        }])
    )

    task_definition = aws.ecs.TaskDefinition("gateway-staging-task",
        family="gateway-staging",
        network_mode="awsvpc",
        requires_compatibilities=["EC2"],
        execution_role_arn=execution_role_arn,
        task_role_arn=task_role_arn,
        container_definitions=container_definitions
    )

    return task_definition

# Função para definir o Target Group
def create_target_group(service_name, vpc_id):
    target_group = aws.lb.TargetGroup(f"{service_name}-tg",
        port=8080,
        protocol="HTTP",
        target_type="ip",
        vpc_id=vpc_id,
        health_check=aws.lb.TargetGroupHealthCheckArgs(
            path="/health",
            protocol="HTTP",
            interval=30,
            timeout=5,
            healthy_threshold=2,
            unhealthy_threshold=2
        ),
        tags={"Name": service_name}
    )
    return target_group

# Função para criar regras do ALB
def create_alb_rule(listener_arn, target_group, priority, service_name):
    rule = aws.lb.ListenerRule(f"{service_name}-alb-rule",
        listener_arn=listener_arn,
        conditions=[
            aws.lb.ListenerRuleConditionArgs(
                host_header=aws.lb.ListenerRuleConditionHostHeaderArgs(
                    values=[f"{service_name}.staging.magie.services"]
                )
            ),
            aws.lb.ListenerRuleConditionArgs(
                path_pattern=aws.lb.ListenerRuleConditionPathPatternArgs(
                    values=["/*"]
                )
            )
        ],
        actions=[aws.lb.ListenerRuleActionArgs(
            type="forward",
            target_group_arn=target_group.arn
        )],
        priority=priority,
        tags={"Name": service_name}
    )
    return rule

# Função para criar o registro Route53
def create_route53_record(hosted_zone_id, service_name, alb_dns_name):
    record = aws.route53.Record(f"{service_name}-dns",
        name=f"{service_name}.staging.magie.services",
        type="A",
        zone_id=hosted_zone_id,
        aliases=[aws.route53.RecordAliasArgs(
            name=alb_dns_name,
            zone_id="Z35SXDOTRQ7X7K",
            evaluate_target_health=False
        )]
    )
    return record

# Função para criar o serviço ECS
def create_ecs_service(cluster, task_definition, subnets, security_group, target_group, listener_arn, hosted_zone_id, alb_dns_name):
    service_name = "gateway-staging"

    service = aws.ecs.Service(f"{service_name}-service",
        cluster=cluster.id,
        task_definition=task_definition.arn,
        desired_count=1,
        launch_type="EC2",
        network_configuration=aws.ecs.ServiceNetworkConfigurationArgs(
            assign_public_ip=False,
            subnets=subnets,
            security_groups=[security_group]
        ),
        load_balancers=[aws.ecs.ServiceLoadBalancerArgs(
            target_group_arn=target_group.arn,
            container_name="gateway-container",
            container_port=8080
        )],
        health_check_grace_period_seconds=60,
        deployment_controller=aws.ecs.ServiceDeploymentControllerArgs(
            type="ECS"
        ),
        deployment_maximum_percent=200,
        deployment_minimum_healthy_percent=50,
        tags={"Name": service_name}
    )

    # Criar a regra do ALB e o registro do Route53
    create_alb_rule(listener_arn, target_group, 1, service_name)
    create_route53_record(hosted_zone_id, service_name, alb_dns_name)

    return service
