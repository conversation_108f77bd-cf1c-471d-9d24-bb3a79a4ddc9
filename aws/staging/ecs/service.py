import pulumi
import pulumi_aws as aws

def create_ecs_service(ecs_cluster, task_definition, private_subnets, ecs_sg, target_group):
    service = aws.ecs.Service("magie-staging-service",
                              cluster=ecs_cluster.id,
                              task_definition=task_definition.arn,
                              desired_count=1,
                              launch_type="EC2",
                              network_configuration=aws.ecs.ServiceNetworkConfigurationArgs(
                                  subnets=[subnet.id for subnet in private_subnets],
                                  security_groups=[ecs_sg.id],
                                  assign_public_ip=False,
                              ),
                              load_balancers=[aws.ecs.ServiceLoadBalancerArgs(
                                  target_group_arn=target_group.arn,
                                  container_name="my-container",
                                  container_port=80,
                              )])

    pulumi.export('ecs_service_name', service.name)

    return service
