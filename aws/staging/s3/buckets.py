import pulumi_aws as aws

def create_s3_buckets():
    bucket_names = [
        "magie-site-storage-staging",
        "magie-retool-medias-staging",
        "magie-rds-backups-staging",
        "magie-medias-staging",
        "magie-receipts-staging",
        "magie-assets-staging"
    ]

    buckets = []
    for name in bucket_names:
        bucket = aws.s3.Bucket(name,
            bucket=name,
            acl="private",
            tags={"Environment": "staging"}
        )
        buckets.append(bucket)

    return buckets
