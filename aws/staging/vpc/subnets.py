import pulumi_aws as aws

def create_subnets(vpc, internet_gateway):
    # Cria sub-redes públicas
    public_subnets = []
    private_subnets = []

    availability_zones = ["us-east-1a", "us-east-1b", "us-east-1c", "us-east-1d", "us-east-1e", "us-east-1f"]

    for i, az in enumerate(availability_zones):
        public_subnet = aws.ec2.Subnet(f"public-subnet-{i + 1}",
                                       vpc_id=vpc.id,
                                       cidr_block=f"14.0.{i + 1}.0/24",
                                       map_public_ip_on_launch=True,
                                       availability_zone=az,
                                       tags={"Name": f"public-subnet-{i + 1}"})
        public_subnets.append(public_subnet)

        private_subnet = aws.ec2.Subnet(f"private-subnet-{i + 1}",
                                        vpc_id=vpc.id,
                                        cidr_block=f"14.0.{i + 101}.0/24",
                                        availability_zone=az,
                                        tags={"Name": f"private-subnet-{i + 1}"})
        private_subnets.append(private_subnet)

    # Cria o NAT Gateway na primeira subnet pública
    nat_eip = aws.ec2.Eip("magie-staging-nat-eip", domain="vpc")
    nat_gateway = aws.ec2.NatGateway("magie-staging-nat-gateway",
                                     allocation_id=nat_eip.id,
                                     subnet_id=public_subnets[0].id,
                                     tags={"Name": "nat-gateway"})

    # Cria a tabela de rotas para subnets públicas
    public_route_table = aws.ec2.RouteTable("public-rt",
                                            vpc_id=vpc.id,
                                            routes=[{
                                                "cidr_block": "0.0.0.0/0",
                                                "gateway_id": internet_gateway.id,
                                            }],
                                            tags={"Name": "public-route-table"})

    # Associa subnets públicas à tabela de rotas públicas
    for i, public_subnet in enumerate(public_subnets):
        aws.ec2.RouteTableAssociation(f"public-rt-assoc-{i + 1}",
                                      route_table_id=public_route_table.id,
                                      subnet_id=public_subnet.id)

    # Cria a tabela de rotas para subnets privadas
    private_route_table = aws.ec2.RouteTable("private-rt",
                                             vpc_id=vpc.id,
                                             routes=[{
                                                 "cidr_block": "0.0.0.0/0",
                                                 "nat_gateway_id": nat_gateway.id,
                                             }],
                                             tags={"Name": "private-route-table"})

    # Associa subnets privadas à tabela de rotas privadas
    for i, private_subnet in enumerate(private_subnets):
        aws.ec2.RouteTableAssociation(f"private-rt-assoc-{i + 1}",
                                      route_table_id=private_route_table.id,
                                      subnet_id=private_subnet.id)

    return public_subnets, private_subnets
