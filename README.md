# Infrastructure as Code (IaC) with Terraform

This repository contains Infrastructure as Code (IaC) configurations for managing cloud infrastructure using Terraform, supporting staging and production environments.

## Terraform Infrastructure

The `terraform/` directory contains Terraform configurations managed with Terragrunt for AWS infrastructure deployment. This setup provides a structured approach to managing infrastructure across multiple environments with reusable modules and environment-specific configurations.

### Directory Structure Example

It follows the suggested structure from the [Terragrunt documentation](https://docs.gruntwork.io/2.0/docs/overview/concepts/infrastructure-live/)

```
terraform/
├── aws/
│   ├── live/                    # Environment-specific configurations
│   │   ├── staging/
│   │   │   ├── root.hcl        # Terragrunt root configuration for staging
│   │   │   └── us-east-1/      # Region-specific deployments
│   │   │       ├── lambda/     # Lambda function deployments
│   │   │       │   └── src/    # Lambda function source code
│   │   │       └── networking/ # Network infrastructure
│   │   └── prod/
│   │       └── us-east-1/      # Production region deployments
│   └── modules/                # Reusable Terraform modules
│       └── lambda/             # Lambda function module
│           ├── main.tf         # Main module configuration
│           ├── variables.tf    # Input variables
│           ├── outputs.tf      # Output values
│           └── backend.tf      # Backend configuration
└── README.md
```

### Purpose and Organization

The terraform directory serves as the centralized location for:

- **Infrastructure Modules**: Reusable Terraform modules in `terraform/aws/modules/`
- **Environment Configurations**: Environment-specific deployments in `terraform/aws/live/`
- **State Management**: Remote state storage using S3 backend with DynamoDB locking
- **Multi-Environment Support**: Separate configurations for staging and production
- **Regional Deployments**: Region-specific infrastructure (currently us-east-1)

### Terragrunt Integration

This project uses [Terragrunt](https://terragrunt.gruntwork.io/) as a thin wrapper around Terraform to:

- **DRY Configuration**: Avoid repetition across environments
- **Remote State Management**: Automatic S3 backend configuration
- **Provider Generation**: Automatic AWS provider configuration with environment-specific settings
- **Dependency Management**: Handle dependencies between infrastructure components

#### Key Terragrunt Files

- **`root.hcl`**: Contains shared configuration for remote state and provider settings
- **`terragrunt.hcl`**: Environment-specific configuration that references modules

### Prerequisites

Before working with the Terraform configurations, ensure you have:

1. **Terraform** (>= 1.12): [Install Terraform](https://developer.hashicorp.com/terraform/downloads)
2. **Terragrunt** (>= 0.80): [Install Terragrunt](https://terragrunt.gruntwork.io/docs/getting-started/install/)
3. **AWS CLI**: Configured with appropriate credentials and profiles
4. **AWS Profiles**: Set up profiles for `staging` and `prod` environments

### AWS Profile Configuration

Ensure your AWS credentials are configured with the appropriate profiles. It can be done using the [magie cli](https://github.com/GiroOfficial/mgcli)

### Usage Instructions

#### Basic Terragrunt Commands

Navigate to the specific environment and resource directory before running commands:

```bash
# Using a profile when running the commands
export AWS_PROFILE=staging
or
AWS_PROFILE=staging terragrunt apply

# Example: Deploy lambda function in staging
cd terraform/aws/live/staging/us-east-1/lambda/unleash_backup

# Initialize and download dependencies
terragrunt init

# Plan changes
terragrunt plan

# Apply changes
terragrunt apply

# Destroy resources (use with caution)
terragrunt destroy

# Apply changed in all modules in an environment
cd terraform/aws/live/staging
terragrunt run-all apply
```

#### Working with Different Environments

**Staging Environment:**
```bash
cd terraform/aws/live/staging/us-east-1/[resource-type]/[resource-name]
terragrunt apply
```

**Production Environment:**
```bash
cd terraform/aws/live/prod/us-east-1/[resource-type]/[resource-name]
terragrunt apply
```

#### Common Operations

**Deploy all resources in an environment:**
```bash
# From the environment root
cd terraform/aws/live/staging
terragrunt run-all apply
```

**Plan all changes in an environment:**
```bash
cd terraform/aws/live/staging
terragrunt run-all plan
```

**Update a specific module:**
```bash
cd terraform/aws/live/staging/us-east-1/lambda/unleash_backup
terragrunt apply
```

### Current Infrastructure

The terraform configuration currently manages:

- **Lambda Functions**: Unleash backup functionality with VPC integration
- **IAM Roles and Policies**: Service-specific permissions
- **Security Groups**: Network access controls
- **S3 Backend**: State storage with DynamoDB locking

### State Management

- **Backend**: S3 with DynamoDB locking
- **State Files**: Organized by environment and resource path
- **Locking**: Prevents concurrent modifications
- **Staging Bucket**: `magie-iac-state-staging`
- **Lock Table**: `terraform-lock-staging`

### Best Practices

1. **Always run `terragrunt plan`** before applying changes
2. **Use environment-specific directories** for deployments
3. **Test changes in staging** before applying to production
4. **Review state files** and lock status before major changes
5. **Use `terragrunt run-all`** for environment-wide operations
6. **Keep modules generic** and environment-agnostic

### Troubleshooting

**Common Issues:**

- **State Lock**: If state is locked, check DynamoDB table for stuck locks
- **AWS Credentials**: Ensure correct profile is configured and has necessary permissions
- **Module Source**: Verify module paths in terragrunt.hcl files
- **Provider Version**: Check Terraform version compatibility

**Useful Commands:**
```bash
# Force unlock state (use carefully)
terragrunt force-unlock [LOCK_ID]

# Refresh state
terragrunt refresh

# Import existing resources
terragrunt import [RESOURCE_TYPE].[NAME] [RESOURCE_ID]
```