# AWS KMS Encryption/Decryption Script

Este script (`kms.sh`) permite criptografar e descriptografar arquivos utilizando o AWS KMS com uma chave assimétrica, garantindo que:

- **Todos os usuários** possam criptografar arquivos usando a chave pública.
- **Somente administradores** (ou usuários com permissão para `kms:Decrypt`) possam descriptografar arquivos.

## Visão Geral

O script realiza as seguintes funções:
- Define a região padrão para `us-east-1`.
- Verifica se o AWS CLI está instalado e se o usuário está autenticado (via SSO ou credenciais temporárias).
- Valida se a chave KMS especificada existe e está habilitada.
- Permite que o usuário escolha entre criptografar (`e`) ou descriptografar (`d`) um arquivo.
- Gera um arquivo criptografado (output binário) com a extensão `.encrypted` ou um arquivo descriptografado com a extensão `.decrypted`.

> **Nota:** Para chaves assimétricas, o script utiliza a operação `kms:Encrypt` e a obtenção da chave pública para criptografia, e inclui o parâmetro `--key-id` na operação de descriptografia para garantir que somente administradores possam descriptografar.

## Pré-requisitos

- **AWS CLI** instalado e configurado (via `aws sso login` ou `aws configure`).
- **Brew** instalado (para instalação do AWS CLI, se necessário).
- Permissões necessárias para utilizar o AWS KMS com a chave:
  - **Chave KMS** a ser utilizada:
    - ARN: `arn:aws:kms:us-east-1:992382535149:key/80488b4e-0b97-4ff4-bc23-b0802a38ea1f`
    - Nome: `encrypt-secrets`
- O ambiente deve estar configurado para a região `us-east-1`.x