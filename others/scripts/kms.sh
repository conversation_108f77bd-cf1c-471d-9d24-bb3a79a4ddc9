#!/bin/bash
# -*- coding: utf-8 -*-
#
# Script para criptografar e descriptografar arquivos utilizando a AWS KMS com uma chave assimétrica.
# Valida se o AWS CLI está configurado (via SSO ou credenciais temporárias), se a chave KMS
# existe e está habilitada, e define a região padrão para us-east-1.
#
# Pré-requisitos:
# - AWS CLI instalada e configurada (via SSO ou outro método).
# - Permissões necessárias para usar a chave KMS.
#
# Chave KMS a ser utilizada:
# ARN: arn:aws:kms:us-east-1:************:key/80488b4e-0b97-4ff4-bc23-b0802a38ea1f
# Nome: encrypt-secrets
#
# O script permite escolher entre criptografar (e) e descriptografar (d) um arquivo.

set -euo pipefail
trap 'echo "[ERROR] Ocorreu um erro na linha ${LINENO}. Saindo." >&2' ERR

##############################
# Configurar região padrão
##############################
export AWS_DEFAULT_REGION=us-east-1

##############################
# Funções de log
##############################
log_info() {
    echo -e "[INFO] $1"
}
log_error() {
    echo -e "[ERROR] $1" >&2
}

##############################
# Verificar se o AWS CLI está instalado (via brew)
##############################
if ! command -v aws >/dev/null 2>&1; then
    log_error "O AWS CLI não está instalado."
    read -rp "Deseja instalar o AWS CLI via brew? (s/n): " install_aws_choice
    if [[ "$install_aws_choice" =~ ^[sS]$ ]]; then
        if ! command -v brew >/dev/null 2>&1; then
            log_error "O brew não está instalado. Por favor, instale-o primeiro."
            exit 1
        fi
        log_info "Instalando o AWS CLI via brew..."
        brew install awscli
    else
        log_error "Por favor, instale o AWS CLI manualmente e execute o script novamente."
        exit 1
    fi
fi

##############################
# Validar conexão com a conta AWS (SSO/credenciais temporárias)
##############################
log_info "Verificando a conexão com a conta AWS na região ${AWS_DEFAULT_REGION}..."
if ! aws sts get-caller-identity >/dev/null 2>&1; then
    log_error "Você não está conectado a uma conta AWS ou suas credenciais não estão configuradas."
    log_error "Por favor, execute 'aws sso login' ou 'aws configure' para configurar o AWS CLI."
    exit 1
fi

ACCOUNT_ID=$(aws sts get-caller-identity --query 'Account' --output text)
log_info "Conectado à conta AWS: $ACCOUNT_ID"

##############################
# Parâmetros da chave KMS
##############################
KMS_KEY_ARN="arn:aws:kms:us-east-1:************:key/80488b4e-0b97-4ff4-bc23-b0802a38ea1f"
ENCRYPTION_ALGORITHM="RSAES_OAEP_SHA_256"  # Obrigatório para chaves assimétricas RSA na KMS

##############################
# Validar se a chave KMS existe e está habilitada
##############################
log_info "Verificando a existência e o estado da chave KMS..."
if ! aws kms describe-key --key-id "$KMS_KEY_ARN" >/dev/null 2>&1; then
    log_error "A chave KMS $KMS_KEY_ARN não foi encontrada na conta AWS configurada."
    exit 1
fi

KEY_STATE=$(aws kms describe-key --key-id "$KMS_KEY_ARN" --query 'KeyMetadata.KeyState' --output text)
if [[ "$KEY_STATE" != "Enabled" ]]; then
    log_error "A chave KMS $KMS_KEY_ARN não está habilitada. Estado atual: $KEY_STATE"
    exit 1
fi
log_info "Chave KMS encontrada e está habilitada."

##############################
# Escolher operação: Criptografar ou Descriptografar
##############################
echo ""
read -rp "Escolha a operação - (e) para criptografar, (d) para descriptografar: " mode

##############################
# Operação de CRIPTOGRAFIA
##############################
if [[ "$mode" =~ ^[eE]$ ]]; then
    read -rp "Digite o nome do arquivo a ser criptografado (caminho relativo ou absoluto): " FILE
    if [[ ! -f "$FILE" ]]; then
        log_error "Arquivo '$FILE' não encontrado!"
        exit 1
    fi

    OUTPUT_FILE="${FILE}.encrypted"

    log_info "Criptografando o arquivo '$FILE' usando a chave KMS..."
    # Criptografa o arquivo e decodifica o resultado base64 para gerar um arquivo binário.
    aws kms encrypt \
        --key-id "$KMS_KEY_ARN" \
        --plaintext fileb://"$FILE" \
        --encryption-algorithm "$ENCRYPTION_ALGORITHM" \
        --output text \
        --query CiphertextBlob | base64 --decode > "$OUTPUT_FILE"

    log_info "Arquivo criptografado com sucesso: $OUTPUT_FILE"
    exit 0

##############################
# Operação de DESCRIPTOGRAFIA
##############################
elif [[ "$mode" =~ ^[dD]$ ]]; then
    read -rp "Digite o nome do arquivo a ser descriptografado (caminho relativo ou absoluto): " FILE
    if [[ ! -f "$FILE" ]]; then
        log_error "Arquivo '$FILE' não encontrado!"
        exit 1
    fi

    OUTPUT_FILE="${FILE}.decrypted"

    log_info "Descriptografando o arquivo '$FILE' usando a chave KMS..."
    # Incluímos explicitamente o parâmetro --key-id para chaves assimétricas.
    if ! DECRYPT_OUTPUT=$(aws kms decrypt \
        --key-id "$KMS_KEY_ARN" \
        --ciphertext-blob fileb://"$FILE" \
        --encryption-algorithm "$ENCRYPTION_ALGORITHM" \
        --output text \
        --query Plaintext 2>&1); then
        log_error "Falha ao descriptografar o arquivo. Detalhes: $DECRYPT_OUTPUT"
        exit 1
    fi

    if [[ -z "$DECRYPT_OUTPUT" ]]; then
        log_error "A saída da descriptografia está vazia. O arquivo pode estar corrompido ou não ter sido criptografado com a chave KMS correta."
        exit 1
    fi

    echo "$DECRYPT_OUTPUT" | base64 --decode > "$OUTPUT_FILE"
    log_info "Arquivo descriptografado com sucesso: $OUTPUT_FILE"
    exit 0

else
    log_error "Opção inválida. Use 'e' para criptografar ou 'd' para descriptografar."
    exit 1
fi
