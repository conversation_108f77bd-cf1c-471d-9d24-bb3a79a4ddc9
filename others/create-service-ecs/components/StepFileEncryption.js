import React, { useState, useMemo } from 'react';
import { KMSClient, EncryptCommand, DecryptCommand } from '@aws-sdk/client-kms';

const KMS_KEY_ARN = "arn:aws:kms:us-east-1:992382535149:key/80488b4e-0b97-4ff4-bc23-b0802a38ea1f";
const ENCRYPTION_ALGORITHM = "RSAES_OAEP_SHA_256";
const MAX_FILE_SIZE = 4096; // 4KB máximo para criptografia direta

export default function StepFileEncryption({ credentials, onBack }) {
  const [file, setFile] = useState(null);
  const [mode, setMode] = useState("encrypt");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [downloadUrl, setDownloadUrl] = useState(null);
  const [downloadFilename, setDownloadFilename] = useState("");

  // Cria o cliente KMS uma vez, quando as credenciais estiverem disponíveis
  const kmsClient = useMemo(() => {
    if (!credentials) return null;
    return new KMSClient({
      region: 'us-east-1',
      credentials,
    });
  }, [credentials]);

  const handleFileChange = (e) => {
    const selected = e.target.files?.[0] || null;
    setFile(selected);
    setError("");
    setDownloadUrl(null);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError("");
    setDownloadUrl(null);

    if (!file) {
      setError("Selecione um arquivo.");
      return;
    }
    if (mode === "encrypt" && file.size > MAX_FILE_SIZE) {
      setError("O arquivo excede o limite de 4KB para criptografia direta. Selecione um arquivo menor.");
      return;
    }
    if (!kmsClient) {
      setError("Cliente KMS não configurado. Verifique as credenciais.");
      return;
    }

    setLoading(true);
    try {
      // Lê o conteúdo do arquivo e converte para Uint8Array
      const fileBuffer = await file.arrayBuffer();
      const uint8Data = new Uint8Array(fileBuffer);
      let resultBlob;
      let newFilename;

      if (mode === "encrypt") {
        const command = new EncryptCommand({
          KeyId: KMS_KEY_ARN,
          Plaintext: uint8Data,
          EncryptionAlgorithm: ENCRYPTION_ALGORITHM,
        });
        const data = await kmsClient.send(command);
        resultBlob = new Blob([data.CiphertextBlob], { type: "application/octet-stream" });
        newFilename = file.name + ".encrypted";
      } else {
        const command = new DecryptCommand({
          CiphertextBlob: uint8Data,
          KeyId: KMS_KEY_ARN,
          EncryptionAlgorithm: ENCRYPTION_ALGORITHM,
        });
        const data = await kmsClient.send(command);
        resultBlob = new Blob([data.Plaintext], { type: "application/octet-stream" });
        // Se o arquivo original tiver a extensão ".encrypted", remove para restaurar o nome original
        if (file.name.endsWith(".encrypted")) {
          newFilename = file.name.slice(0, -10);
        } else {
          newFilename = file.name + ".decrypted";
        }
      }
      const url = URL.createObjectURL(resultBlob);
      setDownloadUrl(url);
      setDownloadFilename(newFilename);
    } catch (err) {
      setError("Erro: " + err.message);
    } finally {
      setLoading(false);
    }
  };

  // Define o rótulo do botão de download com base na operação
  const downloadButtonLabel = mode === "encrypt"
    ? "Baixar Arquivo Criptografado"
    : "Baixar Arquivo Descriptografado";

  return (
    <div className="container encryption-container">
      <h1 className="page-title">Criptografar/Descriptografar Arquivo</h1>

      <form onSubmit={handleSubmit} className="encryption-form">
        <div className="form-group">
          <label>Escolha um arquivo:</label>
          <input type="file" onChange={handleFileChange} />
          {file && (
            <p className="file-info">
              <strong>Arquivo:</strong> {file.name}{" "}
              <span className="file-size">
                ({(file.size / 1024).toFixed(2)} KB)
              </span>
            </p>
          )}
        </div>

        <div className="form-group operation-group">
          <label>Operação:</label>
          <div className="radio-group">
            <label>
              <input
                type="radio"
                value="encrypt"
                checked={mode === "encrypt"}
                onChange={() => setMode("encrypt")}
              />
              Criptografar
            </label>
            <label>
              <input
                type="radio"
                value="decrypt"
                checked={mode === "decrypt"}
                onChange={() => setMode("decrypt")}
              />
              Descriptografar
            </label>
          </div>
        </div>

        <button type="submit" className="btn" disabled={loading}>
          {loading ? <span className="loading-text">Processando...</span> : mode === "encrypt" ? "Criptografar" : "Descriptografar"}
        </button>
      </form>

      {error && <p className="error-msg">{error}</p>}

      {downloadUrl && (
        <div className="success-box">
          <p>Operação concluída com sucesso!</p>
          <a href={downloadUrl} download={downloadFilename} className="btn">
            {downloadButtonLabel}
          </a>
        </div>
      )}

      <button className="btn-back" onClick={onBack}>← Voltar</button>
    </div>
  );
}
