// components/StepSummary.js
import React from 'react';

export default function StepSummary({ summary, onDeploy, onBack }) {
  if (!summary) return null;
  
  const { selectedTasks, clusterData, albData, hostedZone, deploymentConfig, ecrRepository } = summary;

  // Extraímos o ARN completo do cluster e transformamos no nome curto
  const fullArn = clusterData.selectedCluster; // Ex.: "arn:aws:ecs:us-east-1:xxxxxxx:cluster/magie-staging-cluster"
  const clusterShortName = fullArn.split('/').pop(); // "magie-staging-cluster"

  // Obter nome da zone, ex.: "staging.magie.services"
  const zoneName = hostedZone.Name || hostedZone.name; // normalizamos

  return (
    <div className="card">
      <h1>Resumo da Implantação</h1>
      <div className="summary-content">

        <div className="summary-section">
          <h3>Definições de Tarefas Selecionadas</h3>
          <ul>
            {selectedTasks.map((task) => {
              // Exemplo de ARN de tarefa: "arn:aws:ecs:us-east-1:xxxx:task-definition/reward-machine:1"
              // Pegamos o nome da tarefa antes do ":"
              const parts = task.split('/').pop().split(':');
              const taskName = parts[0]; // ex.: "reward-machine"
              
              // Montamos a URL final "reward-machine.staging.magie.services"
              const domain = `${taskName}.${zoneName}`.replace(/\.$/, '');

              return (
                <li key={task}>
                  {task}
                  <br />
                  <small style={{ color: '#666' }}>
                    URL do serviço: <strong>{domain}</strong>
                  </small>
                </li>
              );
            })}
          </ul>
        </div>

        <div className="summary-section">
          <h3>Cluster</h3>
          <p><strong>Cluster:</strong> {clusterShortName}</p>
          <p><strong>Capacity Provider:</strong> {clusterData.capacityProvider}</p>
        </div>

        <div className="summary-section">
          <h3>Application Load Balancer</h3>
          <p><strong>Nome:</strong> {albData.selectedLB.LoadBalancerName}</p>
          <p><strong>DNS:</strong> {albData.selectedLB.DNSName}</p>
          <p><strong>Listener:</strong> {albData.listenerArn}</p>
        </div>

        <div className="summary-section">
          <h3>Zona Hospedada (Route 53)</h3>
          <p><strong>Nome:</strong> {hostedZone.Name || hostedZone.name}</p>
          <p><strong>ID:</strong> {hostedZone.Id}</p>
        </div>

        {deploymentConfig && (
          <div className="summary-section">
            <h3>Configuração de Implantação</h3>
            <p><strong>Health Check:</strong> {deploymentConfig.healthCheckPath}</p>
            <p><strong>Número de Réplicas:</strong> {deploymentConfig.desiredCount}</p>
            <p><strong>Ambiente:</strong> {deploymentConfig.environment}</p>
            {deploymentConfig.logGroupNames && Object.keys(deploymentConfig.logGroupNames).length > 0 && (
              <div>
                <h4>Grupos de Logs:</h4>
                <ul>
                  {Object.entries(deploymentConfig.logGroupNames).map(([task, name]) => (
                    <li key={task}>
                      {task}: {name}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}

        {ecrRepository && (
          <div className="summary-section">
            <h3>Repositório ECR</h3>
            <p><strong>Nome do Repositório:</strong> {ecrRepository.repositoryName}</p>
            <p><strong>URI:</strong> {ecrRepository.repositoryUri}</p>
            <p>(Caso não existisse, foi criado automaticamente.)</p>
          </div>
        )}
      </div>

      <div className="button-group">
        <button onClick={onBack} className="btn btn-secondary">Voltar</button>
        <button onClick={onDeploy} className="btn btn-success">Confirmar e Implantar</button>
      </div>
    </div>
  );
}
