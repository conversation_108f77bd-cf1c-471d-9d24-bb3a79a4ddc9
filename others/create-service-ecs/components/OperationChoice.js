// components/OperationChoice.js
import React, { useState } from 'react';

export default function OperationChoice({ onOperationSelected }) {
  const [selectedOperation, setSelectedOperation] = useState('');

  const handleContinue = () => {
    if (!selectedOperation) {
      alert('Por favor, selecione uma operação.');
      return;
    }
    onOperationSelected(selectedOperation);
  };

  return (
    <div className="container">
      <h1 className="page-title">Escolha a Operação</h1>

      <div className="form-group">
        <label>Selecione uma opção:</label>
        <select
          className="dropdown"
          value={selectedOperation}
          onChange={(e) => setSelectedOperation(e.target.value)}
        >
          <option value="">-- Selecione --</option>
          <option value="ECS">Criar Serviço no ECS</option>
          <option value="Encryption">Criptografar/Descriptografar Arquivo</option>
          <option value="ECR">Criar Repositório ECR</option>
        </select>
      </div>

      <button className="btn" onClick={handleContinue}>
        Continuar
      </button>
    </div>
  );
}
