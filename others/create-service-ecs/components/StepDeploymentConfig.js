// components/StepDeploymentConfig.js
import React, { useState } from 'react';

export default function StepDeploymentConfig({ selectedTasks, onNext, onBack }) {
  const [healthCheckPath, setHealthCheckPath] = useState('/health');
  const [desiredCount, setDesiredCount] = useState(1);
  const [environment, setEnvironment] = useState('staging');
  const [logGroupNames, setLogGroupNames] = useState({});
  const [error, setError] = useState('');

  const handleLogGroupChange = (task, value) => {
    setLogGroupNames((prev) => ({ ...prev, [task]: value }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!healthCheckPath) {
      setError('Caminho do health check é obrigatório.');
      return;
    }
    setError('');
    onNext({ healthCheckPath, desiredCount, environment, logGroupNames });
  };

  return (
    <div className="container">
      <h1>Configurações de Implantação</h1>
      {error && <p className="text-red-500 mb-2">{error}</p>}
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="healthCheckPath">Caminho do Health Check:</label>
          <input
            type="text"
            id="healthCheckPath"
            value={healthCheckPath}
            onChange={(e) => setHealthCheckPath(e.target.value)}
            required
          />
        </div>
        <div className="form-group">
          <label htmlFor="desiredCount">Número de Réplicas:</label>
          <input
            type="number"
            id="desiredCount"
            value={desiredCount}
            onChange={(e) => setDesiredCount(Number(e.target.value))}
            required
          />
        </div>
        <div className="form-group">
          <label htmlFor="environment">Ambiente:</label>
          <input
            type="text"
            id="environment"
            value={environment}
            onChange={(e) => setEnvironment(e.target.value)}
            required
          />
        </div>
        <h3 className="text-lg font-semibold mb-2">Grupos de Logs para cada Tarefa</h3>
        {selectedTasks.map((task) => {
          // Exemplo: transforma "arn:aws:ecs:us-east-1:xxxx:task-definition/mytask:3"
          // para "mytask" e monta o defaultName
          const defaultName = `/ecs/${environment}/${task.split('/').pop().split(':')[0]}`;
          return (
            <div key={task} className="form-group">
              <label>{task}:</label>
              <input
                type="text"
                placeholder={defaultName}
                value={logGroupNames[task] || ''}
                onChange={(e) => handleLogGroupChange(task, e.target.value)}
              />
            </div>
          );
        })}
        <div className="button-group">
          <button type="button" onClick={onBack} className="btn btn-secondary">
            Voltar
          </button>
          <button type="submit" className="btn">
            Próximo
          </button>
        </div>
      </form>
    </div>
  );
}
