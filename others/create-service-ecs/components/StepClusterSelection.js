// components/StepClusterSelection.js
import React, { useState, useEffect } from 'react';
import { loadClusters, getCapacityProvider } from '../utils/awsFunctions';

export default function StepClusterSelection({ ecsClient, onNext, onBack }) {
  const [clusters, setClusters] = useState([]);
  const [selectedClusterArn, setSelectedClusterArn] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    async function fetchClusters() {
      try {
        const clustersData = await loadClusters(ecsClient);
        setClusters(clustersData); // clustersData são ARNs completos
      } catch (err) {
        setError('Erro ao carregar clusters.');
      } finally {
        setLoading(false);
      }
    }
    if (ecsClient) {
      fetchClusters();
    }
  }, [ecsClient]);

  const handleNext = async () => {
    if (!selectedClusterArn) {
      setError('Selecione um cluster.');
      return;
    }
    try {
      // Chamamos getCapacityProvider com o ARN completo
      let capacityProvider = await getCapacityProvider(ecsClient, selectedClusterArn);
      if (!capacityProvider) {
        capacityProvider = window.prompt(
          'Nenhum Capacity Provider associado. Insira o nome do Capacity Provider:'
        );
      }
      onNext({
        // Guardamos o ARN real do cluster
        selectedCluster: selectedClusterArn,
        capacityProvider,
      });
    } catch (err) {
      setError('Erro ao obter o capacity provider.');
    }
  };

  const handleSelectChange = (e) => {
    setSelectedClusterArn(e.target.value);
  };

  if (loading) return <div className="loading-text">Carregando clusters...</div>;
  if (error) return <p className="text-red-500">{error}</p>;

  return (
    <div className="container">
      <h1>Selecione o Cluster ECS</h1>
      <select
        className="dropdown"
        onChange={handleSelectChange}
        value={selectedClusterArn}
      >
        <option value="">Selecione um cluster</option>
        {clusters.map((arn) => {
          // Extraímos o nome curto do ARN
          const shortName = arn.split('/').pop(); 
          return (
            <option key={arn} value={arn}>
              {shortName}
            </option>
          );
        })}
      </select>
      {error && <p className="text-red-500 mt-2">{error}</p>}
      <div className="button-group">
        <button onClick={onBack} className="btn btn-secondary">
          Voltar
        </button>
        <button onClick={handleNext} className="btn">
          Próximo
        </button>
      </div>
    </div>
  );
}
