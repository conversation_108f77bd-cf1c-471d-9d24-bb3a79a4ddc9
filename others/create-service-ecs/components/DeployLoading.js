// components/DeployLoading.js
import React from 'react';

export default function DeployLoading() {
  return (
    <div className="deployment-animation-container">
      {/* Cloud Container */}
      <div className="cloud-container">
        <div className="cloud-success"></div>
        <div className="cloud"></div>
        
        {/* App Components */}
        <div className="app-component"></div>
        <div className="app-component"></div>
        <div className="app-component"></div>
      </div>

      {/* Message */}
      <div className="deployment-message">
        Sua aplicação está sendo implantada, aguarde...
      </div>
    </div>
  );
}
