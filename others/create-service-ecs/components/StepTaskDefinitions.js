// components/StepTaskDefinitions.js
import React, { useState, useEffect } from 'react';
import { loadTaskDefinitions } from '../utils/awsFunctions';

export default function StepTaskDefinitions({ ecsClient, onNext, onBack }) {
  const [taskDefinitions, setTaskDefinitions] = useState([]);
  const [selectedTasks, setSelectedTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    async function fetchTasks() {
      try {
        const tasks = await loadTaskDefinitions(ecsClient);
        // Ordena as tarefas em ordem alfabética com base no nome extraído da ARN
        const sortedTasks = tasks.sort((a, b) => {
          const aName = a.split('/').pop().split(':')[0].toLowerCase();
          const bName = b.split('/').pop().split(':')[0].toLowerCase();
          if (aName < bName) return -1;
          if (aName > bName) return 1;
          return 0;
        });
        setTaskDefinitions(sortedTasks);
      } catch (err) {
        setError('Erro ao carregar definições de tarefa.');
      } finally {
        setLoading(false);
      }
    }
    if (ecsClient) {
      fetchTasks();
    }
  }, [ecsClient]);

  const handleSelectChange = (e) => {
    // Converte as opções selecionadas para um array de valores (ARNs)
    const selectedValues = Array.from(e.target.selectedOptions, option => option.value);
    setSelectedTasks(selectedValues);
  };

  const handleNext = () => {
    if (selectedTasks.length === 0) {
      setError('Selecione pelo menos uma definição de tarefa.');
      return;
    }
    onNext(selectedTasks);
  };

  if (loading) return <div className="loading-text">Carregando definições de tarefa...</div>;
  if (error) return <p className="text-red-500">{error}</p>;

  return (
    <div className="container">
      <h1>Selecione as Definições de Tarefa</h1>
      <select
        multiple
        className="dropdown"
        onChange={handleSelectChange}
        size={Math.min(10, taskDefinitions.length)}
      >
        {taskDefinitions.map((arn) => {
          // Exemplo de ARN: arn:aws:ecs:us-east-1:xxx:task-definition/workflow:6
          const parts = arn.split('/').pop().split(':');
          const [taskName, version] = parts;
          return (
            <option key={arn} value={arn}>
              {taskName} (versão {version})
            </option>
          );
        })}
      </select>
      {error && <p className="text-red-500 mt-2">{error}</p>}
      <div className="button-group">
        <button onClick={onBack} className="btn btn-secondary">
          Voltar
        </button>
        <button onClick={handleNext} className="btn">
          Próximo
        </button>
      </div>
    </div>
  );
}
