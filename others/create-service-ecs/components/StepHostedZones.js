// components/StepHostedZones.js
import React, { useState, useEffect } from 'react';
import { loadHostedZones } from '../utils/awsFunctions';

export default function StepHostedZones({ route53Client, onNext, onBack }) {
  const [hostedZones, setHostedZones] = useState([]);
  const [selectedZone, setSelectedZone] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    async function fetchZones() {
      try {
        const zones = await loadHostedZones(route53Client);
        setHostedZones(zones);
      } catch (err) {
        setError('Erro ao carregar zonas hospedadas.');
      } finally {
        setLoading(false);
      }
    }
    if (route53Client) {
      fetchZones();
    }
  }, [route53Client]);

  const handleNext = () => {
    if (!selectedZone) {
      setError('Selecione uma zona hospedada.');
      return;
    }
    onNext(selectedZone);
  };

  if (loading) return <div className="loading-text">Carregando zonas hospedadas...</div>;
  if (error) return <p className="text-red-500">{error}</p>;

  return (
    <div className="container">
      <h1>Selecione a Zona Hospedada (Route 53)</h1>
      <select
        className="dropdown"
        onChange={(e) => {
          const zone = hostedZones.find((z) => z.Id === e.target.value);
          setSelectedZone(zone);
        }}
        value={selectedZone ? selectedZone.Id : ''}
      >
        <option value="">Selecione uma zona hospedada</option>
        {hostedZones.map((zone) => (
          <option key={zone.Id} value={zone.Id}>
            {zone.Name}
          </option>
        ))}
      </select>
      {error && <p className="text-red-500 mt-2">{error}</p>}
      <div className="button-group">
        <button onClick={onBack} className="btn btn-secondary">
          Voltar
        </button>
        <button onClick={handleNext} className="btn">
          Próximo
        </button>
      </div>
    </div>
  );
}
