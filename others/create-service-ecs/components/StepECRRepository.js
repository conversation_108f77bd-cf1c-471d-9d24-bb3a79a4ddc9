import React, { useState, useEffect } from 'react';
import { createOrGetECRRepository } from '../utils/ecrFunctions';

export default function StepECRRepository({ ecrClient, onNext }) {
  const [repositoryName, setRepositoryName] = useState('');
  const [repoError, setRepoError] = useState('');
  const [infoMessage, setInfoMessage] = useState('');
  const [loading, setLoading] = useState(false);

  // Armazena o repositório obtido/criado e se foi criado agora
  const [repository, setRepository] = useState(null);
  const [wasCreated, setWasCreated] = useState(false);

  useEffect(() => {
    console.log('StepECRRepository - ecrClient:', ecrClient);
  }, [ecrClient]);

  const handleSubmit = async () => {
    console.log('handleSubmit foi chamado');
    if (!repositoryName.trim()) {
      setRepoError('Por favor, informe o nome do repositório.');
      return;
    }
    setRepoError('');
    setInfoMessage('');
    setRepository(null);
    setWasCreated(false);

    if (!ecrClient) {
      setRepoError('Cliente ECR não disponível. Verifique credenciais e região us-east-1.');
      return;
    }

    setLoading(true);
    setInfoMessage('Verificando/criando repositório. Aguarde...');
    try {
      console.log('Chamando createOrGetECRRepository para:', repositoryName);
      const { repo, created } = await createOrGetECRRepository(ecrClient, repositoryName);

      if (repo && repo.repositoryName) {
        setRepository(repo);
        setWasCreated(created);
        if (created) {
          setInfoMessage(`Repositório "${repo.repositoryName}" foi criado com sucesso!`);
        } else {
          setInfoMessage(`Repositório "${repo.repositoryName}" já existe e está pronto para uso.`);
        }
        console.log('Repositório obtido/criado com sucesso:', repo);
      } else {
        setRepoError('Repositório retornado está vazio ou indefinido.');
        console.error('Repositório retornado está vazio.', repo);
      }
    } catch (err) {
      setRepoError(`Erro ao criar/obter o repositório: ${err.message}`);
      console.error('Detalhes do erro ECR:', err);
    } finally {
      setLoading(false);
    }
  };

  // Permite criar um novo repositório
  const handleNewRepository = () => {
    setRepository(null);
    setWasCreated(false);
    setRepositoryName('');
    setRepoError('');
    setInfoMessage('');
  };

  // Volta para a seleção inicial
  const handleBack = () => {
    // onNext(null) deve retornar ao passo anterior (ex.: OperationChoice)
    onNext && onNext(null);
  };

  return (
    <div className="container">
      <h1 className="page-title">Criar/Obter Repositório ECR</h1>

      {repoError && <p className="text-red-500">{repoError}</p>}
      {infoMessage && <p className="text-green-500">{infoMessage}</p>}

      {/* Se ainda não temos um repositório, exibe o formulário */}
      {!repository ? (
        <>
          <div className="form-group">
            <label>Nome do Repositório</label>
            <input
              type="text"
              value={repositoryName}
              onChange={(e) => setRepositoryName(e.target.value)}
              placeholder="ex: meu-repositorio"
            />
          </div>
          <div className="button-group" style={{ marginTop: '20px' }}>
            <button
              onClick={handleSubmit}
              className="btn"
              disabled={loading || !ecrClient}
            >
              {loading ? <span className="loading-text">Aguarde...</span> : 'Criar/Obter Repositório'}
            </button>
            {/* Botão para voltar antes de criar */}
            <button
              onClick={handleBack}
              className="btn btn-secondary"
              style={{ marginLeft: '10px' }}
            >
              Voltar
            </button>
          </div>
        </>
      ) : (
        <>
          {/* Se já temos um repositório, exibe as opções */}
          <div className="button-group" style={{ marginTop: '20px' }}>
            <button onClick={handleNewRepository} className="btn">
              Criar Novo Repositório
            </button>
            <button onClick={handleBack} className="btn btn-secondary" style={{ marginLeft: '10px' }}>
              Voltar para Seleção
            </button>
          </div>
        </>
      )}
    </div>
  );
}
