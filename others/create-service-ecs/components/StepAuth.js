import React, { useState } from 'react';
import { STSClient, GetCallerIdentityCommand } from '@aws-sdk/client-sts';
import Popup from 'reactjs-popup';

export default function StepAuth({ onAuthSuccess }) {
  const [accessKeyId, setAccessKeyId] = useState('');
  const [secretAccessKey, setSecretAccessKey] = useState('');
  const [sessionToken, setSessionToken] = useState('');
  const [error, setError] = useState('');

  // Controle do modal
  const [modalOpen, setModalOpen] = useState(false);
  const [accountIdentity, setAccountIdentity] = useState(null);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!accessKeyId || !secretAccessKey || !sessionToken) {
      setError('Por favor, preencha todos os campos.');
      return;
    }
    setError('');

    const stsClient = new STSClient({
      region: 'us-east-1',
      credentials: { accessKeyId, secretAccessKey, sessionToken },
    });

    try {
      const command = new GetCallerIdentityCommand({});
      const identity = await stsClient.send(command);
      setAccountIdentity(identity);
      setModalOpen(true);
    } catch (err) {
      console.error(err);
      setError('Credenciais inválidas. Por favor, verifique e tente novamente.');
    }
  };

  const handleConfirm = () => {
    setModalOpen(false);
    onAuthSuccess({ accessKeyId, secretAccessKey, sessionToken });
  };

  const handleCancel = () => {
    setModalOpen(false);
    setAccessKeyId('');
    setSecretAccessKey('');
    setSessionToken('');
  };

  return (
    <div className="container">
      <h1>Autenticação AWS</h1>
      {error && <p className="text-red-500">{error}</p>}

      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="accessKeyId">ID da Chave de Acesso AWS</label>
          <input
            type="text"
            id="accessKeyId"
            value={accessKeyId}
            onChange={(e) => setAccessKeyId(e.target.value)}
            required
          />
        </div>
        <div className="form-group">
          <label htmlFor="secretAccessKey">Chave de Acesso Secreta AWS</label>
          <input
            type="text"
            id="secretAccessKey"
            value={secretAccessKey}
            onChange={(e) => setSecretAccessKey(e.target.value)}
            required
          />
        </div>
        <div className="form-group">
          <label htmlFor="sessionToken">Token de Sessão AWS</label>
          <input
            type="text"
            id="sessionToken"
            value={sessionToken}
            onChange={(e) => setSessionToken(e.target.value)}
            required
          />
        </div>
        <button type="submit" className="btn">
          Autenticar
        </button>
      </form>

      <Popup
        open={modalOpen}
        modal
        closeOnDocumentClick={false}
        onClose={() => setModalOpen(false)}
      >
        <div className="modal-card">
          <h2 className="mb-4">Confirmar Conta</h2>
          {accountIdentity && (
            <>
              <p><strong>Account:</strong> {accountIdentity.Account}</p>
              <p><strong>ARN:</strong> <span className="arn-wrap">{accountIdentity.Arn}</span></p>
              <p><strong>UserId:</strong> {accountIdentity.UserId}</p>
              <p className="mt-4">
                Deseja prosseguir com essa conta?
              </p>
              <div className="button-group">
                <button onClick={handleConfirm} className="btn btn-success">
                  Sim, Confirmar
                </button>
                <button onClick={handleCancel} className="btn btn-secondary">
                  Cancelar
                </button>
              </div>
            </>
          )}
        </div>
      </Popup>
    </div>
  );
}


