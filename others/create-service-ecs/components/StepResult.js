// components/StepResult.js
import React, { useState } from 'react';
import { checkApplicationStatus } from '../utils/awsFunctions';

// Componente Modal
const Modal = ({ isOpen, onClose, title, children }) => {
  if (!isOpen) return null;

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={e => e.stopPropagation()}>
        <div className="modal-header">
          <h2 className="modal-title">{title}</h2>
          <button className="modal-close" onClick={onClose}>×</button>
        </div>
        <div className="modal-body">
          {children}
        </div>
      </div>
    </div>
  );
};

// Componente Card
const SummaryCard = ({ title, description, onClick, status }) => (
  <div className="summary-card" onClick={onClick}>
    <div className="card-content">
      <div className="card-title">{title}</div>
      <div className="card-description">{description}</div>
    </div>
    {status && (
      <div className={`status-badge ${status.includes('Erro') ? 'error' : 'success'}`}>
        {status.includes('Erro') ? '×' : '✓'}
      </div>
    )}
  </div>
);

export default function StepResult({ result, onNewDeploy, onDeleteService, ecsClient, selectedCluster }) {
  const [statusMessage, setStatusMessage] = useState('');
  const [activeModal, setActiveModal] = useState(null);

  if (!result) return null;

  const { results, logs } = result;
  const hasError = results.some((r) => r.error);

  // Handler para conferir o status da aplicação
  const handleCheckStatus = async () => {
    if (!results || results.length === 0) {
      alert("Nenhum serviço para verificar.");
      return;
    }
    const firstResult = results[0];
    if (!firstResult.serviceArn) {
      alert("Serviço não criado ou com erro.");
      return;
    }
    const arnParts = firstResult.serviceArn.split('/');
    const serviceName = arnParts[arnParts.length - 1];

    const statusResult = await checkApplicationStatus(ecsClient, selectedCluster, serviceName);
    setStatusMessage(statusResult.message);
  };

  const renderTaskDefinitions = () => (
    <div className="summary-section">
      {results.map((item, idx) => {
        const taskParts = item.task.split('/').pop().split(':');
        const taskName = taskParts[0];
        const serviceUrl = `https://${taskName}.staging.magie.services`;
        
        return (
          <div key={idx} className="summary-item">
            {item.error ? (
              <div className="error-msg">Erro: {item.error}</div>
            ) : (
              <div className="task-definition-item">
                <div>
                  <div className="summary-label">Task Definition ARN</div>
                  <div className="summary-value">{item.task}</div>
                </div>
                <div>
                  <div className="summary-label">URL do serviço</div>
                  <a href={serviceUrl} className="summary-url" target="_blank" rel="noopener noreferrer">
                    {serviceUrl}
                  </a>
                </div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );

  const renderClusterInfo = () => (
    <div className="summary-section">
      <div className="summary-item">
        <div className="summary-label">Cluster</div>
        <div className="summary-value">{selectedCluster}</div>
      </div>
    </div>
  );

  const renderLoadBalancer = () => (
    <div className="summary-section">
      <div className="summary-group">
        <div className="summary-item">
          <div className="summary-label">Nome</div>
          <div className="summary-value">private-app-lb-80169cf</div>
        </div>
        <div className="summary-item">
          <div className="summary-label">DNS</div>
          <div className="summary-value">internal-private-app-lb-80169cf.us-east-1.elb.amazonaws.com</div>
        </div>
      </div>
    </div>
  );

  const renderDeploymentConfig = () => (
    <div className="deployment-config">
      <div className="config-item">
        <div className="config-label">Health Check</div>
        <div className="config-value">/health</div>
      </div>
      <div className="config-item">
        <div className="config-label">Número de Réplicas</div>
        <div className="config-value">1</div>
      </div>
      <div className="config-item">
        <div className="config-label">Ambiente</div>
        <div className="config-value">staging</div>
      </div>
    </div>
  );

  const cards = [
    {
      title: "Definições",
      description: "Visualize as tarefas implantadas e suas URLs",
      onClick: () => setActiveModal('tasks')
    },
    {
      title: "Cluster",
      description: "Informações do cluster e capacity provider",
      onClick: () => setActiveModal('cluster')
    },
    {
      title: "Load Balancer",
      description: "Detalhes do balanceador de carga",
      onClick: () => setActiveModal('loadBalancer')
    },
    {
      title: "Configuração",
      description: "Configurações de implantação",
      onClick: () => setActiveModal('config')
    },
    {
      title: "Logs",
      description: "Logs da implantação",
      onClick: () => setActiveModal('logs')
    },
    {
      title: "Status",
      description: "Verificar status atual do serviço",
      onClick: handleCheckStatus,
      status: statusMessage
    }
  ];

  return (
    <div className="card">
      <h1 className="summary-title">Resumo da Implantação</h1>

      <div className="summary-cards">
        {cards.map((card, index) => (
          <SummaryCard
            key={index}
            title={card.title}
            description={card.description}
            onClick={card.onClick}
            status={card.status}
          />
        ))}
      </div>

      {/* Modais */}
      <Modal
        isOpen={activeModal === 'tasks'}
        onClose={() => setActiveModal(null)}
        title="Definições de Tarefas"
      >
        {renderTaskDefinitions()}
      </Modal>

      <Modal
        isOpen={activeModal === 'cluster'}
        onClose={() => setActiveModal(null)}
        title="Cluster"
      >
        {renderClusterInfo()}
      </Modal>

      <Modal
        isOpen={activeModal === 'loadBalancer'}
        onClose={() => setActiveModal(null)}
        title="Load Balancer"
      >
        {renderLoadBalancer()}
      </Modal>

      <Modal
        isOpen={activeModal === 'config'}
        onClose={() => setActiveModal(null)}
        title="Configuração"
      >
        {renderDeploymentConfig()}
      </Modal>

      <Modal
        isOpen={activeModal === 'logs'}
        onClose={() => setActiveModal(null)}
        title="Logs da Implantação"
      >
        <div className="log-container">
          {logs.map((log, idx) => (
            <div key={idx} className="log-item">{log}</div>
          ))}
        </div>
      </Modal>

      {/* Botões de ação */}
      <div className="button-group">
        <button className="btn" onClick={onNewDeploy}>
          Voltar ao início
        </button>
        {hasError && onDeleteService && (
          <button className="btn btn-danger" onClick={onDeleteService}>
            Excluir Serviço
          </button>
        )}
      </div>
    </div>
  );
}
