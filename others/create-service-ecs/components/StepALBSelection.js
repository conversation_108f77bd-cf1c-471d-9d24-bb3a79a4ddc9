// components/StepALBSelection.js
import React, { useState, useEffect } from 'react';
import { loadLoadBalancers, getListener } from '../utils/awsFunctions';

export default function StepALBSelection({ elbClient, onNext, onBack }) {
  const [loadBalancers, setLoadBalancers] = useState([]);
  const [selectedLB, setSelectedLB] = useState(null);
  const [listenerArn, setListenerArn] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    async function fetchLBs() {
      try {
        const lbs = await loadLoadBalancers(elbClient);
        setLoadBalancers(lbs);
      } catch (err) {
        setError('Erro ao carregar Load Balancers.');
      } finally {
        setLoading(false);
      }
    }
    if (elbClient) {
      fetchLBs();
    }
  }, [elbClient]);

  const handleNext = async () => {
    if (!selectedLB) {
      setError('Selecione um ALB.');
      return;
    }
    try {
      const listener = await getListener(elbClient, selectedLB.LoadBalancerArn);
      if (!listener) {
        setError('Nenhum listener na porta 443 encontrado.');
        return;
      }
      setListenerArn(listener);
      onNext({ selectedLB, listenerArn: listener });
    } catch (err) {
      setError('Erro ao obter o listener.');
    }
  };

  if (loading) return <div className="loading-text">Carregando ALBs...</div>;
  if (error) return <p className="text-red-500">{error}</p>;

  return (
    <div className="container">
      <h1>Selecione o Application Load Balancer (ALB)</h1>
      <select
        className="dropdown"
        onChange={(e) => {
          const lb = loadBalancers.find(
            (item) => item.LoadBalancerArn === e.target.value
          );
          setSelectedLB(lb);
        }}
        value={selectedLB ? selectedLB.LoadBalancerArn : ''}
      >
        <option value="">Selecione um ALB</option>
        {loadBalancers.map((lb) => (
          <option key={lb.LoadBalancerArn} value={lb.LoadBalancerArn}>
            {lb.DNSName}
          </option>
        ))}
      </select>
      {error && <p className="text-red-500 mt-2">{error}</p>}
      <div className="button-group">
        <button onClick={onBack} className="btn btn-secondary">
          Voltar
        </button>
        <button onClick={handleNext} className="btn">
          Próximo
        </button>
      </div>
    </div>
  );
}
