// hooks/useAwsClients.js
import { useEffect, useState } from 'react';
import { ECSClient } from '@aws-sdk/client-ecs';
import { ElasticLoadBalancingV2Client } from '@aws-sdk/client-elastic-load-balancing-v2';
import { Route53Client } from '@aws-sdk/client-route-53';
import { CloudWatchLogsClient } from '@aws-sdk/client-cloudwatch-logs';
import { ECRClient } from '@aws-sdk/client-ecr';

export default function useAwsClients(credentials, region = 'us-east-1') {
  const [clients, setClients] = useState(null);

  useEffect(() => {
    if (credentials) {
      const config = { region, credentials };
      setClients({
        ecsClient: new ECSClient(config),
        elbClient: new ElasticLoadBalancingV2Client(config),
        route53Client: new Route53Client(config),
        logsClient: new CloudWatchLogsClient(config),
        ecrClient: new ECRClient(config),
      });
    } else {
      setClients(null);
    }
  }, [credentials, region]);

  return clients;
}
