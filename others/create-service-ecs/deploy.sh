#!/bin/bash

# Para o script encerrar em caso de erro:
set -e

# Nome do bucket e ID do CloudFront
BUCKET_NAME="magie-create-services-ecs"
DISTRIBUTION_ID="E1YPT7MWQGVYI3"

echo "=== 1. Construindo projeto Next.js (Gerando arquivos em 'out/') ==="
npm run build

echo "=== 2. Sincronizando arquivos com o bucket S3 ==="
aws s3 sync out/ "s3://${BUCKET_NAME}" --delete

echo "=== 3. Invalidando o cache do CloudFront ==="
aws cloudfront create-invalidation \
  --distribution-id "${DISTRIBUTION_ID}" \
  --paths "/*"

echo "=== Implantação concluída com sucesso! ==="
