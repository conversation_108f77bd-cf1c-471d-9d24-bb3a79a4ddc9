// utils/awsFunctions.js
import {
  ListTaskDefinitionsCommand,
  DescribeTaskDefinitionCommand,
  ListClustersCommand,
  DescribeClustersCommand,
  CreateServiceCommand,
  DescribeServicesCommand,
  UpdateServiceCommand,
} from '@aws-sdk/client-ecs';
import {
  DescribeLoadBalancersCommand,
  DescribeListenersCommand,
  CreateTargetGroupCommand,
  DescribeTargetGroupsCommand,
  CreateRuleCommand,
  DescribeRulesCommand,
} from '@aws-sdk/client-elastic-load-balancing-v2';
import { ListHostedZonesCommand, ChangeResourceRecordSetsCommand } from '@aws-sdk/client-route-53';
import { CreateLogGroupCommand } from '@aws-sdk/client-cloudwatch-logs';
import { DescribeRepositoriesCommand, CreateRepositoryCommand } from '@aws-sdk/client-ecr';

/**
 * Carrega todas as definições de tarefa ECS, paginando e retornando apenas a versão
 * mais recente de cada tarefa. A comparação é feita numericamente.
 */
export async function loadTaskDefinitions(ecsClient) {
  try {
    let taskDefinitionArns = [];
    let nextToken = null;
    do {
      const command = new ListTaskDefinitionsCommand({
        sort: 'DESC',
        maxResults: 100,
        nextToken,
      });
      const data = await ecsClient.send(command);
      taskDefinitionArns.push(...(data.taskDefinitionArns || []));
      nextToken = data.nextToken;
    } while (nextToken);

    // Seleciona a versão mais alta de cada task
    const latestTasks = {};
    for (const arn of taskDefinitionArns) {
      // Exemplo: arn:aws:ecs:us-east-1:xxxx:task-definition/workflow:1000
      const taskSegment = arn.split('/').pop(); // "workflow:1000"
      const [taskName, versionStr] = taskSegment.split(':');
      const version = parseInt(versionStr, 10) || 0;
      if (!latestTasks[taskName] || version > latestTasks[taskName].version) {
        latestTasks[taskName] = { arn, version };
      }
    }
    return Object.values(latestTasks).map((item) => item.arn);
  } catch (err) {
    console.error('Erro ao carregar definições de tarefa:', err);
    throw err;
  }
}

export async function loadClusters(ecsClient) {
  try {
    const command = new ListClustersCommand({});
    const data = await ecsClient.send(command);
    return data.clusterArns;
  } catch (err) {
    console.error('Erro ao carregar clusters:', err);
    throw err;
  }
}

export async function getCapacityProvider(ecsClient, clusterArn) {
  try {
    const command = new DescribeClustersCommand({ clusters: [clusterArn] });
    const data = await ecsClient.send(command);
    const clusterInfo = data.clusters?.[0];
    return clusterInfo?.capacityProviders?.[0] || null;
  } catch (err) {
    console.error('Erro ao obter capacity provider:', err);
    throw err;
  }
}

export async function loadLoadBalancers(elbClient) {
  try {
    const command = new DescribeLoadBalancersCommand({});
    const data = await elbClient.send(command);
    const albs = (data.LoadBalancers || []).filter((lb) => lb.Type === 'application');
    return albs;
  } catch (err) {
    console.error('Erro ao carregar Load Balancers:', err);
    throw err;
  }
}

export async function getListener(elbClient, loadBalancerArn) {
  try {
    const command = new DescribeListenersCommand({ LoadBalancerArn: loadBalancerArn });
    const data = await elbClient.send(command);
    const listener = data.Listeners?.find((l) => l.Port === 443);
    return listener ? listener.ListenerArn : null;
  } catch (err) {
    console.error('Erro ao obter listener:', err);
    throw err;
  }
}

export async function loadHostedZones(route53Client) {
  try {
    const command = new ListHostedZonesCommand({});
    const data = await route53Client.send(command);
    return data.HostedZones;
  } catch (err) {
    console.error('Erro ao carregar Hosted Zones:', err);
    throw err;
  }
}

/**
 * Cria ou obtém o repositório ECR na região us-east-1.
 * Se o repositório existir, retorna-o; caso contrário, cria-o.
 */
export async function createOrGetECRRepository(ecrClient, repositoryName) {
  if (!ecrClient) {
    throw new Error(
      "O cliente ECR não está disponível. Verifique se você está autenticado, " +
      "se as credenciais estão corretas e se a região us-east-1 está configurada."
    );
  }
  try {
    const describeCommand = new DescribeRepositoriesCommand({ repositoryNames: [repositoryName] });
    const data = await ecrClient.send(describeCommand);
    if (data.repositories && data.repositories.length > 0) {
      console.log(`Repositório "${repositoryName}" encontrado na região us-east-1.`);
      return data.repositories[0];
    }
  } catch (err) {
    if (err.name === 'RepositoryNotFoundException') {
      try {
        const createCommand = new CreateRepositoryCommand({ repositoryName });
        const data = await ecrClient.send(createCommand);
        console.log(`Repositório "${repositoryName}" criado com sucesso na região us-east-1.`);
        return data.repository;
      } catch (createErr) {
        console.error(`Erro ao criar o repositório ECR "${repositoryName}" na região us-east-1:`, createErr);
        throw new Error(`Erro ao criar o repositório ECR: ${createErr.message}`);
      }
    }
    console.error(`Erro ao obter o repositório ECR "${repositoryName}" na região us-east-1:`, err);
    throw new Error(`Erro ao obter ou criar repositório ECR: ${err.message}`);
  }
  throw new Error(
    `Não foi possível encontrar ou criar o repositório "${repositoryName}" na região us-east-1. Motivo desconhecido.`
  );
}

/**
 * Aguarda até que o serviço esteja em estado ACTIVE, com um número de tentativas e delay configuráveis.
 */
async function waitForServiceActive(
  ecsClient,
  cluster,
  serviceName,
  maxAttempts = 10,
  delayMs = 5000,
  progressCallback,
  log
) {
  for (let attempt = 0; attempt < maxAttempts; attempt++) {
    const response = await ecsClient.send(
      new DescribeServicesCommand({ cluster, services: [serviceName] })
    );
    const service = response.services?.[0];
    log(`Tentativa ${attempt + 1}: Serviço ${serviceName} status: ${service?.status ?? 'N/A'}`);
    
    if (service && service.status === 'ACTIVE' && service.runningCount === service.desiredCount) {
      return service;
    }
    if (service && ['DRAINING', 'PROVISIONING'].includes(service.status)) {
      return service;
    }
    await new Promise((resolve) => setTimeout(resolve, delayMs));
  }
  throw new Error(`O serviço ${serviceName} não ficou ACTIVE após ${maxAttempts} tentativas.`);
}

/**
 * Cria ou atualiza um registro no Route53 que aponta para o ALB.
 */
export async function createRoute53Record(route53Client, hostedZoneId, domainName, loadBalancerDNS, elbHostedZoneId, progressCallback) {
  try {
    const command = new ChangeResourceRecordSetsCommand({
      HostedZoneId: hostedZoneId,
      ChangeBatch: {
        Changes: [
          {
            Action: "UPSERT",
            ResourceRecordSet: {
              Name: domainName,
              Type: "A",
              AliasTarget: {
                HostedZoneId: elbHostedZoneId,
                DNSName: loadBalancerDNS,
                EvaluateTargetHealth: false,
              },
            },
          },
        ],
      },
    });
    await route53Client.send(command);
    progressCallback?.(`Registro Route53 para ${domainName} criado/atualizado com sucesso.`);
  } catch (err) {
    progressCallback?.(`Erro ao criar registro Route53 para ${domainName}: ${err.message}`);
    console.error('Erro no registro Route53:', err);
    // Não lança erro para que o fluxo continue
  }
}

/**
 * Verifica o status de um serviço ECS e retorna um objeto com o resultado.
 */
export async function checkApplicationStatus(ecsClient, cluster, serviceName) {
  try {
    const response = await ecsClient.send(
      new DescribeServicesCommand({ cluster, services: [serviceName] })
    );
    const service = response.services?.[0];
    if (!service) {
      return { healthy: false, message: 'Serviço não encontrado.' };
    }
    if (service.status === 'ACTIVE' && service.runningCount === service.desiredCount) {
      return { healthy: true, message: 'O serviço está ativo e saudável.' };
    } else if (service.runningCount < service.desiredCount) {
      return {
        healthy: false,
        message: 'O serviço está inicializando. Verifique os logs da tarefa no ECS se o problema persistir.',
      };
    } else if (['DRAINING', 'PROVISIONING'].includes(service.status)) {
      return {
        healthy: false,
        message: `O serviço está em estado: ${service.status}. Consulte os logs da tarefa no ECS.`,
      };
    } else {
      return {
        healthy: false,
        message: `Status do serviço: ${service.status}. Verifique os logs da tarefa no ECS.`,
      };
    }
  } catch (err) {
    console.error('Erro ao verificar status do serviço:', err);
    return { healthy: false, message: `Erro: ${err.message}` };
  }
}

/**
 * Função principal de implantação.
 * Realiza as seguintes etapas:
 * - Cria/atualiza grupo de logs, target group e regra no ALB.
 * - Cria ou atualiza o serviço ECS.
 * - Cria registro DNS no Route53 apontando para o ALB, se possível.
 */
export async function deployServices(config) {
  const {
    ecsClient,
    elbClient,
    logsClient,
    route53Client,
    selectedTasks,
    selectedCluster,
    capacityProvider,
    selectedLB,
    listenerArn,
    selectedZone,
    healthCheckPath,
    desiredCount,
    environment,
    logGroupNames,
    progressCallback,
  } = config;

  const zoneName = selectedZone && (selectedZone.Name || selectedZone.name);
  if (!zoneName) {
    throw new Error('Zona hospedada não foi selecionada.');
  }

  const logs = [];
  const results = [];
  const logMessage = (msg) => {
    logs.push(msg);
    progressCallback?.(msg);
  };

  for (const task of selectedTasks) {
    try {
      logMessage(`Iniciando implantação para a tarefa ${task}`);

      // 1. Descrever a definição da tarefa
      const descData = await ecsClient.send(
        new DescribeTaskDefinitionCommand({ taskDefinition: task })
      );
      const containerDef = descData.taskDefinition?.containerDefinitions?.[0];
      if (!containerDef) {
        throw new Error('Definição de container não encontrada.');
      }
      const containerName = containerDef.name;
      const containerPort = containerDef.portMappings?.[0]?.containerPort;

      // 2. Criar grupo de logs (ignorar se já existir)
      const logGrpName = logGroupNames[task] || `/ecs/${environment}/${task.split('/').pop().split(':')[0]}`;
      logMessage(`Verificando grupo de logs ${logGrpName}`);
      try {
        await logsClient.send(new CreateLogGroupCommand({ logGroupName: logGrpName }));
        logMessage(`Grupo de logs ${logGrpName} criado com sucesso.`);
      } catch (err) {
        logMessage(`Grupo de logs ${logGrpName} já existe ou não foi necessário criá-lo.`);
      }

      // 3. Criar ou obter Target Group
      let targetGroupArn = '';
      const tgName = `ecs-${task.split('/').pop().split(':')[0]}`.substring(0, 32);
      logMessage(`Verificando Target Group ${tgName}`);
      try {
        const tgData = await elbClient.send(
          new DescribeTargetGroupsCommand({ Names: [tgName] })
        );
        targetGroupArn = tgData.TargetGroups?.[0]?.TargetGroupArn;
        logMessage(`Target Group ${tgName} encontrado.`);
      } catch (err) {
        logMessage(`Target Group ${tgName} não encontrado. Criando...`);
        const tgCreateData = await elbClient.send(
          new CreateTargetGroupCommand({
            Name: tgName,
            Protocol: 'HTTP',
            Port: 80,
            VpcId: selectedLB.VpcId,
            HealthCheckProtocol: 'HTTP',
            HealthCheckPath: healthCheckPath,
            HealthCheckIntervalSeconds: 30,
            HealthCheckTimeoutSeconds: 5,
            HealthyThresholdCount: 2,
            UnhealthyThresholdCount: 2,
            TargetType: 'instance',
          })
        );
        targetGroupArn = tgCreateData.TargetGroups?.[0]?.TargetGroupArn;
        logMessage(`Target Group ${tgName} criado.`);
      }

      // 4. Criar ou atualizar regra no ALB
      let ruleArn = '';
      const domainName = `${task.split('/').pop().split(':')[0]}.${zoneName}`.replace(/\.$/, '');
      logMessage(`Verificando regras no ALB para o domínio ${domainName}`);
      try {
        const rulesData = await elbClient.send(
          new DescribeRulesCommand({ ListenerArn: listenerArn })
        );
        const existingRule = rulesData.Rules?.find((rule) => {
          const hostHeaderCond = rule.Conditions?.find((c) => c.Field === 'host-header');
          const pathPatternCond = rule.Conditions?.find((c) => c.Field === 'path-pattern');
          if (hostHeaderCond && pathPatternCond) {
            const hostValues = hostHeaderCond.HostHeaderConfig.Values;
            const pathValues = pathPatternCond.PathPatternConfig.Values;
            return hostValues.includes(domainName) && pathValues.includes('/*');
          }
          return false;
        });

        if (existingRule) {
          ruleArn = existingRule.RuleArn;
          logMessage(`Regra existente encontrada: ${ruleArn}`);
        } else {
          const existingPriorities = (rulesData.Rules || [])
            .map((r) => parseInt(r.Priority))
            .filter((num) => !isNaN(num));
          const nextPriority = existingPriorities.length > 0 ? Math.max(...existingPriorities) + 1 : 1;
          logMessage(`Criando nova regra com prioridade ${nextPriority}`);
          const ruleData = await elbClient.send(
            new CreateRuleCommand({
              ListenerArn: listenerArn,
              Conditions: [
                { Field: 'host-header', HostHeaderConfig: { Values: [domainName] } },
                { Field: 'path-pattern', PathPatternConfig: { Values: ['/*'] } },
              ],
              Actions: [
                {
                  Type: 'forward',
                  ForwardConfig: { TargetGroups: [{ TargetGroupArn: targetGroupArn, Weight: 1 }] },
                },
              ],
              Priority: nextPriority.toString(),
            })
          );
          ruleArn = ruleData.Rules?.[0]?.RuleArn;
          logMessage(`Regra criada com sucesso: ${ruleArn}`);
        }
      } catch (err) {
        logMessage(`Erro ao criar ou atualizar a regra: ${err.message}`);
        console.error('Erro ao criar ou atualizar a regra:', err);
      }

      // 4.1 Tentar criar registro no Route53 apontando para o ALB
      try {
        if (route53Client && selectedZone.Id && selectedLB.DNSName && selectedLB.CanonicalHostedZoneId) {
          await createRoute53Record(
            route53Client,
            selectedZone.Id,
            domainName,
            selectedLB.DNSName,
            selectedLB.CanonicalHostedZoneId,
            logMessage
          );
        } else {
          logMessage("Dados insuficientes para criar registro Route53. Registro não será criado.");
        }
      } catch (err) {
        logMessage(`Erro ao criar registro no Route53: ${err.message}`);
        console.error('Erro ao criar registro no Route53:', err);
      }

      // 5. Criar ou atualizar o serviço ECS
      let serviceArn = '';
      const serviceName = task.split('/').pop().split(':')[0];
      logMessage(`Verificando serviço ECS ${serviceName}`);
      try {
        const describeServicesResponse = await ecsClient.send(
          new DescribeServicesCommand({ cluster: selectedCluster, services: [serviceName] })
        );
        const describedService = describeServicesResponse.services?.[0];
        const serviceExists = describedService && describedService.status && describedService.status !== 'INACTIVE';

        if (serviceExists) {
          if (describedService.status !== 'ACTIVE') {
            logMessage(`Serviço ${serviceName} está em status ${describedService.status}. Aguardando ACTIVE...`);
            await waitForServiceActive(ecsClient, selectedCluster, serviceName, 10, 5000, progressCallback, logMessage);
          }
          logMessage(`Serviço ${serviceName} já existe. Atualizando...`);
          try {
            const updateServiceResponse = await ecsClient.send(
              new UpdateServiceCommand({
                cluster: selectedCluster,
                service: serviceName,
                taskDefinition: task,
                desiredCount,
                capacityProviderStrategy: [{ capacityProvider, weight: 1, base: 0 }],
                forceNewDeployment: true,
              })
            );
            serviceArn = updateServiceResponse.service?.serviceArn;
            logMessage(`Serviço ${serviceName} atualizado com sucesso.`);
          } catch (err) {
            if (err.name === 'ServiceNotActiveException') {
              logMessage(`Serviço ${serviceName} não está ACTIVE. Aguardando...`);
              await waitForServiceActive(ecsClient, selectedCluster, serviceName, 10, 5000, progressCallback, logMessage);
              const updateAgainResponse = await ecsClient.send(
                new UpdateServiceCommand({
                  cluster: selectedCluster,
                  service: serviceName,
                  taskDefinition: task,
                  desiredCount,
                  capacityProviderStrategy: [{ capacityProvider, weight: 1, base: 0 }],
                  forceNewDeployment: true,
                })
              );
              serviceArn = updateAgainResponse.service?.serviceArn;
              logMessage(`Serviço ${serviceName} atualizado com sucesso após aguardar ACTIVE.`);
            } else {
              logMessage(`Erro ao atualizar serviço ${serviceName}: ${err.message}`);
              console.error('Erro ao atualizar serviço:', err);
            }
          }
        } else {
          logMessage(`Serviço ${serviceName} não existe. Criando...`);
          const createServiceCmd = new CreateServiceCommand({
            cluster: selectedCluster,
            serviceName: serviceName,
            taskDefinition: task,
            desiredCount,
            capacityProviderStrategy: [{ capacityProvider, weight: 1, base: 0 }],
            loadBalancers: [
              {
                targetGroupArn,
                containerName,
                containerPort,
              },
            ],
            deploymentConfiguration: { maximumPercent: 200, minimumHealthyPercent: 100 },
            enableECSManagedTags: true,
            enableExecuteCommand: true,
            propagateTags: 'TASK_DEFINITION',
          });
          const serviceData = await ecsClient.send(createServiceCmd);
          serviceArn = serviceData.service?.serviceArn;
          logMessage(`Serviço ${serviceName} criado com sucesso.`);
        }
      } catch (err) {
        logMessage(`Erro ao criar ou atualizar o serviço ${serviceName}: ${err.message}`);
        console.error('Erro ao criar ou atualizar o serviço:', err);
      }

      results.push({ task, serviceArn });
    } catch (err) {
      logMessage(`Erro ao implantar serviço para ${task}: ${err.message}`);
      console.error(`Erro ao implantar serviço para ${task}:`, err);
      results.push({ task, error: err.message });
    }
  }

  logMessage('Implantação concluída.');
  return { results, logs };
}
