// utils/ecrFunctions.js
import { DescribeRepositoriesCommand, CreateRepositoryCommand } from '@aws-sdk/client-ecr';
import { STSClient, GetCallerIdentityCommand } from '@aws-sdk/client-sts';

/**
 * Cria ou obtém o repositório ECR na região us-east-1.
 * Se o repositório existir, retorna-o; caso contrário, cria-o com
 * imageTagMutability "IMMUTABLE" e encryptionConfiguration AES256.
 */
export async function createOrGetECRRepository(ecrClient, repositoryName) {
  if (!ecrClient) {
    throw new Error("Cliente ECR não está disponível. Verifique suas credenciais e região us-east-1.");
  }

  // (Opcional) Verifica a conta via STS, para confirmar credenciais
  try {
    const stsClient = new STSClient({
      region: 'us-east-1',
      credentials: ecrClient.config.credentials,
    });
    const identityData = await stsClient.send(new GetCallerIdentityCommand({}));
    console.log(`Conectado à conta AWS: ${identityData.Account} (us-east-1)`);
  } catch (stsErr) {
    throw new Error(`Erro ao obter identidade AWS: ${stsErr.message}`);
  }

  try {
    // 1. Tenta descrever o repositório
    const describeCmd = new DescribeRepositoriesCommand({
      repositoryNames: [repositoryName],
    });
    const describeData = await ecrClient.send(describeCmd);

    // Se encontrar o repositório, retorna
    if (describeData.repositories && describeData.repositories.length > 0) {
      console.log(`Repositório "${repositoryName}" já existia.`);
      return { repo: describeData.repositories[0], created: false };
    }
  } catch (err) {
    // 2. Se o repositório não existir, criamos
    if (err.name === 'RepositoryNotFoundException') {
      try {
        const createCmd = new CreateRepositoryCommand({
          repositoryName,
          imageTagMutability: 'IMMUTABLE',
          encryptionConfiguration: { encryptionType: 'AES256' },
        });
        const createData = await ecrClient.send(createCmd);

        console.log(`Repositório "${repositoryName}" criado com sucesso na região us-east-1.`);
        return { repo: createData.repository, created: true };
      } catch (createErr) {
        console.error(`Erro ao criar o repositório ECR "${repositoryName}":`, createErr);
        throw new Error(`Erro ao criar o repositório ECR: ${createErr.message}`);
      }
    }
    // Se for outro erro que não "RepositoryNotFoundException"
    throw new Error(`Erro ao obter ou criar repositório ECR: ${err.message}`);
  }

  // Caso improvável
  throw new Error(`Não foi possível encontrar ou criar o repositório "${repositoryName}". Motivo desconhecido.`);
}
