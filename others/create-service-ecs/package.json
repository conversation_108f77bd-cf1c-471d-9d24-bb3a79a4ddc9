{"name": "my-ecs-deployment-app", "version": "1.0.0", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "export": "next build && next export"}, "dependencies": {"@aws-sdk/client-cloudwatch-logs": "^3.x.x", "@aws-sdk/client-ecr": "^3.741.0", "@aws-sdk/client-ecs": "^3.x.x", "@aws-sdk/client-elastic-load-balancing-v2": "^3.x.x", "@aws-sdk/client-kms": "^3.750.0", "@aws-sdk/client-route-53": "^3.x.x", "@aws-sdk/client-sts": "^3.741.0", "next": "latest", "react": "latest", "react-dom": "latest", "reactjs-popup": "^2.0.6"}, "devDependencies": {"autoprefixer": "^10.x.x", "postcss": "^8.x.x", "tailwindcss": "^3.x.x"}}