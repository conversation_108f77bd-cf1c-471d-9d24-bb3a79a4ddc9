/* ============================
   Variáveis de Cores
   ============================ */
:root {
  --bg-primary: #0d1117;
  --bg-secondary: #161b22;
  --bg-tertiary: #21262d;
  --text-primary: #c9d1d9;
  --text-secondary: #8b949e;
  --accent-primary: #238636;
  --accent-secondary: #2ea043;
  --border-color: #30363d;
  --input-bg: #0d1117;
  --hover-bg: #1f2937;
  --error-color: #f85149;
  --success-color: #238636;
  --quantum-glow: rgba(35, 134, 54, 0.1);
  --quantum-pulse: rgba(35, 134, 54, 0.2);
}

/* ============================
   Animações Globais
   ============================ */
@keyframes quantumFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes quantumPulse {
  0% {
    box-shadow: 0 0 0 0 var(--quantum-pulse);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(35, 134, 54, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(35, 134, 54, 0);
  }
}

@keyframes quantumGlow {
  0% {
    box-shadow: 0 0 5px var(--quantum-glow);
  }
  50% {
    box-shadow: 0 0 20px var(--quantum-glow);
  }
  100% {
    box-shadow: 0 0 5px var(--quantum-glow);
  }
}

@keyframes spin {
  0% { 
    transform: rotate(0deg); 
  }
  100% { 
    transform: rotate(360deg); 
  }
}

/* ============================
   Título Principal
   ============================ */
.page-title {
  font-size: 1.8rem;
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  text-align: center;
}

.page-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: var(--accent-primary);
  border-radius: 3px;
  animation: quantumGlow 2s infinite;
}

/* ============================
   Estilos para Popups e Modais
   ============================ */
.popup-overlay {
  background: rgba(0, 0, 0, 0.8) !important;
}

.popup-content {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  background: none !important;
  border: none !important;
}

.modal-card {
  background: var(--bg-secondary);
  max-width: 400px;
  width: 90vw;
  max-height: 80vh;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 20px 30px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  text-align: center;
  margin: 0 auto;
  position: relative;
  animation: quantumFadeIn 0.5s ease-out;
  transition: all 0.3s ease-in-out;
}

.modal-card:hover {
  box-shadow: 0 0 20px var(--quantum-glow);
}

/* ============================
   Estilos Gerais de Layout
   ============================ */
body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  margin: 0;
  padding: 0;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.container {
  position: relative;
  max-width: 400px;
  width: 90%;
  background: var(--bg-secondary);
  padding: 2rem;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  text-align: center;
  animation: quantumFadeIn 0.5s ease-out;
  transition: all 0.3s ease-in-out;
  display: flex;
  flex-direction: column;
  align-items: center;
}

h1 {
  font-size: 2rem;
  margin-bottom: 2rem;
  width: 100%;
  text-align: center;
}

form {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
}

.form-group {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0.5rem;
}

.form-group label {
  font-size: 1rem;
  color: var(--text-primary);
  width: 100%;
  text-align: left;
}

.dropdown, 
input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-size: 1rem;
  background: var(--input-bg);
  color: var(--text-primary);
  transition: all 0.3s ease-in-out;
  box-sizing: border-box;
}

.dropdown:focus, input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 15px var(--quantum-glow);
}

/* ============================
   Botões e Ações
   ============================ */
.btn {
  width: 100%;
  padding: 0.75rem;
  margin-top: 1rem;
  border: none;
  border-radius: 6px;
  background: var(--accent-primary);
  color: white;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
}

.btn:hover {
  background: var(--accent-secondary);
}

.btn:active {
  transform: translateY(0);
}

.btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%);
  transform-origin: 50% 50%;
}

.btn:focus:not(:active)::after {
  animation: quantumPulse 1s ease-out;
}

.btn-back {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  font-size: 14px;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  border: 1px solid var(--border-color);
  margin-bottom: 10px;
  transition: background-color 0.2s ease-in-out;
}

.btn-back:hover {
  background: var(--hover-bg);
}

.button-group {
  display: flex;
  justify-content: center;
  gap: 0.75rem;
  margin-top: 1rem;
}

.button-group .btn {
  padding: 0.6rem 1.25rem;
  font-size: 0.9rem;
  min-width: 130px;
}

/* ============================
   Indicador de Progresso
   ============================ */
.step-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.step-circle {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  border-radius: 50%;
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease-in-out;
  position: relative;
}

.step-circle.active {
  background: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
  animation: quantumPulse 2s infinite;
}

.step-circle.completed {
  background: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
  animation: quantumGlow 2s infinite;
}

/* ============================
   Outros Componentes
   ============================ */
.log-container {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 1rem;
  margin: 1rem 0;
  max-height: 180px;
  overflow-y: auto;
}

.log-item {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 0.85rem;
  margin: 0.25rem 0;
  color: var(--text-primary);
  white-space: pre-wrap;
  word-wrap: break-word;
}

.arn-wrap {
  display: block;
  white-space: normal;
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
  text-align: center;
  padding: 10px;
  max-width: 100%;
  color: var(--text-secondary);
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 12px;
}

/* ============================
   Spinner de Carregamento
   ============================ */
.spinner {
  position: relative;
  width: 40px;
  height: 40px;
  margin: 20px auto;
}

.spinner::before,
.spinner::after {
  content: '';
  position: absolute;
  border-radius: 50%;
}

.spinner::before {
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, var(--accent-primary), var(--accent-secondary));
  animation: spin 1s linear infinite;
}

.spinner::after {
  width: 80%;
  height: 80%;
  background: var(--bg-secondary);
  top: 10%;
  left: 10%;
  border-radius: 50%;
}

/* ============================
   Mensagens de Status
   ============================ */
.error-msg {
  color: var(--error-color);
  background: rgba(248, 81, 73, 0.1);
  padding: 10px;
  border-radius: 6px;
  margin: 10px 0;
  font-size: 14px;
  animation: quantumFadeIn 0.3s ease-out;
  transition: all 0.3s ease-in-out;
}

.error-msg:hover {
  transform: translateX(5px);
}

.success-box {
  background: rgba(35, 134, 54, 0.1);
  border: 1px solid var(--accent-primary);
  border-radius: 6px;
  padding: 15px;
  margin: 15px 0;
  animation: quantumFadeIn 0.3s ease-out;
  transition: all 0.3s ease-in-out;
}

.success-box:hover {
  transform: translateX(5px);
  box-shadow: 0 0 20px var(--quantum-glow);
}

.success-box p {
  color: var(--accent-primary);
  margin: 0;
}

/* ============================
   Scrollbar Personalizada
   ============================ */
::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: var(--accent-primary);
  border-radius: 6px;
  border: 3px solid var(--bg-tertiary);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-secondary);
}

/* ===== StepFileEncryption Layout ===== */

/* Ajusta o container para dar espaço extra em telas grandes */
.encryption-container {
  max-width: 500px;
  margin: 30px auto;
}

/* Form principal do passo de criptografia */
.encryption-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Informações do arquivo */
.file-info {
  margin-top: 5px;
  font-size: 14px;
  color: #666;
}

.file-size {
  color: #999;
}

/* Grupo da operação (Criptografar/Descriptografar) */
.operation-group .radio-group {
  display: flex;
  gap: 15px;
  margin-top: 5px;
}

.radio-group label {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
}

/* ============================
   Responsividade
   ============================ */
@media (max-width: 640px) {
  .card {
    padding: 1.5rem;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .button-group {
    flex-direction: column;
  }

  .button-group .btn {
    width: 100%;
  }

  .summary-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .container {
    width: 90%;
    padding: 1.25rem;
  }
  
  h1 {
    font-size: 1.75rem;
    margin-bottom: 1.5rem;
  }
  
  .form-group {
    gap: 0.4rem;
  }

  .summary-cards {
    grid-template-columns: 1fr;
    max-width: 280px;
  }
  
  .summary-card {
    min-height: 45px;
  }
  
  .card-title {
    font-size: 0.9rem;
  }
}

@media (max-width: 320px) {
  .container {
    width: 95%;
    padding: 1rem;
  }
  
  h1 {
    font-size: 1.5rem;
    margin-bottom: 1.25rem;
  }
  
  .form-group label {
    font-size: 0.9rem;
  }
  
  .dropdown, 
  input {
    padding: 0.6rem;
    font-size: 0.9rem;
  }
  
  .btn {
    padding: 0.6rem;
    font-size: 0.9rem;
  }
}

/* ============================
   Loading Animations
   ============================ */
@keyframes shimmerEffect {
  0% {
    background-position: -1000px 0;
  }
  100% {
    background-position: 1000px 0;
  }
}

@keyframes pulseGlow {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes breathe {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
}

.skeleton-loader {
  background: linear-gradient(
    90deg,
    var(--bg-tertiary) 0px,
    var(--bg-secondary) 40px,
    var(--bg-tertiary) 80px
  );
  background-size: 1000px 100%;
  animation: shimmerEffect 2s infinite linear;
  border-radius: 6px;
  min-height: 40px;
  margin: 8px 0;
  position: relative;
  overflow: hidden;
}

.skeleton-loader::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  transform: translateX(-100%);
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.08),
    transparent
  );
  animation: shimmerEffect 2s infinite;
}

.task-definition-loading {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  background: var(--bg-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.task-definition-loading .skeleton-line {
  height: 20px;
  background: linear-gradient(
    90deg,
    var(--bg-tertiary) 0px,
    var(--bg-secondary) 40px,
    var(--bg-tertiary) 80px
  );
  background-size: 1000px 100%;
  animation: shimmerEffect 2s infinite linear;
  border-radius: 4px;
}

.task-definition-loading .skeleton-line:nth-child(2) {
  width: 85%;
}

.task-definition-loading .skeleton-line:nth-child(3) {
  width: 70%;
}

.loading-pulse {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px;
  background: var(--bg-secondary);
  border-radius: 8px;
  animation: breathe 2s ease-in-out infinite;
}

.loading-pulse .dot {
  width: 8px;
  height: 8px;
  background: var(--accent-primary);
  border-radius: 50%;
  animation: pulseGlow 1.4s ease-in-out infinite;
}

.loading-pulse .dot:nth-child(2) {
  animation-delay: 0.2s;
}

.loading-pulse .dot:nth-child(3) {
  animation-delay: 0.4s;
}

.loading-container {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-text {
  display: flex;
  align-items: center;
  gap: 12px;
  color: var(--text-secondary);
  font-size: 0.95rem;
  background: var(--bg-secondary);
  padding: 10px 20px;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.loading-text::before {
  content: '';
  width: 16px;
  height: 16px;
  border: 2px solid var(--accent-primary);
  border-right-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-progress {
  width: 100%;
  max-width: 300px;
  height: 4px;
  background: var(--bg-tertiary);
  border-radius: 2px;
  overflow: hidden;
  position: relative;
}

.loading-progress::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 30%;
  background: var(--accent-primary);
  border-radius: 2px;
  animation: progressBar 2s ease-in-out infinite;
}

@keyframes progressBar {
  0% {
    left: -30%;
  }
  100% {
    left: 100%;
  }
}

.loading-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 16px;
  width: 100%;
  max-width: 400px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.loading-card .skeleton-header {
  height: 24px;
  width: 60%;
  margin-bottom: 8px;
}

.loading-card .skeleton-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.loading-card .skeleton-line {
  height: 16px;
  width: 100%;
}

.loading-card .skeleton-line:last-child {
  width: 80%;
}

/* Loading Button State */
.btn.loading {
  position: relative;
  cursor: wait;
  overflow: hidden;
}

.btn.loading::after {
  content: '';
  display: inline-block;
  vertical-align: middle;
  width: 16px;
  height: 16px;
  margin-left: 8px;
  border: 2px solid rgba(255, 255, 255, 0.8);
  border-right-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Ajustes para o StepResult */
.card {
  background: var(--bg-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  padding: 2rem;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  box-sizing: border-box;
}

.service-info {
  background: var(--bg-tertiary);
  border-radius: 6px;
  padding: 1rem;
  margin: 1rem 0;
}

.service-info h3 {
  font-size: 1.2rem;
  margin: 0 0 1rem 0;
  color: var(--text-primary);
}

.service-url {
  display: block;
  margin-top: 0.5rem;
  font-size: 0.9rem;
}

.service-url a {
  color: var(--accent-primary);
  text-decoration: none;
  word-break: break-all;
}

.service-url a:hover {
  text-decoration: underline;
}

/* Estilos para o Resumo da Implantação */
.deployment-summary {
  background: var(--bg-secondary);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1rem;
}

.summary-section {
  margin-bottom: 1.5rem;
}

.summary-section:last-child {
  margin-bottom: 0;
}

.summary-title {
  font-size: 1.5rem;
  color: var(--text-primary);
  margin-bottom: 1rem;
  text-align: center;
  font-weight: 600;
}

.summary-subtitle {
  font-size: 1.2rem;
  color: var(--text-primary);
  margin: 1.5rem 0 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border-color);
}

.summary-item {
  background: var(--bg-tertiary);
  border-radius: 6px;
  padding: 0.75rem 1rem;
  margin-bottom: 0.75rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.summary-label {
  color: var(--text-secondary);
  font-size: 0.9rem;
  display: block;
}

.summary-value {
  color: var(--text-primary);
  font-size: 0.95rem;
  word-break: break-all;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  display: block;
  line-height: 1.4;
}

.task-definition {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  list-style: none;
  padding: 0;
  margin: 0;
}

.task-definition-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.summary-url {
  color: var(--accent-primary);
  text-decoration: none;
  display: block;
  margin-top: 0.5rem;
  font-size: 0.9rem;
}

.summary-url:hover {
  text-decoration: underline;
}

.summary-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.deployment-config {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  background: var(--bg-tertiary);
  border-radius: 6px;
  padding: 1rem;
}

.config-item {
  text-align: center;
}

.config-label {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.config-value {
  color: var(--text-primary);
  font-size: 1rem;
  font-weight: 500;
}

/* Cards de Resumo */
.summary-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.25rem;
  margin: 1.5rem auto;
  max-width: 700px;
  padding: 0 1rem;
}

.summary-card {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 1.25rem 1rem;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  position: relative;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.summary-card:hover {
  transform: translateY(-2px);
  border-color: var(--accent-primary);
  background: var(--bg-secondary);
}

.card-content {
  position: relative;
  width: 100%;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 40px;
}

.card-title {
  font-size: 1.15rem;
  font-weight: 500;
  color: var(--text-primary);
  transition: opacity 0.2s ease-in-out;
  line-height: 1.3;
  margin: 0;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.card-description {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  font-size: 1rem;
  color: var(--text-secondary);
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
  padding: 0 0.75rem;
  pointer-events: none;
  line-height: 1.4;
  text-align: center;
}

.summary-card:hover .card-title {
  opacity: 0;
}

.summary-card:hover .card-description {
  opacity: 1;
}

/* Status Badge */
.status-badge {
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 1.2rem;
  line-height: 1;
  z-index: 2;
}

.status-badge.success {
  color: var(--accent-primary);
}

.status-badge.error {
  color: var(--error-color);
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease-out;
}

.modal-content {
  background: var(--bg-secondary);
  border-radius: 12px;
  padding: 1.5rem;
  width: 90%;
  max-width: 600px;
  max-height: 85vh;
  overflow-y: auto;
  position: relative;
  animation: slideUp 0.3s ease-out;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.modal-title {
  font-size: 1.4rem;
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.modal-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.5rem;
  font-size: 1.5rem;
  line-height: 1;
  transition: all 0.2s;
  border-radius: 50%;
}

.modal-close:hover {
  color: var(--text-primary);
  background: var(--bg-tertiary);
}

.modal-body {
  margin-bottom: 1.5rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

/* Animações */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Status Badge */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-badge.success {
  background: rgba(35, 134, 54, 0.15);
  color: #2ea043;
}

.status-badge.warning {
  background: rgba(201, 157, 38, 0.15);
  color: #d29922;
}

.status-badge.error {
  background: rgba(248, 81, 73, 0.15);
  color: #f85149;
}

/* Responsividade */
@media (max-width: 900px) {
  .summary-cards {
    max-width: 700px;
  }
}

@media (max-width: 640px) {
  .summary-cards {
    grid-template-columns: 1fr;
    max-width: 400px;
    gap: 1rem;
  }

  .summary-card {
    min-height: 55px;
    padding: 1.25rem 1rem;
  }
  
  .card-content {
    min-height: 35px;
  }
}

@media (max-width: 480px) {
  .summary-cards {
    max-width: 320px;
    gap: 0.875rem;
    margin: 1.25rem auto;
  }
  
  .summary-card {
    min-height: 50px;
    padding: 1rem;
  }
  
  .card-content {
    min-height: 30px;
  }

  .status-badge {
    font-size: 1.1rem;
    bottom: 8px;
  }
}

/* ============================
   Circular Progress Animation
   ============================ */
.deployment-progress {
  position: relative;
  width: 160px;
  height: 160px;
  margin: 30px auto;
  transform: rotate(-90deg);
}

.progress-circle {
  fill: none;
  stroke-width: 8;
}

.progress-circle-bg {
  stroke: var(--bg-tertiary);
}

.progress-circle-path {
  stroke: var(--accent-primary);
  stroke-linecap: round;
  stroke-dasharray: 452; /* 2 * π * (radius = 72) */
  stroke-dashoffset: 452;
  transition: stroke-dashoffset 0.5s ease;
  filter: drop-shadow(0 0 4px var(--accent-primary));
}

.progress-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(90deg);
  text-align: center;
  width: 100%;
  color: var(--text-primary);
}

/* Animação do círculo de progresso */
@keyframes circlePulse {
  0%, 100% {
    stroke-width: 8;
    filter: drop-shadow(0 0 4px var(--accent-primary));
  }
  50% {
    stroke-width: 10;
    filter: drop-shadow(0 0 8px var(--accent-primary));
  }
}

/* Animação do ponto de progresso */
@keyframes progressDot {
  0%, 100% {
    stroke-width: 0;
    opacity: 0;
  }
  50% {
    stroke-width: 12;
    opacity: 1;
  }
}

.progress-dot {
  fill: var(--accent-primary);
  r: 4;
  opacity: 0;
  filter: drop-shadow(0 0 4px var(--accent-primary));
  animation: progressDot 2s ease-in-out infinite;
}

/* Container do progresso com etapas */
.progress-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

/* Template do SVG */
.progress-svg {
  width: 160px;
  height: 160px;
}

/* Etapas do progresso */
.progress-steps {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

.progress-step {
  position: absolute;
  width: 12px;
  height: 12px;
  background: var(--bg-tertiary);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.progress-step.completed {
  background: var(--accent-primary);
  box-shadow: 0 0 10px var(--accent-primary);
}

.progress-step.active {
  background: var(--accent-primary);
  animation: stepPulse 1.5s ease-in-out infinite;
}

@keyframes stepPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 10px var(--accent-primary);
  }
  50% {
    transform: scale(1.2);
    box-shadow: 0 0 20px var(--accent-primary);
  }
}

/* Texto central */
.progress-label {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1.1rem;
  color: var(--text-primary);
  text-align: center;
  width: 80%;
}

/* Container do progresso */
.deployment-status-container {
  position: relative;
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

/* HTML Template para referência:
<div class="deployment-status-container">
  <div class="progress-container">
    <svg class="progress-svg" viewBox="0 0 160 160">
      <circle class="progress-circle progress-circle-bg"
              cx="80" cy="80" r="72"
              stroke-width="8"/>
      <circle class="progress-circle progress-circle-path"
              cx="80" cy="80" r="72"/>
      <circle class="progress-dot"
              cx="80" cy="8" r="4"/>
    </svg>
    <div class="progress-label">
      Implantação em Andamento
    </div>
  </div>
</div>
*/

/* Animação suave para transição entre etapas */
.progress-circle-path {
  animation: circlePulse 2s ease-in-out infinite;
}

/* Estado de conclusão */
.progress-container.completed .progress-circle-path {
  stroke: var(--success-color);
  animation: none;
  transition: all 0.5s ease;
}

.progress-container.completed .progress-dot {
  fill: var(--success-color);
  animation: none;
}

/* Estado de erro */
.progress-container.error .progress-circle-path {
  stroke: var(--error-color);
  animation: none;
}

.progress-container.error .progress-dot {
  fill: var(--error-color);
  animation: none;
}

/* ============================
   Deployment Animation
   ============================ */
.deployment-animation-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 2rem;
}

.cloud-container {
  position: relative;
  width: 300px;
  height: 200px;
  margin-bottom: 2rem;
}

.cloud {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 100px;
  background: #f0f0f0;
  border-radius: 50px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  animation: cloudGlow 2s ease-in-out infinite;
}

.cloud:before {
  content: '';
  position: absolute;
  top: -30px;
  left: 40px;
  width: 80px;
  height: 80px;
  background: #f0f0f0;
  border-radius: 50%;
}

.cloud:after {
  content: '';
  position: absolute;
  top: -20px;
  right: 40px;
  width: 60px;
  height: 60px;
  background: #f0f0f0;
  border-radius: 50%;
}

.cloud-success {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 100px;
  border-radius: 50px;
  opacity: 0;
  background: radial-gradient(circle, rgba(76, 217, 100, 0.2), transparent);
  animation: successGlow 2s ease-in-out infinite;
}

.app-component {
  position: absolute;
  width: 30px;
  height: 30px;
  background: #4a90e2;
  border-radius: 6px;
  opacity: 0;
}

.app-component:nth-child(3) {
  top: 80%;
  left: 10%;
  animation: moveToCloud 1.5s ease-in forwards;
}

.app-component:nth-child(4) {
  top: 80%;
  left: 45%;
  animation: moveToCloud 1.5s ease-in 0.5s forwards;
}

.app-component:nth-child(5) {
  top: 80%;
  left: 80%;
  animation: moveToCloud 1.5s ease-in 1s forwards;
}

.deployment-message {
  margin-top: 2rem;
  font-size: 1.2rem;
  color: #333;
  text-align: center;
  opacity: 0;
  animation: fadeIn 1s ease-out forwards;
}

@keyframes cloudGlow {
  0%, 100% {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow: 0 8px 24px rgba(74, 144, 226, 0.3);
  }
}

@keyframes successGlow {
  0%, 100% {
    opacity: 0;
  }
  50% {
    opacity: 0.6;
  }
}

@keyframes moveToCloud {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(-100px);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
  
  