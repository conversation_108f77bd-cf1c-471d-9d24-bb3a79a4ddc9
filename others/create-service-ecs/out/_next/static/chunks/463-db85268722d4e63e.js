(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[463],{5071:(e,t,r)=>{"use strict";r.d(t,{I:()=>y});var n=r(6970),s={name:"SHA-256"},i={name:"<PERSON><PERSON>",hash:s},o=new Uint8Array([227,176,196,66,152,252,28,20,154,251,244,200,153,111,185,36,39,174,65,228,100,155,147,76,164,149,153,27,120,82,184,85]);let a={};function c(){return"undefined"!=typeof window?window:"undefined"!=typeof self?self:a}var u=function(){function e(e){this.toHash=new Uint8Array(0),this.secret=e,this.reset()}return e.prototype.update=function(e){if(!(0,n.Kz)(e)){var t=(0,n.pP)(e),r=new Uint8Array(this.toHash.byteLength+t.byteLength);r.set(this.toHash,0),r.set(t,this.toHash.byteLength),this.toHash=r}},e.prototype.digest=function(){var e=this;return this.key?this.key.then(function(t){return c().crypto.subtle.sign(i,t,e.toHash).then(function(e){return new Uint8Array(e)})}):(0,n.Kz)(this.toHash)?Promise.resolve(o):Promise.resolve().then(function(){return c().crypto.subtle.digest(s,e.toHash)}).then(function(e){return Promise.resolve(new Uint8Array(e))})},e.prototype.reset=function(){var e=this;this.toHash=new Uint8Array(0),this.secret&&void 0!==this.secret&&(this.key=new Promise(function(t,r){c().crypto.subtle.importKey("raw",(0,n.pP)(e.secret),i,!1,["sign"]).then(t,r)}),this.key.catch(function(){}))},e}(),l=r(1635),d=new Uint32Array([0x428a2f98,0x71374491,0xb5c0fbcf,0xe9b5dba5,0x3956c25b,0x59f111f1,0x923f82a4,0xab1c5ed5,0xd807aa98,0x12835b01,0x243185be,0x550c7dc3,0x72be5d74,0x80deb1fe,0x9bdc06a7,0xc19bf174,0xe49b69c1,0xefbe4786,0xfc19dc6,0x240ca1cc,0x2de92c6f,0x4a7484aa,0x5cb0a9dc,0x76f988da,0x983e5152,0xa831c66d,0xb00327c8,0xbf597fc7,0xc6e00bf3,0xd5a79147,0x6ca6351,0x14292967,0x27b70a85,0x2e1b2138,0x4d2c6dfc,0x53380d13,0x650a7354,0x766a0abb,0x81c2c92e,0x92722c85,0xa2bfe8a1,0xa81a664b,0xc24b8b70,0xc76c51a3,0xd192e819,0xd6990624,0xf40e3585,0x106aa070,0x19a4c116,0x1e376c08,0x2748774c,0x34b0bcb5,0x391c0cb3,0x4ed8aa4a,0x5b9cca4f,0x682e6ff3,0x748f82ee,0x78a5636f,0x84c87814,0x8cc70208,0x90befffa,0xa4506ceb,0xbef9a3f7,0xc67178f2]),p=[0x6a09e667,0xbb67ae85,0x3c6ef372,0xa54ff53a,0x510e527f,0x9b05688c,0x1f83d9ab,0x5be0cd19],f=function(){function e(){this.state=Int32Array.from(p),this.temp=new Int32Array(64),this.buffer=new Uint8Array(64),this.bufferLength=0,this.bytesHashed=0,this.finished=!1}return e.prototype.update=function(e){if(this.finished)throw Error("Attempted to update an already finished hash.");var t=0,r=e.byteLength;if(this.bytesHashed+=r,8*this.bytesHashed>0x1fffffffffffff)throw Error("Cannot hash more than 2^53 - 1 bits");for(;r>0;)this.buffer[this.bufferLength++]=e[t++],r--,64===this.bufferLength&&(this.hashBuffer(),this.bufferLength=0)},e.prototype.digest=function(){if(!this.finished){var e=8*this.bytesHashed,t=new DataView(this.buffer.buffer,this.buffer.byteOffset,this.buffer.byteLength),r=this.bufferLength;if(t.setUint8(this.bufferLength++,128),r%64>=56){for(var n=this.bufferLength;n<64;n++)t.setUint8(n,0);this.hashBuffer(),this.bufferLength=0}for(var n=this.bufferLength;n<56;n++)t.setUint8(n,0);t.setUint32(56,Math.floor(e/0x100000000),!0),t.setUint32(60,e),this.hashBuffer(),this.finished=!0}for(var s=new Uint8Array(32),n=0;n<8;n++)s[4*n]=this.state[n]>>>24&255,s[4*n+1]=this.state[n]>>>16&255,s[4*n+2]=this.state[n]>>>8&255,s[4*n+3]=this.state[n]>>>0&255;return s},e.prototype.hashBuffer=function(){for(var e=this.buffer,t=this.state,r=t[0],n=t[1],s=t[2],i=t[3],o=t[4],a=t[5],c=t[6],u=t[7],l=0;l<64;l++){if(l<16)this.temp[l]=(255&e[4*l])<<24|(255&e[4*l+1])<<16|(255&e[4*l+2])<<8|255&e[4*l+3];else{var p=this.temp[l-2],f=(p>>>17|p<<15)^(p>>>19|p<<13)^p>>>10,h=((p=this.temp[l-15])>>>7|p<<25)^(p>>>18|p<<14)^p>>>3;this.temp[l]=(f+this.temp[l-7]|0)+(h+this.temp[l-16]|0)}var m=(((o>>>6|o<<26)^(o>>>11|o<<21)^(o>>>25|o<<7))+(o&a^~o&c)|0)+(u+(d[l]+this.temp[l]|0)|0)|0,y=((r>>>2|r<<30)^(r>>>13|r<<19)^(r>>>22|r<<10))+(r&n^r&s^n&s)|0;u=c,c=a,a=o,o=i+m|0,i=s,s=n,n=r,r=m+y|0}t[0]+=r,t[1]+=n,t[2]+=s,t[3]+=i,t[4]+=o,t[5]+=a,t[6]+=c,t[7]+=u},e}(),h=function(){function e(e){this.secret=e,this.hash=new f,this.reset()}return e.prototype.update=function(e){if(!(0,n.Kz)(e)&&!this.error)try{this.hash.update((0,n.pP)(e))}catch(e){this.error=e}},e.prototype.digestSync=function(){if(this.error)throw this.error;return this.outer?(this.outer.finished||this.outer.update(this.hash.digest()),this.outer.digest()):this.hash.digest()},e.prototype.digest=function(){return(0,l.sH)(this,void 0,void 0,function(){return(0,l.YH)(this,function(e){return[2,this.digestSync()]})})},e.prototype.reset=function(){if(this.hash=new f,this.secret){this.outer=new f;var e=function(e){var t=(0,n.pP)(e);if(t.byteLength>64){var r=new f;r.update(t),t=r.digest()}var s=new Uint8Array(64);return s.set(t),s}(this.secret),t=new Uint8Array(64);t.set(e);for(var r=0;r<64;r++)e[r]^=54,t[r]^=92;this.hash.update(e),this.outer.update(t);for(var r=0;r<e.byteLength;r++)e[r]=0}},e}(),m=["decrypt","digest","encrypt","exportKey","generateKey","importKey","sign","verify"],y=function(){function e(e){var t,r,n;"object"==typeof(r=t=c())&&"object"==typeof r.crypto&&"function"==typeof r.crypto.getRandomValues&&"object"==typeof t.crypto.subtle&&(n=t.crypto.subtle)&&m.every(function(e){return"function"==typeof n[e]})?this.hash=new u(e):this.hash=new h(e)}return e.prototype.update=function(e,t){this.hash.update((0,n.pP)(e))},e.prototype.digest=function(){return this.hash.digest()},e.prototype.reset=function(){this.hash.reset()},e}()},6970:(e,t,r)=>{"use strict";r.d(t,{pP:()=>i,Kz:()=>o,yl:()=>a,zB:()=>c});var n=r(2928).Buffer,s=void 0!==n&&n.from?function(e){return n.from(e,"utf8")}:e=>new TextEncoder().encode(e);function i(e){return e instanceof Uint8Array?e:"string"==typeof e?s(e):ArrayBuffer.isView(e)?new Uint8Array(e.buffer,e.byteOffset,e.byteLength/Uint8Array.BYTES_PER_ELEMENT):new Uint8Array(e)}function o(e){return"string"==typeof e?0===e.length:0===e.byteLength}function a(e){return new Uint8Array([(0xff000000&e)>>24,(0xff0000&e)>>16,(65280&e)>>8,255&e])}function c(e){if(!Uint32Array.from){for(var t=new Uint32Array(e.length),r=0;r<e.length;)t[r]=e[r],r+=1;return t}return Uint32Array.from(e)}},6906:(e,t,r)=>{"use strict";r.d(t,{T:()=>eK});var n,s=r(1095),i=r(5937),o=r(8377),a=r(1136),c=r(1651),u=r(4383);let l=e=>({...e,eventStreamMarshaller:e.eventStreamSerdeProvider(e)});var d=r(649),p=r(8176),f=r(6320),h=r(5268),m=r(7258),y=r(9848);let g=async(e,t,r)=>({operation:(0,y.u)(t).operation,region:await (0,y.t)(e.region)()||(()=>{throw Error("expected `region` to be configured for `aws.auth#sigv4`")})()}),x=e=>{let t=[];return e.operation,t.push({schemeId:"aws.auth#sigv4",signingProperties:{name:"logs",region:e.region},propertiesExtractor:(e,t)=>({signingProperties:{config:e,context:t}})}),t},b=e=>({...(0,m.h)(e)});var w=r(9987);let v={rE:"3.741.0"};var E=r(5071),S=r(9391),P=r(1635),O=r(6970);!function(){function e(){this.crc32=new $}e.prototype.update=function(e){(0,O.Kz)(e)||this.crc32.update((0,O.pP)(e))},e.prototype.digest=function(){return(0,P.sH)(this,void 0,void 0,function(){return(0,P.YH)(this,function(e){return[2,(0,O.yl)(this.crc32.digest())]})})},e.prototype.reset=function(){this.crc32=new $}}();var $=function(){function e(){this.checksum=0xffffffff}return e.prototype.update=function(e){var t,r;try{for(var n=(0,P.Ju)(e),s=n.next();!s.done;s=n.next()){var i=s.value;this.checksum=this.checksum>>>8^A[(this.checksum^i)&255]}}catch(e){t={error:e}}finally{try{s&&!s.done&&(r=n.return)&&r.call(n)}finally{if(t)throw t.error}}return this},e.prototype.digest=function(){return(0xffffffff^this.checksum)>>>0},e}(),A=(0,O.zB)([0,0x77073096,0xee0e612c,0x990951ba,0x76dc419,0x706af48f,0xe963a535,0x9e6495a3,0xedb8832,0x79dcb8a4,0xe0d5e91e,0x97d2d988,0x9b64c2b,0x7eb17cbd,0xe7b82d07,0x90bf1d91,0x1db71064,0x6ab020f2,0xf3b97148,0x84be41de,0x1adad47d,0x6ddde4eb,0xf4d4b551,0x83d385c7,0x136c9856,0x646ba8c0,0xfd62f97a,0x8a65c9ec,0x14015c4f,0x63066cd9,0xfa0f3d63,0x8d080df5,0x3b6e20c8,0x4c69105e,0xd56041e4,0xa2677172,0x3c03e4d1,0x4b04d447,0xd20d85fd,0xa50ab56b,0x35b5a8fa,0x42b2986c,0xdbbbc9d6,0xacbcf940,0x32d86ce3,0x45df5c75,0xdcd60dcf,0xabd13d59,0x26d930ac,0x51de003a,0xc8d75180,0xbfd06116,0x21b4f4b5,0x56b3c423,0xcfba9599,0xb8bda50f,0x2802b89e,0x5f058808,0xc60cd9b2,0xb10be924,0x2f6f7c87,0x58684c11,0xc1611dab,0xb6662d3d,0x76dc4190,0x1db7106,0x98d220bc,0xefd5102a,0x71b18589,0x6b6b51f,0x9fbfe4a5,0xe8b8d433,0x7807c9a2,0xf00f934,0x9609a88e,0xe10e9818,0x7f6a0dbb,0x86d3d2d,0x91646c97,0xe6635c01,0x6b6b51f4,0x1c6c6162,0x856530d8,0xf262004e,0x6c0695ed,0x1b01a57b,0x8208f4c1,0xf50fc457,0x65b0d9c6,0x12b7e950,0x8bbeb8ea,0xfcb9887c,0x62dd1ddf,0x15da2d49,0x8cd37cf3,0xfbd44c65,0x4db26158,0x3ab551ce,0xa3bc0074,0xd4bb30e2,0x4adfa541,0x3dd895d7,0xa4d1c46d,0xd3d6f4fb,0x4369e96a,0x346ed9fc,0xad678846,0xda60b8d0,0x44042d73,0x33031de5,0xaa0a4c5f,0xdd0d7cc9,0x5005713c,0x270241aa,0xbe0b1010,0xc90c2086,0x5768b525,0x206f85b3,0xb966d409,0xce61e49f,0x5edef90e,0x29d9c998,0xb0d09822,0xc7d7a8b4,0x59b33d17,0x2eb40d81,0xb7bd5c3b,0xc0ba6cad,0xedb88320,0x9abfb3b6,0x3b6e20c,0x74b1d29a,0xead54739,0x9dd277af,0x4db2615,0x73dc1683,0xe3630b12,0x94643b84,0xd6d6a3e,0x7a6a5aa8,0xe40ecf0b,0x9309ff9d,0xa00ae27,0x7d079eb1,0xf00f9344,0x8708a3d2,0x1e01f268,0x6906c2fe,0xf762575d,0x806567cb,0x196c3671,0x6e6b06e7,0xfed41b76,0x89d32be0,0x10da7a5a,0x67dd4acc,0xf9b9df6f,0x8ebeeff9,0x17b7be43,0x60b08ed5,0xd6d6a3e8,0xa1d1937e,0x38d8c2c4,0x4fdff252,0xd1bb67f1,0xa6bc5767,0x3fb506dd,0x48b2364b,0xd80d2bda,0xaf0a1b4c,0x36034af6,0x41047a60,0xdf60efc3,0xa867df55,0x316e8eef,0x4669be79,0xcb61b38c,0xbc66831a,0x256fd2a0,0x5268e236,0xcc0c7795,0xbb0b4703,0x220216b9,0x5505262f,0xc5ba3bbe,0xb2bd0b28,0x2bb45a92,0x5cb36a04,0xc2d7ffa7,0xb5d0cf31,0x2cd99e8b,0x5bdeae1d,0x9b64c2b0,0xec63f226,0x756aa39c,0x26d930a,0x9c0906a9,0xeb0e363f,0x72076785,0x5005713,0x95bf4a82,0xe2b87a14,0x7bb12bae,0xcb61b38,0x92d28e9b,0xe5d5be0d,0x7cdcefb7,0xbdbdf21,0x86d3d2d4,0xf1d4e242,0x68ddb3f8,0x1fda836e,0x81be16cd,0xf6b9265b,0x6fb077e1,0x18b74777,0x88085ae6,0xff0f6a70,0x66063bca,0x11010b5c,0x8f659eff,0xf862ae69,0x616bffd3,0x166ccf45,0xa00ae278,0xd70dd2ee,0x4e048354,0x3903b3c2,0xa7672661,0xd06016f7,0x4969474d,0x3e6e77db,0xaed16a4a,0xd9d65adc,0x40df0b66,936918e3,0xa9bcae53,0xdebb9ec5,0x47b2cf7f,0x30b5ffe9,0xbdbdf21c,0xcabac28a,0x53b39330,0x24b4a3a6,0xbad03605,0xcdd70693,0x54de5729,0x23d967bf,0xb3667a2e,0xc4614ab8,0x5d681b02,0x2a6f2b94,0xb40bbe37,0xc30c8ea1,0x5a05df1b,0x2d02ef8d]),M=r(8004);class I{constructor(e){if(this.bytes=e,8!==e.byteLength)throw Error("Int64 buffers must be exactly 8 bytes")}static fromNumber(e){if(e>0x8000000000000000||e<-0x8000000000000000)throw Error(`${e} is too large (or, if negative, too small) to represent as an Int64`);let t=new Uint8Array(8);for(let r=7,n=Math.abs(Math.round(e));r>-1&&n>0;r--,n/=256)t[r]=n;return e<0&&C(t),new I(t)}valueOf(){let e=this.bytes.slice(0),t=128&e[0];return t&&C(e),parseInt((0,M.n)(e),16)*(t?-1:1)}toString(){return String(this.valueOf())}}function C(e){for(let t=0;t<8;t++)e[t]^=255;for(let t=7;t>-1&&(e[t]++,0===e[t]);t--);}class k{constructor(e,t){this.toUtf8=e,this.fromUtf8=t}format(e){let t=[];for(let r of Object.keys(e)){let n=this.fromUtf8(r);t.push(Uint8Array.from([n.byteLength]),n,this.formatHeaderValue(e[r]))}let r=new Uint8Array(t.reduce((e,t)=>e+t.byteLength,0)),n=0;for(let e of t)r.set(e,n),n+=e.byteLength;return r}formatHeaderValue(e){switch(e.type){case"boolean":return Uint8Array.from([e.value?0:1]);case"byte":return Uint8Array.from([2,e.value]);case"short":let t=new DataView(new ArrayBuffer(3));return t.setUint8(0,3),t.setInt16(1,e.value,!1),new Uint8Array(t.buffer);case"integer":let r=new DataView(new ArrayBuffer(5));return r.setUint8(0,4),r.setInt32(1,e.value,!1),new Uint8Array(r.buffer);case"long":let n=new Uint8Array(9);return n[0]=5,n.set(e.value.bytes,1),n;case"binary":let s=new DataView(new ArrayBuffer(3+e.value.byteLength));s.setUint8(0,6),s.setUint16(1,e.value.byteLength,!1);let i=new Uint8Array(s.buffer);return i.set(e.value,3),i;case"string":let o=this.fromUtf8(e.value),a=new DataView(new ArrayBuffer(3+o.byteLength));a.setUint8(0,7),a.setUint16(1,o.byteLength,!1);let c=new Uint8Array(a.buffer);return c.set(o,3),c;case"timestamp":let u=new Uint8Array(9);return u[0]=8,u.set(I.fromNumber(e.value.valueOf()).bytes,1),u;case"uuid":if(!z.test(e.value))throw Error(`Invalid UUID received: ${e.value}`);let l=new Uint8Array(17);return l[0]=9,l.set((0,M.a)(e.value.replace(/\-/g,"")),1),l}}parse(e){let t={},r=0;for(;r<e.byteLength;){let n=e.getUint8(r++),s=this.toUtf8(new Uint8Array(e.buffer,e.byteOffset+r,n));switch(r+=n,e.getUint8(r++)){case 0:t[s]={type:T,value:!0};break;case 1:t[s]={type:T,value:!1};break;case 2:t[s]={type:R,value:e.getInt8(r++)};break;case 3:t[s]={type:N,value:e.getInt16(r,!1)},r+=2;break;case 4:t[s]={type:D,value:e.getInt32(r,!1)},r+=4;break;case 5:t[s]={type:U,value:new I(new Uint8Array(e.buffer,e.byteOffset+r,8))},r+=8;break;case 6:let i=e.getUint16(r,!1);r+=2,t[s]={type:j,value:new Uint8Array(e.buffer,e.byteOffset+r,i)},r+=i;break;case 7:let o=e.getUint16(r,!1);r+=2,t[s]={type:F,value:this.toUtf8(new Uint8Array(e.buffer,e.byteOffset+r,o))},r+=o;break;case 8:t[s]={type:_,value:new Date(new I(new Uint8Array(e.buffer,e.byteOffset+r,8)).valueOf())},r+=8;break;case 9:let a=new Uint8Array(e.buffer,e.byteOffset+r,16);r+=16,t[s]={type:L,value:`${(0,M.n)(a.subarray(0,4))}-${(0,M.n)(a.subarray(4,6))}-${(0,M.n)(a.subarray(6,8))}-${(0,M.n)(a.subarray(8,10))}-${(0,M.n)(a.subarray(10))}`};break;default:throw Error("Unrecognized header type tag")}}return t}}!function(e){e[e.boolTrue=0]="boolTrue",e[e.boolFalse=1]="boolFalse",e[e.byte=2]="byte",e[e.short=3]="short",e[e.integer=4]="integer",e[e.long=5]="long",e[e.byteArray=6]="byteArray",e[e.string=7]="string",e[e.timestamp=8]="timestamp",e[e.uuid=9]="uuid"}(n||(n={}));let T="boolean",R="byte",N="short",D="integer",U="long",j="binary",F="string",_="timestamp",L="uuid",z=/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/;class B{constructor(e,t){this.headerMarshaller=new k(e,t),this.messageBuffer=[],this.isEndOfStream=!1}feed(e){this.messageBuffer.push(this.decode(e))}endOfStream(){this.isEndOfStream=!0}getMessage(){let e=this.messageBuffer.pop(),t=this.isEndOfStream;return{getMessage:()=>e,isEndOfStream:()=>t}}getAvailableMessages(){let e=this.messageBuffer;this.messageBuffer=[];let t=this.isEndOfStream;return{getMessages:()=>e,isEndOfStream:()=>t}}encode({headers:e,body:t}){let r=this.headerMarshaller.format(e),n=r.byteLength+t.byteLength+16,s=new Uint8Array(n),i=new DataView(s.buffer,s.byteOffset,s.byteLength),o=new $;return i.setUint32(0,n,!1),i.setUint32(4,r.byteLength,!1),i.setUint32(8,o.update(s.subarray(0,8)).digest(),!1),s.set(r,12),s.set(t,r.byteLength+12),i.setUint32(n-4,o.update(s.subarray(8,n-4)).digest(),!1),s}decode(e){let{headers:t,body:r}=function({byteLength:e,byteOffset:t,buffer:r}){if(e<16)throw Error("Provided message too short to accommodate event stream message overhead");let n=new DataView(r,t,e),s=n.getUint32(0,!1);if(e!==s)throw Error("Reported message length does not match received message length");let i=n.getUint32(4,!1),o=n.getUint32(8,!1),a=n.getUint32(e-4,!1),c=new $().update(new Uint8Array(r,t,8));if(o!==c.digest())throw Error(`The prelude checksum specified in the message (${o}) does not match the calculated CRC32 checksum (${c.digest()})`);if(c.update(new Uint8Array(r,t+8,e-12)),a!==c.digest())throw Error(`The message checksum (${c.digest()}) did not match the expected value of ${a}`);return{headers:new DataView(r,t+8+4,i),body:new Uint8Array(r,t+8+4+i,s-i-16)}}(e);return{headers:this.headerMarshaller.parse(t),body:r}}formatHeaders(e){return this.headerMarshaller.format(e)}}class K{constructor(e){this.options=e}[Symbol.asyncIterator](){return this.asyncIterator()}async *asyncIterator(){for await(let e of this.options.inputStream){let t=this.options.decoder.decode(e);yield t}}}class V{constructor(e){this.options=e}[Symbol.asyncIterator](){return this.asyncIterator()}async *asyncIterator(){for await(let e of this.options.messageStream){let t=this.options.encoder.encode(e);yield t}this.options.includeEndFrame&&(yield new Uint8Array(0))}}class H{constructor(e){this.options=e}[Symbol.asyncIterator](){return this.asyncIterator()}async *asyncIterator(){for await(let e of this.options.messageStream){let t=await this.options.deserializer(e);void 0!==t&&(yield t)}}}class q{constructor(e){this.options=e}[Symbol.asyncIterator](){return this.asyncIterator()}async *asyncIterator(){for await(let e of this.options.inputStream){let t=this.options.serializer(e);yield t}}}class G{constructor({utf8Encoder:e,utf8Decoder:t}){this.eventStreamCodec=new B(e,t),this.utfEncoder=e}deserialize(e,t){var r;return new H({messageStream:new K({inputStream:function(e){let t=0,r=0,n=null,s=null,i=e=>{if("number"!=typeof e)throw Error("Attempted to allocate an event message where size was not a number: "+e);t=e,r=4,new DataView((n=new Uint8Array(e)).buffer).setUint32(0,e,!1)};return{[Symbol.asyncIterator]:async function*(){let o=e[Symbol.asyncIterator]();for(;;){let{value:e,done:a}=await o.next();if(a){if(t){if(t===r)yield n;else throw Error("Truncated event message received.")}return}let c=e.length,u=0;for(;u<c;){if(!n){let t=c-u;s||(s=new Uint8Array(4));let n=Math.min(4-r,t);if(s.set(e.slice(u,u+n),r),r+=n,u+=n,r<4)break;i(new DataView(s.buffer).getUint32(0,!1)),s=null}let o=Math.min(t-r,c-u);n.set(e.slice(u,u+o),r),r+=o,u+=o,t&&t===r&&(yield n,n=null,t=0,r=0)}}}}}(e),decoder:this.eventStreamCodec}),deserializer:(r=this.utfEncoder,async function(e){let{value:n}=e.headers[":message-type"];if("error"===n){let t=Error(e.headers[":error-message"].value||"UnknownError");throw t.name=e.headers[":error-code"].value,t}if("exception"===n){let n=e.headers[":exception-type"].value,s=await t({[n]:e});if(s.$unknown){let t=Error(r(e.body));throw t.name=n,t}throw s[n]}if("event"===n){let r={[e.headers[":event-type"].value]:e},n=await t(r);if(n.$unknown)return;return n}throw Error(`Unrecognizable event type: ${e.headers[":event-type"].value}`)})})}serialize(e,t){return new V({messageStream:new q({inputStream:e,serializer:t}),encoder:this.eventStreamCodec,includeEndFrame:!0})}}let W=e=>({[Symbol.asyncIterator]:async function*(){let t=e.getReader();try{for(;;){let{done:e,value:r}=await t.read();if(e)return;yield r}}finally{t.releaseLock()}}}),X=e=>{let t=e[Symbol.asyncIterator]();return new ReadableStream({async pull(e){let{done:r,value:n}=await t.next();if(r)return e.close();e.enqueue(n)}})};class Y{constructor({utf8Encoder:e,utf8Decoder:t}){this.universalMarshaller=new G({utf8Decoder:t,utf8Encoder:e})}deserialize(e,t){let r=J(e)?W(e):e;return this.universalMarshaller.deserialize(r,t)}serialize(e,t){let r=this.universalMarshaller.serialize(e,t);return"function"==typeof ReadableStream?X(r):r}}let J=e=>"function"==typeof ReadableStream&&e instanceof ReadableStream,Z=e=>new Y(e);var Q=r(9124),ee=r(2423),et=r(6850),er=r(2073),en=r(8768),es=r(4262),ei=r(9764),eo=r(2637),ea=r(3411),ec=r(6137),eu=r(8636);let el="required",ed="argv",ep="isSet",ef="booleanEquals",eh="error",em="endpoint",ey="tree",eg="PartitionResult",ex="stringEquals",eb={[el]:!1,type:"String"},ew={[el]:!0,default:!1,type:"Boolean"},ev={ref:"Endpoint"},eE={fn:ef,[ed]:[{ref:"UseFIPS"},!0]},eS={fn:ef,[ed]:[{ref:"UseDualStack"},!0]},eP={},eO={ref:"Region"},e$={fn:"getAttr",[ed]:[{ref:eg},"supportsFIPS"]},eA={fn:ef,[ed]:[!0,{fn:"getAttr",[ed]:[{ref:eg},"supportsDualStack"]}]},eM=[eE],eI=[eS],eC=[eO],ek={version:"1.0",parameters:{Region:eb,UseDualStack:ew,UseFIPS:ew,Endpoint:eb},rules:[{conditions:[{fn:ep,[ed]:[ev]}],rules:[{conditions:eM,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:eh},{conditions:eI,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:eh},{endpoint:{url:ev,properties:eP,headers:eP},type:em}],type:ey},{conditions:[{fn:ep,[ed]:eC}],rules:[{conditions:[{fn:"aws.partition",[ed]:eC,assign:eg}],rules:[{conditions:[eE,eS],rules:[{conditions:[{fn:ef,[ed]:[!0,e$]},eA],rules:[{endpoint:{url:"https://logs-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:eP,headers:eP},type:em}],type:ey},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:eh}],type:ey},{conditions:eM,rules:[{conditions:[{fn:ef,[ed]:[e$,!0]}],rules:[{conditions:[{fn:ex,[ed]:[eO,"us-gov-east-1"]}],endpoint:{url:"https://logs.us-gov-east-1.amazonaws.com",properties:eP,headers:eP},type:em},{conditions:[{fn:ex,[ed]:[eO,"us-gov-west-1"]}],endpoint:{url:"https://logs.us-gov-west-1.amazonaws.com",properties:eP,headers:eP},type:em},{endpoint:{url:"https://logs-fips.{Region}.{PartitionResult#dnsSuffix}",properties:eP,headers:eP},type:em}],type:ey},{error:"FIPS is enabled but this partition does not support FIPS",type:eh}],type:ey},{conditions:eI,rules:[{conditions:[eA],rules:[{endpoint:{url:"https://logs.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:eP,headers:eP},type:em}],type:ey},{error:"DualStack is enabled but this partition does not support DualStack",type:eh}],type:ey},{endpoint:{url:"https://logs.{Region}.{PartitionResult#dnsSuffix}",properties:eP,headers:eP},type:em}],type:ey}],type:ey},{error:"Invalid Configuration: Missing Region",type:eh}]},eT=new eu.kS({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),eR=(e,t={})=>eT.get(e,()=>(0,eu.sO)(ek,{endpointParams:e,logger:t.logger}));eu.mw.aws=ec.UF;let eN=e=>({apiVersion:"2014-03-28",base64Decoder:e?.base64Decoder??ei.E,base64Encoder:e?.base64Encoder??ei.n,disableHostPrefix:e?.disableHostPrefix??!1,endpointProvider:e?.endpointProvider??eR,extensions:e?.extensions??[],httpAuthSchemeProvider:e?.httpAuthSchemeProvider??x,httpAuthSchemes:e?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:e=>e.getIdentityProvider("aws.auth#sigv4"),signer:new en.f2}],logger:e?.logger??new h.N4,serviceId:e?.serviceId??"CloudWatch Logs",urlParser:e?.urlParser??es.D,utf8Decoder:e?.utf8Decoder??eo.a,utf8Encoder:e?.utf8Encoder??ea.P});var eD=r(1996);let eU=e=>{let t=(0,eD.I)(e),r=()=>t().then(h.lT),n=eN(e);return{...n,...e,runtime:"browser",defaultsMode:t,bodyLengthChecker:e?.bodyLengthChecker??et.n,credentialDefaultProvider:e?.credentialDefaultProvider??(e=>()=>Promise.reject(Error("Credential is missing"))),defaultUserAgentProvider:e?.defaultUserAgentProvider??(0,S.p)({serviceId:n.serviceId,clientVersion:v.rE}),eventStreamSerdeProvider:e?.eventStreamSerdeProvider??Z,maxAttempts:e?.maxAttempts??er.Gz,region:e?.region??(0,ee.B)("Region is missing"),requestHandler:Q.NC.create(e?.requestHandler??r),retryMode:e?.retryMode??(async()=>(await r()).retryMode||er.L0),sha256:e?.sha256??E.I,streamCollector:e?.streamCollector??Q.kv,useDualstackEndpoint:e?.useDualstackEndpoint??(()=>Promise.resolve(c.VW)),useFipsEndpoint:e?.useFipsEndpoint??(()=>Promise.resolve(c.Hj))}};var ej=r(3614),eF=r(7609);let e_=e=>{let t=e.httpAuthSchemes,r=e.httpAuthSchemeProvider,n=e.credentials;return{setHttpAuthScheme(e){let r=t.findIndex(t=>t.schemeId===e.schemeId);-1===r?t.push(e):t.splice(r,1,e)},httpAuthSchemes:()=>t,setHttpAuthSchemeProvider(e){r=e},httpAuthSchemeProvider:()=>r,setCredentials(e){n=e},credentials:()=>n}},eL=e=>({httpAuthSchemes:e.httpAuthSchemes(),httpAuthSchemeProvider:e.httpAuthSchemeProvider(),credentials:e.credentials()}),ez=e=>e,eB=(e,t)=>{let r={...ez((0,ej.Rq)(e)),...ez((0,h.xA)(e)),...ez((0,eF.eS)(e)),...ez(e_(e))};return t.forEach(e=>e.configure(r)),{...e,...(0,ej.$3)(r),...(0,h.uv)(r),...(0,eF.jt)(r),...eL(r)}};class eK extends h.Kj{config;constructor(...[e]){let t=eU(e||{}),r=(0,w.v)(t),n=(0,a.Dc)(r),h=(0,f.$z)(n),m=(0,c.TD)(h),y=(0,s.OV)(m),x=eB(b(l((0,p.Co)(y))),e?.extensions||[]);super(x),this.config=x,this.middlewareStack.use((0,a.sM)(this.config)),this.middlewareStack.use((0,f.ey)(this.config)),this.middlewareStack.use((0,d.vK)(this.config)),this.middlewareStack.use((0,s.TC)(this.config)),this.middlewareStack.use((0,i.Y7)(this.config)),this.middlewareStack.use((0,o.n4)(this.config)),this.middlewareStack.use((0,u.wB)(this.config,{httpAuthSchemeParametersProvider:g,identityProviderConfigProvider:async e=>new u.h$({"aws.auth#sigv4":e.credentials})})),this.middlewareStack.use((0,u.lW)(this.config))}destroy(){super.destroy()}}},5775:(e,t,r)=>{"use strict";r.d(t,{B:()=>Q});var n,s,i,o=r(8176),a=r(228),c=r(5268),u=r(9987),l=r(1919),d=r(7609);class p extends c.TJ{constructor(e){super(e),Object.setPrototypeOf(this,p.prototype)}}class f extends p{name="AccessDeniedException";$fault="client";constructor(e){super({name:"AccessDeniedException",$fault:"client",...e}),Object.setPrototypeOf(this,f.prototype)}}class h extends p{name="InvalidParameterException";$fault="client";constructor(e){super({name:"InvalidParameterException",$fault:"client",...e}),Object.setPrototypeOf(this,h.prototype)}}class m extends p{name="OperationAbortedException";$fault="client";constructor(e){super({name:"OperationAbortedException",$fault:"client",...e}),Object.setPrototypeOf(this,m.prototype)}}class y extends p{name="ResourceNotFoundException";$fault="client";constructor(e){super({name:"ResourceNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,y.prototype)}}class g extends p{name="ServiceUnavailableException";$fault="server";constructor(e){super({name:"ServiceUnavailableException",$fault:"server",...e}),Object.setPrototypeOf(this,g.prototype)}}class x extends p{name="InvalidOperationException";$fault="client";constructor(e){super({name:"InvalidOperationException",$fault:"client",...e}),Object.setPrototypeOf(this,x.prototype)}}class b extends p{name="ConflictException";$fault="client";constructor(e){super({name:"ConflictException",$fault:"client",...e}),Object.setPrototypeOf(this,b.prototype)}}class w extends p{name="ServiceQuotaExceededException";$fault="client";constructor(e){super({name:"ServiceQuotaExceededException",$fault:"client",...e}),Object.setPrototypeOf(this,w.prototype)}}class v extends p{name="ThrottlingException";$fault="client";constructor(e){super({name:"ThrottlingException",$fault:"client",...e}),Object.setPrototypeOf(this,v.prototype)}}class E extends p{name="ValidationException";$fault="client";constructor(e){super({name:"ValidationException",$fault:"client",...e}),Object.setPrototypeOf(this,E.prototype)}}class S extends p{name="LimitExceededException";$fault="client";constructor(e){super({name:"LimitExceededException",$fault:"client",...e}),Object.setPrototypeOf(this,S.prototype)}}class P extends p{name="ResourceAlreadyExistsException";$fault="client";constructor(e){super({name:"ResourceAlreadyExistsException",$fault:"client",...e}),Object.setPrototypeOf(this,P.prototype)}}class O extends p{name="DataAlreadyAcceptedException";$fault="client";expectedSequenceToken;constructor(e){super({name:"DataAlreadyAcceptedException",$fault:"client",...e}),Object.setPrototypeOf(this,O.prototype),this.expectedSequenceToken=e.expectedSequenceToken}}(n||(n={})).visit=(e,t)=>void 0!==e.openSearchIntegrationDetails?t.openSearchIntegrationDetails(e.openSearchIntegrationDetails):t._(e.$unknown[0],e.$unknown[1]);class $ extends p{name="InvalidSequenceTokenException";$fault="client";expectedSequenceToken;constructor(e){super({name:"InvalidSequenceTokenException",$fault:"client",...e}),Object.setPrototypeOf(this,$.prototype),this.expectedSequenceToken=e.expectedSequenceToken}}(s||(s={})).visit=(e,t)=>void 0!==e.openSearchResourceConfig?t.openSearchResourceConfig(e.openSearchResourceConfig):t._(e.$unknown[0],e.$unknown[1]);class A extends p{name="UnrecognizedClientException";$fault="client";constructor(e){super({name:"UnrecognizedClientException",$fault:"client",...e}),Object.setPrototypeOf(this,A.prototype)}}(i||(i={})).visit=(e,t)=>void 0!==e.sessionStart?t.sessionStart(e.sessionStart):void 0!==e.sessionUpdate?t.sessionUpdate(e.sessionUpdate):void 0!==e.SessionTimeoutException?t.SessionTimeoutException(e.SessionTimeoutException):void 0!==e.SessionStreamingException?t.SessionStreamingException(e.SessionStreamingException):t._(e.$unknown[0],e.$unknown[1]);class M extends p{name="MalformedQueryException";$fault="client";queryCompileError;constructor(e){super({name:"MalformedQueryException",$fault:"client",...e}),Object.setPrototypeOf(this,M.prototype),this.queryCompileError=e.queryCompileError}}class I extends p{name="TooManyTagsException";$fault="client";resourceName;constructor(e){super({name:"TooManyTagsException",$fault:"client",...e}),Object.setPrototypeOf(this,I.prototype),this.resourceName=e.resourceName}}let C=async(e,t)=>Z(t,{"content-type":"application/x-amz-json-1.1","x-amz-target":"Logs_20140328.CreateLogGroup"},"/",void 0,JSON.stringify((0,c.Ss)(e))),k=async(e,t)=>e.statusCode>=300?T(e,t):(await (0,c.Px)(e.body,t),{$metadata:Y(e)}),T=async(e,t)=>{let r={...e,body:await (0,l.CG)(e.body,t)},n=(0,l.cJ)(e,r.body);switch(n){case"InvalidParameterException":case"com.amazonaws.cloudwatchlogs#InvalidParameterException":throw await j(r,t);case"OperationAbortedException":case"com.amazonaws.cloudwatchlogs#OperationAbortedException":throw await z(r,t);case"ResourceNotFoundException":case"com.amazonaws.cloudwatchlogs#ResourceNotFoundException":throw await K(r,t);case"ServiceUnavailableException":case"com.amazonaws.cloudwatchlogs#ServiceUnavailableException":throw await H(r,t);case"InvalidOperationException":case"com.amazonaws.cloudwatchlogs#InvalidOperationException":throw await U(r,t);case"AccessDeniedException":case"com.amazonaws.cloudwatchlogs#AccessDeniedException":throw await R(r,t);case"ConflictException":case"com.amazonaws.cloudwatchlogs#ConflictException":throw await N(r,t);case"ServiceQuotaExceededException":case"com.amazonaws.cloudwatchlogs#ServiceQuotaExceededException":throw await V(r,t);case"ThrottlingException":case"com.amazonaws.cloudwatchlogs#ThrottlingException":throw await q(r,t);case"ValidationException":case"com.amazonaws.cloudwatchlogs#ValidationException":throw await X(r,t);case"LimitExceededException":case"com.amazonaws.cloudwatchlogs#LimitExceededException":throw await _(r,t);case"ResourceAlreadyExistsException":case"com.amazonaws.cloudwatchlogs#ResourceAlreadyExistsException":throw await B(r,t);case"DataAlreadyAcceptedException":case"com.amazonaws.cloudwatchlogs#DataAlreadyAcceptedException":throw await D(r,t);case"InvalidSequenceTokenException":case"com.amazonaws.cloudwatchlogs#InvalidSequenceTokenException":throw await F(r,t);case"UnrecognizedClientException":case"com.amazonaws.cloudwatchlogs#UnrecognizedClientException":throw await W(r,t);case"MalformedQueryException":case"com.amazonaws.cloudwatchlogs#MalformedQueryException":throw await L(r,t);case"TooManyTagsException":case"com.amazonaws.cloudwatchlogs#TooManyTagsException":throw await G(r,t);default:return J({output:e,parsedBody:r.body,errorCode:n})}},R=async(e,t)=>{let r=e.body,n=(0,c.Ss)(r),s=new f({$metadata:Y(e),...n});return(0,c.Mw)(s,r)},N=async(e,t)=>{let r=e.body,n=(0,c.Ss)(r),s=new b({$metadata:Y(e),...n});return(0,c.Mw)(s,r)},D=async(e,t)=>{let r=e.body,n=(0,c.Ss)(r),s=new O({$metadata:Y(e),...n});return(0,c.Mw)(s,r)},U=async(e,t)=>{let r=e.body,n=(0,c.Ss)(r),s=new x({$metadata:Y(e),...n});return(0,c.Mw)(s,r)},j=async(e,t)=>{let r=e.body,n=(0,c.Ss)(r),s=new h({$metadata:Y(e),...n});return(0,c.Mw)(s,r)},F=async(e,t)=>{let r=e.body,n=(0,c.Ss)(r),s=new $({$metadata:Y(e),...n});return(0,c.Mw)(s,r)},_=async(e,t)=>{let r=e.body,n=(0,c.Ss)(r),s=new S({$metadata:Y(e),...n});return(0,c.Mw)(s,r)},L=async(e,t)=>{let r=e.body,n=(0,c.Ss)(r),s=new M({$metadata:Y(e),...n});return(0,c.Mw)(s,r)},z=async(e,t)=>{let r=e.body,n=(0,c.Ss)(r),s=new m({$metadata:Y(e),...n});return(0,c.Mw)(s,r)},B=async(e,t)=>{let r=e.body,n=(0,c.Ss)(r),s=new P({$metadata:Y(e),...n});return(0,c.Mw)(s,r)},K=async(e,t)=>{let r=e.body,n=(0,c.Ss)(r),s=new y({$metadata:Y(e),...n});return(0,c.Mw)(s,r)},V=async(e,t)=>{let r=e.body,n=(0,c.Ss)(r),s=new w({$metadata:Y(e),...n});return(0,c.Mw)(s,r)},H=async(e,t)=>{let r=e.body,n=(0,c.Ss)(r),s=new g({$metadata:Y(e),...n});return(0,c.Mw)(s,r)},q=async(e,t)=>{let r=e.body,n=(0,c.Ss)(r),s=new v({$metadata:Y(e),...n});return(0,c.Mw)(s,r)},G=async(e,t)=>{let r=e.body,n=(0,c.Ss)(r),s=new I({$metadata:Y(e),...n});return(0,c.Mw)(s,r)},W=async(e,t)=>{let r=e.body,n=(0,c.Ss)(r),s=new A({$metadata:Y(e),...n});return(0,c.Mw)(s,r)},X=async(e,t)=>{let r=e.body,n=(0,c.Ss)(r),s=new E({$metadata:Y(e),...n});return(0,c.Mw)(s,r)},Y=e=>({httpStatusCode:e.statusCode,requestId:e.headers["x-amzn-requestid"]??e.headers["x-amzn-request-id"]??e.headers["x-amz-request-id"],extendedRequestId:e.headers["x-amz-id-2"],cfId:e.headers["x-amz-cf-id"]}),J=(0,c.jr)(p),Z=async(e,t,r,n,s)=>{let{hostname:i,protocol:o="https",port:a,path:c}=await e.endpoint(),u={protocol:o,hostname:i,port:a,method:"POST",path:c.endsWith("/")?c.slice(0,-1)+r:c+r,headers:t};return void 0!==n&&(u.hostname=n),void 0!==s&&(u.body=s),new d.Kd(u)};class Q extends c.uB.classBuilder().ep(u.S).m(function(e,t,r,n){return[(0,a.TM)(r,this.serialize,this.deserialize),(0,o.rD)(r,e.getEndpointParameterInstructions())]}).s("Logs_20140328","CreateLogGroup",{}).n("CloudWatchLogsClient","CreateLogGroupCommand").f(void 0,void 0).ser(C).de(k).build(){}},9987:(e,t,r)=>{"use strict";r.d(t,{S:()=>s,v:()=>n});let n=e=>({...e,useDualstackEndpoint:e.useDualstackEndpoint??!1,useFipsEndpoint:e.useFipsEndpoint??!1,defaultSigningName:"logs"}),s={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}}},9616:(e,t,r)=>{"use strict";r.d(t,{S:()=>ef});var n=r(1095),s=r(5937),i=r(8377),o=r(1136),a=r(1651),c=r(4383),u=r(649),l=r(8176),d=r(6320),p=r(5268),f=r(7258),h=r(9848);let m=async(e,t,r)=>({operation:(0,h.u)(t).operation,region:await (0,h.t)(e.region)()||(()=>{throw Error("expected `region` to be configured for `aws.auth#sigv4`")})()}),y=e=>{let t=[];return e.operation,t.push({schemeId:"aws.auth#sigv4",signingProperties:{name:"ecr",region:e.region},propertiesExtractor:(e,t)=>({signingProperties:{config:e,context:t}})}),t},g=e=>({...(0,f.h)(e)});var x=r(1641);let b={rE:"3.741.0"};var w=r(5071),v=r(9391),E=r(9124),S=r(2423),P=r(6850),O=r(2073),$=r(8768),A=r(4262),M=r(9764),I=r(2637),C=r(3411),k=r(6137),T=r(8636);let R="required",N="argv",D="isSet",U="booleanEquals",j="error",F="endpoint",_="tree",L="PartitionResult",z="stringEquals",B={[R]:!1,type:"String"},K={[R]:!0,default:!1,type:"Boolean"},V={ref:"Endpoint"},H={fn:U,[N]:[{ref:"UseFIPS"},!0]},q={fn:U,[N]:[{ref:"UseDualStack"},!0]},G={},W={fn:"getAttr",[N]:[{ref:L},"supportsFIPS"]},X={fn:U,[N]:[!0,{fn:"getAttr",[N]:[{ref:L},"supportsDualStack"]}]},Y={fn:"getAttr",[N]:[{ref:L},"name"]},J={url:"https://ecr-fips.{Region}.amazonaws.com",properties:{},headers:{}},Z=[H],Q=[q],ee=[{ref:"Region"}],et={version:"1.0",parameters:{Region:B,UseDualStack:K,UseFIPS:K,Endpoint:B},rules:[{conditions:[{fn:D,[N]:[V]}],rules:[{conditions:Z,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:j},{conditions:Q,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:j},{endpoint:{url:V,properties:G,headers:G},type:F}],type:_},{conditions:[{fn:D,[N]:ee}],rules:[{conditions:[{fn:"aws.partition",[N]:ee,assign:L}],rules:[{conditions:[H,q],rules:[{conditions:[{fn:U,[N]:[!0,W]},X],rules:[{endpoint:{url:"https://api.ecr-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:G,headers:G},type:F}],type:_},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:j}],type:_},{conditions:Z,rules:[{conditions:[{fn:U,[N]:[W,!0]}],rules:[{conditions:[{fn:z,[N]:[Y,"aws"]}],endpoint:J,type:F},{conditions:[{fn:z,[N]:[Y,"aws-us-gov"]}],endpoint:J,type:F},{endpoint:{url:"https://api.ecr-fips.{Region}.{PartitionResult#dnsSuffix}",properties:G,headers:G},type:F}],type:_},{error:"FIPS is enabled but this partition does not support FIPS",type:j}],type:_},{conditions:Q,rules:[{conditions:[X],rules:[{endpoint:{url:"https://api.ecr.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:G,headers:G},type:F}],type:_},{error:"DualStack is enabled but this partition does not support DualStack",type:j}],type:_},{endpoint:{url:"https://api.ecr.{Region}.{PartitionResult#dnsSuffix}",properties:G,headers:G},type:F}],type:_}],type:_},{error:"Invalid Configuration: Missing Region",type:j}]},er=new T.kS({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),en=(e,t={})=>er.get(e,()=>(0,T.sO)(et,{endpointParams:e,logger:t.logger}));T.mw.aws=k.UF;let es=e=>({apiVersion:"2015-09-21",base64Decoder:e?.base64Decoder??M.E,base64Encoder:e?.base64Encoder??M.n,disableHostPrefix:e?.disableHostPrefix??!1,endpointProvider:e?.endpointProvider??en,extensions:e?.extensions??[],httpAuthSchemeProvider:e?.httpAuthSchemeProvider??y,httpAuthSchemes:e?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:e=>e.getIdentityProvider("aws.auth#sigv4"),signer:new $.f2}],logger:e?.logger??new p.N4,serviceId:e?.serviceId??"ECR",urlParser:e?.urlParser??A.D,utf8Decoder:e?.utf8Decoder??I.a,utf8Encoder:e?.utf8Encoder??C.P});var ei=r(1996);let eo=e=>{let t=(0,ei.I)(e),r=()=>t().then(p.lT),n=es(e);return{...n,...e,runtime:"browser",defaultsMode:t,bodyLengthChecker:e?.bodyLengthChecker??P.n,credentialDefaultProvider:e?.credentialDefaultProvider??(e=>()=>Promise.reject(Error("Credential is missing"))),defaultUserAgentProvider:e?.defaultUserAgentProvider??(0,v.p)({serviceId:n.serviceId,clientVersion:b.rE}),maxAttempts:e?.maxAttempts??O.Gz,region:e?.region??(0,S.B)("Region is missing"),requestHandler:E.NC.create(e?.requestHandler??r),retryMode:e?.retryMode??(async()=>(await r()).retryMode||O.L0),sha256:e?.sha256??w.I,streamCollector:e?.streamCollector??E.kv,useDualstackEndpoint:e?.useDualstackEndpoint??(()=>Promise.resolve(a.VW)),useFipsEndpoint:e?.useFipsEndpoint??(()=>Promise.resolve(a.Hj))}};var ea=r(3614),ec=r(7609);let eu=e=>{let t=e.httpAuthSchemes,r=e.httpAuthSchemeProvider,n=e.credentials;return{setHttpAuthScheme(e){let r=t.findIndex(t=>t.schemeId===e.schemeId);-1===r?t.push(e):t.splice(r,1,e)},httpAuthSchemes:()=>t,setHttpAuthSchemeProvider(e){r=e},httpAuthSchemeProvider:()=>r,setCredentials(e){n=e},credentials:()=>n}},el=e=>({httpAuthSchemes:e.httpAuthSchemes(),httpAuthSchemeProvider:e.httpAuthSchemeProvider(),credentials:e.credentials()}),ed=e=>e,ep=(e,t)=>{let r={...ed((0,ea.Rq)(e)),...ed((0,p.xA)(e)),...ed((0,ec.eS)(e)),...ed(eu(e))};return t.forEach(e=>e.configure(r)),{...e,...(0,ea.$3)(r),...(0,p.uv)(r),...(0,ec.jt)(r),...el(r)}};class ef extends p.Kj{config;constructor(...[e]){let t=eo(e||{}),r=(0,x.v)(t),p=(0,o.Dc)(r),f=(0,d.$z)(p),h=(0,a.TD)(f),y=(0,n.OV)(h),b=ep(g((0,l.Co)(y)),e?.extensions||[]);super(b),this.config=b,this.middlewareStack.use((0,o.sM)(this.config)),this.middlewareStack.use((0,d.ey)(this.config)),this.middlewareStack.use((0,u.vK)(this.config)),this.middlewareStack.use((0,n.TC)(this.config)),this.middlewareStack.use((0,s.Y7)(this.config)),this.middlewareStack.use((0,i.n4)(this.config)),this.middlewareStack.use((0,c.wB)(this.config,{httpAuthSchemeParametersProvider:m,identityProviderConfigProvider:async e=>new c.h$({"aws.auth#sigv4":e.credentials})})),this.middlewareStack.use((0,c.lW)(this.config))}destroy(){super.destroy()}}},4900:(e,t,r)=>{"use strict";r.d(t,{W:()=>c});var n=r(8176),s=r(228),i=r(5268),o=r(1641),a=r(1442);class c extends i.uB.classBuilder().ep(o.S).m(function(e,t,r,i){return[(0,s.TM)(r,this.serialize,this.deserialize),(0,n.rD)(r,e.getEndpointParameterInstructions())]}).s("AmazonEC2ContainerRegistry_V20150921","CreateRepository",{}).n("ECRClient","CreateRepositoryCommand").f(void 0,void 0).ser(a.FR).de(a._Q).build(){}},59:(e,t,r)=>{"use strict";r.d(t,{p:()=>c});var n=r(8176),s=r(228),i=r(5268),o=r(1641),a=r(1442);class c extends i.uB.classBuilder().ep(o.S).m(function(e,t,r,i){return[(0,s.TM)(r,this.serialize,this.deserialize),(0,n.rD)(r,e.getEndpointParameterInstructions())]}).s("AmazonEC2ContainerRegistry_V20150921","DescribeRepositories",{}).n("ECRClient","DescribeRepositoriesCommand").f(void 0,void 0).ser(a.mk).de(a.J9).build(){}},1641:(e,t,r)=>{"use strict";r.d(t,{S:()=>s,v:()=>n});let n=e=>({...e,useDualstackEndpoint:e.useDualstackEndpoint??!1,useFipsEndpoint:e.useFipsEndpoint??!1,defaultSigningName:"ecr"}),s={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}}},1442:(e,t,r)=>{"use strict";r.d(t,{_Q:()=>X,J9:()=>Y,FR:()=>G,mk:()=>W});var n=r(1919),s=r(7609),i=r(5268);class o extends i.TJ{constructor(e){super(e),Object.setPrototypeOf(this,o.prototype)}}class a extends o{name="InvalidParameterException";$fault="client";constructor(e){super({name:"InvalidParameterException",$fault:"client",...e}),Object.setPrototypeOf(this,a.prototype)}}class c extends o{name="RepositoryNotFoundException";$fault="client";constructor(e){super({name:"RepositoryNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,c.prototype)}}class u extends o{name="ServerException";$fault="server";constructor(e){super({name:"ServerException",$fault:"server",...e}),Object.setPrototypeOf(this,u.prototype)}}class l extends o{name="LimitExceededException";$fault="client";constructor(e){super({name:"LimitExceededException",$fault:"client",...e}),Object.setPrototypeOf(this,l.prototype)}}class d extends o{name="UnableToGetUpstreamImageException";$fault="client";constructor(e){super({name:"UnableToGetUpstreamImageException",$fault:"client",...e}),Object.setPrototypeOf(this,d.prototype)}}class p extends o{name="ValidationException";$fault="client";constructor(e){super({name:"ValidationException",$fault:"client",...e}),Object.setPrototypeOf(this,p.prototype)}}class f extends o{name="EmptyUploadException";$fault="client";constructor(e){super({name:"EmptyUploadException",$fault:"client",...e}),Object.setPrototypeOf(this,f.prototype)}}class h extends o{name="InvalidLayerException";$fault="client";constructor(e){super({name:"InvalidLayerException",$fault:"client",...e}),Object.setPrototypeOf(this,h.prototype)}}class m extends o{name="KmsException";$fault="client";kmsError;constructor(e){super({name:"KmsException",$fault:"client",...e}),Object.setPrototypeOf(this,m.prototype),this.kmsError=e.kmsError}}class y extends o{name="LayerAlreadyExistsException";$fault="client";constructor(e){super({name:"LayerAlreadyExistsException",$fault:"client",...e}),Object.setPrototypeOf(this,y.prototype)}}class g extends o{name="LayerPartTooSmallException";$fault="client";constructor(e){super({name:"LayerPartTooSmallException",$fault:"client",...e}),Object.setPrototypeOf(this,g.prototype)}}class x extends o{name="UploadNotFoundException";$fault="client";constructor(e){super({name:"UploadNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,x.prototype)}}class b extends o{name="PullThroughCacheRuleAlreadyExistsException";$fault="client";constructor(e){super({name:"PullThroughCacheRuleAlreadyExistsException",$fault:"client",...e}),Object.setPrototypeOf(this,b.prototype)}}class w extends o{name="SecretNotFoundException";$fault="client";constructor(e){super({name:"SecretNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,w.prototype)}}class v extends o{name="UnableToAccessSecretException";$fault="client";constructor(e){super({name:"UnableToAccessSecretException",$fault:"client",...e}),Object.setPrototypeOf(this,v.prototype)}}class E extends o{name="UnableToDecryptSecretValueException";$fault="client";constructor(e){super({name:"UnableToDecryptSecretValueException",$fault:"client",...e}),Object.setPrototypeOf(this,E.prototype)}}class S extends o{name="UnsupportedUpstreamRegistryException";$fault="client";constructor(e){super({name:"UnsupportedUpstreamRegistryException",$fault:"client",...e}),Object.setPrototypeOf(this,S.prototype)}}class P extends o{name="InvalidTagParameterException";$fault="client";constructor(e){super({name:"InvalidTagParameterException",$fault:"client",...e}),Object.setPrototypeOf(this,P.prototype)}}class O extends o{name="RepositoryAlreadyExistsException";$fault="client";constructor(e){super({name:"RepositoryAlreadyExistsException",$fault:"client",...e}),Object.setPrototypeOf(this,O.prototype)}}class $ extends o{name="TooManyTagsException";$fault="client";constructor(e){super({name:"TooManyTagsException",$fault:"client",...e}),Object.setPrototypeOf(this,$.prototype)}}class A extends o{name="TemplateAlreadyExistsException";$fault="client";constructor(e){super({name:"TemplateAlreadyExistsException",$fault:"client",...e}),Object.setPrototypeOf(this,A.prototype)}}class M extends o{name="LifecyclePolicyNotFoundException";$fault="client";constructor(e){super({name:"LifecyclePolicyNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,M.prototype)}}class I extends o{name="PullThroughCacheRuleNotFoundException";$fault="client";constructor(e){super({name:"PullThroughCacheRuleNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,I.prototype)}}class C extends o{name="RegistryPolicyNotFoundException";$fault="client";constructor(e){super({name:"RegistryPolicyNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,C.prototype)}}class k extends o{name="RepositoryNotEmptyException";$fault="client";constructor(e){super({name:"RepositoryNotEmptyException",$fault:"client",...e}),Object.setPrototypeOf(this,k.prototype)}}class T extends o{name="TemplateNotFoundException";$fault="client";constructor(e){super({name:"TemplateNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,T.prototype)}}class R extends o{name="RepositoryPolicyNotFoundException";$fault="client";constructor(e){super({name:"RepositoryPolicyNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,R.prototype)}}class N extends o{name="ImageNotFoundException";$fault="client";constructor(e){super({name:"ImageNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,N.prototype)}}class D extends o{name="ScanNotFoundException";$fault="client";constructor(e){super({name:"ScanNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,D.prototype)}}class U extends o{name="LayerInaccessibleException";$fault="client";constructor(e){super({name:"LayerInaccessibleException",$fault:"client",...e}),Object.setPrototypeOf(this,U.prototype)}}class j extends o{name="LayersNotFoundException";$fault="client";constructor(e){super({name:"LayersNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,j.prototype)}}class F extends o{name="UnableToGetUpstreamLayerException";$fault="client";constructor(e){super({name:"UnableToGetUpstreamLayerException",$fault:"client",...e}),Object.setPrototypeOf(this,F.prototype)}}class _ extends o{name="LifecyclePolicyPreviewNotFoundException";$fault="client";constructor(e){super({name:"LifecyclePolicyPreviewNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,_.prototype)}}class L extends o{name="ImageAlreadyExistsException";$fault="client";constructor(e){super({name:"ImageAlreadyExistsException",$fault:"client",...e}),Object.setPrototypeOf(this,L.prototype)}}class z extends o{name="ImageDigestDoesNotMatchException";$fault="client";constructor(e){super({name:"ImageDigestDoesNotMatchException",$fault:"client",...e}),Object.setPrototypeOf(this,z.prototype)}}class B extends o{name="ImageTagAlreadyExistsException";$fault="client";constructor(e){super({name:"ImageTagAlreadyExistsException",$fault:"client",...e}),Object.setPrototypeOf(this,B.prototype)}}class K extends o{name="ReferencedImagesNotFoundException";$fault="client";constructor(e){super({name:"ReferencedImagesNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,K.prototype)}}class V extends o{name="UnsupportedImageTypeException";$fault="client";constructor(e){super({name:"UnsupportedImageTypeException",$fault:"client",...e}),Object.setPrototypeOf(this,V.prototype)}}class H extends o{name="LifecyclePolicyPreviewInProgressException";$fault="client";constructor(e){super({name:"LifecyclePolicyPreviewInProgressException",$fault:"client",...e}),Object.setPrototypeOf(this,H.prototype)}}class q extends o{name="InvalidLayerPartException";$fault="client";registryId;repositoryName;uploadId;lastValidByteReceived;constructor(e){super({name:"InvalidLayerPartException",$fault:"client",...e}),Object.setPrototypeOf(this,q.prototype),this.registryId=e.registryId,this.repositoryName=e.repositoryName,this.uploadId=e.uploadId,this.lastValidByteReceived=e.lastValidByteReceived}}let G=async(e,t)=>eV(t,eH("CreateRepository"),"/",void 0,JSON.stringify((0,i.Ss)(e))),W=async(e,t)=>eV(t,eH("DescribeRepositories"),"/",void 0,JSON.stringify((0,i.Ss)(e))),X=async(e,t)=>{if(e.statusCode>=300)return J(e,t);let r=await (0,n.Y2)(e.body,t),s={};return s=eF(r,t),{$metadata:eB(e),...s}},Y=async(e,t)=>{if(e.statusCode>=300)return J(e,t);let r=await (0,n.Y2)(e.body,t),s={};return s=e_(r,t),{$metadata:eB(e),...s}},J=async(e,t)=>{let r={...e,body:await (0,n.CG)(e.body,t)},s=(0,n.cJ)(e,r.body);switch(s){case"InvalidParameterException":case"com.amazonaws.ecr#InvalidParameterException":throw await ei(r,t);case"RepositoryNotFoundException":case"com.amazonaws.ecr#RepositoryNotFoundException":throw await eE(r,t);case"ServerException":case"com.amazonaws.ecr#ServerException":throw await e$(r,t);case"LimitExceededException":case"com.amazonaws.ecr#LimitExceededException":throw await em(r,t);case"UnableToGetUpstreamImageException":case"com.amazonaws.ecr#UnableToGetUpstreamImageException":throw await eT(r,t);case"ValidationException":case"com.amazonaws.ecr#ValidationException":throw await ej(r,t);case"EmptyUploadException":case"com.amazonaws.ecr#EmptyUploadException":throw await Z(r,t);case"InvalidLayerException":case"com.amazonaws.ecr#InvalidLayerException":throw await en(r,t);case"KmsException":case"com.amazonaws.ecr#KmsException":throw await ea(r,t);case"LayerAlreadyExistsException":case"com.amazonaws.ecr#LayerAlreadyExistsException":throw await ec(r,t);case"LayerPartTooSmallException":case"com.amazonaws.ecr#LayerPartTooSmallException":throw await el(r,t);case"UploadNotFoundException":case"com.amazonaws.ecr#UploadNotFoundException":throw await eU(r,t);case"PullThroughCacheRuleAlreadyExistsException":case"com.amazonaws.ecr#PullThroughCacheRuleAlreadyExistsException":throw await ey(r,t);case"SecretNotFoundException":case"com.amazonaws.ecr#SecretNotFoundException":throw await eO(r,t);case"UnableToAccessSecretException":case"com.amazonaws.ecr#UnableToAccessSecretException":throw await eC(r,t);case"UnableToDecryptSecretValueException":case"com.amazonaws.ecr#UnableToDecryptSecretValueException":throw await ek(r,t);case"UnsupportedUpstreamRegistryException":case"com.amazonaws.ecr#UnsupportedUpstreamRegistryException":throw await eD(r,t);case"InvalidTagParameterException":case"com.amazonaws.ecr#InvalidTagParameterException":throw await eo(r,t);case"RepositoryAlreadyExistsException":case"com.amazonaws.ecr#RepositoryAlreadyExistsException":throw await ew(r,t);case"TooManyTagsException":case"com.amazonaws.ecr#TooManyTagsException":throw await eI(r,t);case"TemplateAlreadyExistsException":case"com.amazonaws.ecr#TemplateAlreadyExistsException":throw await eA(r,t);case"LifecyclePolicyNotFoundException":case"com.amazonaws.ecr#LifecyclePolicyNotFoundException":throw await ep(r,t);case"PullThroughCacheRuleNotFoundException":case"com.amazonaws.ecr#PullThroughCacheRuleNotFoundException":throw await eg(r,t);case"RegistryPolicyNotFoundException":case"com.amazonaws.ecr#RegistryPolicyNotFoundException":throw await eb(r,t);case"RepositoryNotEmptyException":case"com.amazonaws.ecr#RepositoryNotEmptyException":throw await ev(r,t);case"TemplateNotFoundException":case"com.amazonaws.ecr#TemplateNotFoundException":throw await eM(r,t);case"RepositoryPolicyNotFoundException":case"com.amazonaws.ecr#RepositoryPolicyNotFoundException":throw await eS(r,t);case"ImageNotFoundException":case"com.amazonaws.ecr#ImageNotFoundException":throw await et(r,t);case"ScanNotFoundException":case"com.amazonaws.ecr#ScanNotFoundException":throw await eP(r,t);case"LayerInaccessibleException":case"com.amazonaws.ecr#LayerInaccessibleException":throw await eu(r,t);case"LayersNotFoundException":case"com.amazonaws.ecr#LayersNotFoundException":throw await ed(r,t);case"UnableToGetUpstreamLayerException":case"com.amazonaws.ecr#UnableToGetUpstreamLayerException":throw await eR(r,t);case"LifecyclePolicyPreviewNotFoundException":case"com.amazonaws.ecr#LifecyclePolicyPreviewNotFoundException":throw await eh(r,t);case"ImageAlreadyExistsException":case"com.amazonaws.ecr#ImageAlreadyExistsException":throw await Q(r,t);case"ImageDigestDoesNotMatchException":case"com.amazonaws.ecr#ImageDigestDoesNotMatchException":throw await ee(r,t);case"ImageTagAlreadyExistsException":case"com.amazonaws.ecr#ImageTagAlreadyExistsException":throw await er(r,t);case"ReferencedImagesNotFoundException":case"com.amazonaws.ecr#ReferencedImagesNotFoundException":throw await ex(r,t);case"UnsupportedImageTypeException":case"com.amazonaws.ecr#UnsupportedImageTypeException":throw await eN(r,t);case"LifecyclePolicyPreviewInProgressException":case"com.amazonaws.ecr#LifecyclePolicyPreviewInProgressException":throw await ef(r,t);case"InvalidLayerPartException":case"com.amazonaws.ecr#InvalidLayerPartException":throw await es(r,t);default:return eK({output:e,parsedBody:r.body,errorCode:s})}},Z=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new f({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},Q=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new L({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},ee=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new z({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},et=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new N({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},er=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new B({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},en=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new h({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},es=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new q({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},ei=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new a({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},eo=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new P({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},ea=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new m({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},ec=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new y({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},eu=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new U({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},el=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new g({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},ed=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new j({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},ep=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new M({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},ef=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new H({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},eh=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new _({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},em=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new l({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},ey=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new b({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},eg=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new I({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},ex=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new K({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},eb=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new C({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},ew=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new O({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},ev=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new k({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},eE=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new c({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},eS=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new R({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},eP=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new D({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},eO=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new w({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},e$=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new u({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},eA=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new A({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},eM=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new T({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},eI=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new $({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},eC=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new v({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},ek=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new E({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},eT=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new d({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},eR=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new F({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},eN=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new V({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},eD=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new S({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},eU=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new x({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},ej=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new p({$metadata:eB(e),...n});return(0,i.Mw)(s,r)},eF=(e,t)=>(0,i.s)(e,{repository:e=>eL(e,t)}),e_=(e,t)=>(0,i.s)(e,{nextToken:i.lK,repositories:e=>ez(e,t)}),eL=(e,t)=>(0,i.s)(e,{createdAt:e=>(0,i.Y0)((0,i.l3)((0,i.r$)(e))),encryptionConfiguration:i.Ss,imageScanningConfiguration:i.Ss,imageTagMutability:i.lK,registryId:i.lK,repositoryArn:i.lK,repositoryName:i.lK,repositoryUri:i.lK}),ez=(e,t)=>(e||[]).filter(e=>null!=e).map(e=>eL(e,t)),eB=e=>({httpStatusCode:e.statusCode,requestId:e.headers["x-amzn-requestid"]??e.headers["x-amzn-request-id"]??e.headers["x-amz-request-id"],extendedRequestId:e.headers["x-amz-id-2"],cfId:e.headers["x-amz-cf-id"]}),eK=(0,i.jr)(o),eV=async(e,t,r,n,i)=>{let{hostname:o,protocol:a="https",port:c,path:u}=await e.endpoint(),l={protocol:a,hostname:o,port:c,method:"POST",path:u.endsWith("/")?u.slice(0,-1)+r:u+r,headers:t};return void 0!==n&&(l.hostname=n),void 0!==i&&(l.body=i),new s.Kd(l)};function eH(e){return{"content-type":"application/x-amz-json-1.1","x-amz-target":`AmazonEC2ContainerRegistry_V20150921.${e}`}}},278:(e,t,r)=>{"use strict";r.d(t,{Z:()=>el});var n=r(1095),s=r(5937),i=r(8377),o=r(1136),a=r(1651),c=r(4383),u=r(649),l=r(8176),d=r(6320),p=r(5268),f=r(7258),h=r(9848);let m=async(e,t,r)=>({operation:(0,h.u)(t).operation,region:await (0,h.t)(e.region)()||(()=>{throw Error("expected `region` to be configured for `aws.auth#sigv4`")})()}),y=e=>{let t=[];return e.operation,t.push({schemeId:"aws.auth#sigv4",signingProperties:{name:"ecs",region:e.region},propertiesExtractor:(e,t)=>({signingProperties:{config:e,context:t}})}),t},g=e=>({...(0,f.h)(e)});var x=r(5070);let b={rE:"3.741.0"};var w=r(5071),v=r(9391),E=r(9124),S=r(2423),P=r(6850),O=r(2073),$=r(8768),A=r(4262),M=r(9764),I=r(2637),C=r(3411),k=r(6137),T=r(8636);let R="required",N="argv",D="isSet",U="booleanEquals",j="error",F="endpoint",_="tree",L="PartitionResult",z={[R]:!1,type:"String"},B={[R]:!0,default:!1,type:"Boolean"},K={ref:"Endpoint"},V={fn:U,[N]:[{ref:"UseFIPS"},!0]},H={fn:U,[N]:[{ref:"UseDualStack"},!0]},q={},G={fn:"getAttr",[N]:[{ref:L},"supportsFIPS"]},W={fn:U,[N]:[!0,{fn:"getAttr",[N]:[{ref:L},"supportsDualStack"]}]},X=[V],Y=[H],J=[{ref:"Region"}],Z={version:"1.0",parameters:{Region:z,UseDualStack:B,UseFIPS:B,Endpoint:z},rules:[{conditions:[{fn:D,[N]:[K]}],rules:[{conditions:X,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:j},{conditions:Y,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:j},{endpoint:{url:K,properties:q,headers:q},type:F}],type:_},{conditions:[{fn:D,[N]:J}],rules:[{conditions:[{fn:"aws.partition",[N]:J,assign:L}],rules:[{conditions:[V,H],rules:[{conditions:[{fn:U,[N]:[!0,G]},W],rules:[{endpoint:{url:"https://ecs-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:q,headers:q},type:F}],type:_},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:j}],type:_},{conditions:X,rules:[{conditions:[{fn:U,[N]:[G,!0]}],rules:[{endpoint:{url:"https://ecs-fips.{Region}.{PartitionResult#dnsSuffix}",properties:q,headers:q},type:F}],type:_},{error:"FIPS is enabled but this partition does not support FIPS",type:j}],type:_},{conditions:Y,rules:[{conditions:[W],rules:[{endpoint:{url:"https://ecs.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:q,headers:q},type:F}],type:_},{error:"DualStack is enabled but this partition does not support DualStack",type:j}],type:_},{endpoint:{url:"https://ecs.{Region}.{PartitionResult#dnsSuffix}",properties:q,headers:q},type:F}],type:_}],type:_},{error:"Invalid Configuration: Missing Region",type:j}]},Q=new T.kS({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),ee=(e,t={})=>Q.get(e,()=>(0,T.sO)(Z,{endpointParams:e,logger:t.logger}));T.mw.aws=k.UF;let et=e=>({apiVersion:"2014-11-13",base64Decoder:e?.base64Decoder??M.E,base64Encoder:e?.base64Encoder??M.n,disableHostPrefix:e?.disableHostPrefix??!1,endpointProvider:e?.endpointProvider??ee,extensions:e?.extensions??[],httpAuthSchemeProvider:e?.httpAuthSchemeProvider??y,httpAuthSchemes:e?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:e=>e.getIdentityProvider("aws.auth#sigv4"),signer:new $.f2}],logger:e?.logger??new p.N4,serviceId:e?.serviceId??"ECS",urlParser:e?.urlParser??A.D,utf8Decoder:e?.utf8Decoder??I.a,utf8Encoder:e?.utf8Encoder??C.P});var er=r(1996);let en=e=>{let t=(0,er.I)(e),r=()=>t().then(p.lT),n=et(e);return{...n,...e,runtime:"browser",defaultsMode:t,bodyLengthChecker:e?.bodyLengthChecker??P.n,credentialDefaultProvider:e?.credentialDefaultProvider??(e=>()=>Promise.reject(Error("Credential is missing"))),defaultUserAgentProvider:e?.defaultUserAgentProvider??(0,v.p)({serviceId:n.serviceId,clientVersion:b.rE}),maxAttempts:e?.maxAttempts??O.Gz,region:e?.region??(0,S.B)("Region is missing"),requestHandler:E.NC.create(e?.requestHandler??r),retryMode:e?.retryMode??(async()=>(await r()).retryMode||O.L0),sha256:e?.sha256??w.I,streamCollector:e?.streamCollector??E.kv,useDualstackEndpoint:e?.useDualstackEndpoint??(()=>Promise.resolve(a.VW)),useFipsEndpoint:e?.useFipsEndpoint??(()=>Promise.resolve(a.Hj))}};var es=r(3614),ei=r(7609);let eo=e=>{let t=e.httpAuthSchemes,r=e.httpAuthSchemeProvider,n=e.credentials;return{setHttpAuthScheme(e){let r=t.findIndex(t=>t.schemeId===e.schemeId);-1===r?t.push(e):t.splice(r,1,e)},httpAuthSchemes:()=>t,setHttpAuthSchemeProvider(e){r=e},httpAuthSchemeProvider:()=>r,setCredentials(e){n=e},credentials:()=>n}},ea=e=>({httpAuthSchemes:e.httpAuthSchemes(),httpAuthSchemeProvider:e.httpAuthSchemeProvider(),credentials:e.credentials()}),ec=e=>e,eu=(e,t)=>{let r={...ec((0,es.Rq)(e)),...ec((0,p.xA)(e)),...ec((0,ei.eS)(e)),...ec(eo(e))};return t.forEach(e=>e.configure(r)),{...e,...(0,es.$3)(r),...(0,p.uv)(r),...(0,ei.jt)(r),...ea(r)}};class el extends p.Kj{config;constructor(...[e]){let t=en(e||{}),r=(0,x.v)(t),p=(0,o.Dc)(r),f=(0,d.$z)(p),h=(0,a.TD)(f),y=(0,n.OV)(h),b=eu(g((0,l.Co)(y)),e?.extensions||[]);super(b),this.config=b,this.middlewareStack.use((0,o.sM)(this.config)),this.middlewareStack.use((0,d.ey)(this.config)),this.middlewareStack.use((0,u.vK)(this.config)),this.middlewareStack.use((0,n.TC)(this.config)),this.middlewareStack.use((0,s.Y7)(this.config)),this.middlewareStack.use((0,i.n4)(this.config)),this.middlewareStack.use((0,c.wB)(this.config,{httpAuthSchemeParametersProvider:m,identityProviderConfigProvider:async e=>new c.h$({"aws.auth#sigv4":e.credentials})})),this.middlewareStack.use((0,c.lW)(this.config))}destroy(){super.destroy()}}},4769:(e,t,r)=>{"use strict";r.d(t,{J:()=>c});var n=r(8176),s=r(228),i=r(5268),o=r(5070),a=r(6014);class c extends i.uB.classBuilder().ep(o.S).m(function(e,t,r,i){return[(0,s.TM)(r,this.serialize,this.deserialize),(0,n.rD)(r,e.getEndpointParameterInstructions())]}).s("AmazonEC2ContainerServiceV20141113","CreateService",{}).n("ECSClient","CreateServiceCommand").f(void 0,void 0).ser(a.QG).de(a.Bn).build(){}},5887:(e,t,r)=>{"use strict";r.d(t,{_:()=>c});var n=r(8176),s=r(228),i=r(5268),o=r(5070),a=r(6014);class c extends i.uB.classBuilder().ep(o.S).m(function(e,t,r,i){return[(0,s.TM)(r,this.serialize,this.deserialize),(0,n.rD)(r,e.getEndpointParameterInstructions())]}).s("AmazonEC2ContainerServiceV20141113","DescribeClusters",{}).n("ECSClient","DescribeClustersCommand").f(void 0,void 0).ser(a.Vq).de(a.qn).build(){}},1290:(e,t,r)=>{"use strict";r.d(t,{H:()=>c});var n=r(8176),s=r(228),i=r(5268),o=r(5070),a=r(6014);class c extends i.uB.classBuilder().ep(o.S).m(function(e,t,r,i){return[(0,s.TM)(r,this.serialize,this.deserialize),(0,n.rD)(r,e.getEndpointParameterInstructions())]}).s("AmazonEC2ContainerServiceV20141113","DescribeServices",{}).n("ECSClient","DescribeServicesCommand").f(void 0,void 0).ser(a.sz).de(a.L4).build(){}},5010:(e,t,r)=>{"use strict";r.d(t,{Z:()=>c});var n=r(8176),s=r(228),i=r(5268),o=r(5070),a=r(6014);class c extends i.uB.classBuilder().ep(o.S).m(function(e,t,r,i){return[(0,s.TM)(r,this.serialize,this.deserialize),(0,n.rD)(r,e.getEndpointParameterInstructions())]}).s("AmazonEC2ContainerServiceV20141113","DescribeTaskDefinition",{}).n("ECSClient","DescribeTaskDefinitionCommand").f(void 0,void 0).ser(a.wH).de(a.TU).build(){}},6660:(e,t,r)=>{"use strict";r.d(t,{N:()=>c});var n=r(8176),s=r(228),i=r(5268),o=r(5070),a=r(6014);class c extends i.uB.classBuilder().ep(o.S).m(function(e,t,r,i){return[(0,s.TM)(r,this.serialize,this.deserialize),(0,n.rD)(r,e.getEndpointParameterInstructions())]}).s("AmazonEC2ContainerServiceV20141113","ListClusters",{}).n("ECSClient","ListClustersCommand").f(void 0,void 0).ser(a.wS).de(a.RA).build(){}},8860:(e,t,r)=>{"use strict";r.d(t,{P:()=>c});var n=r(8176),s=r(228),i=r(5268),o=r(5070),a=r(6014);class c extends i.uB.classBuilder().ep(o.S).m(function(e,t,r,i){return[(0,s.TM)(r,this.serialize,this.deserialize),(0,n.rD)(r,e.getEndpointParameterInstructions())]}).s("AmazonEC2ContainerServiceV20141113","ListTaskDefinitions",{}).n("ECSClient","ListTaskDefinitionsCommand").f(void 0,void 0).ser(a.CA).de(a.dM).build(){}},6411:(e,t,r)=>{"use strict";r.d(t,{k:()=>c});var n=r(8176),s=r(228),i=r(5268),o=r(5070),a=r(6014);class c extends i.uB.classBuilder().ep(o.S).m(function(e,t,r,i){return[(0,s.TM)(r,this.serialize,this.deserialize),(0,n.rD)(r,e.getEndpointParameterInstructions())]}).s("AmazonEC2ContainerServiceV20141113","UpdateService",{}).n("ECSClient","UpdateServiceCommand").f(void 0,void 0).ser(a.vs).de(a._3).build(){}},5070:(e,t,r)=>{"use strict";r.d(t,{S:()=>s,v:()=>n});let n=e=>({...e,useDualstackEndpoint:e.useDualstackEndpoint??!1,useFipsEndpoint:e.useFipsEndpoint??!1,defaultSigningName:"ecs"}),s={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}}},6014:(e,t,r)=>{"use strict";r.d(t,{Bn:()=>L,qn:()=>z,L4:()=>B,TU:()=>K,RA:()=>V,dM:()=>H,_3:()=>q,QG:()=>R,Vq:()=>N,sz:()=>D,wH:()=>U,wS:()=>j,CA:()=>F,vs:()=>_});var n=r(1919),s=r(7609),i=r(5268);class o extends i.TJ{constructor(e){super(e),Object.setPrototypeOf(this,o.prototype)}}class a extends o{name="AccessDeniedException";$fault="client";constructor(e){super({name:"AccessDeniedException",$fault:"client",...e}),Object.setPrototypeOf(this,a.prototype)}}class c extends o{name="ClientException";$fault="client";constructor(e){super({name:"ClientException",$fault:"client",...e}),Object.setPrototypeOf(this,c.prototype)}}class u extends o{name="InvalidParameterException";$fault="client";constructor(e){super({name:"InvalidParameterException",$fault:"client",...e}),Object.setPrototypeOf(this,u.prototype)}}class l extends o{name="LimitExceededException";$fault="client";constructor(e){super({name:"LimitExceededException",$fault:"client",...e}),Object.setPrototypeOf(this,l.prototype)}}class d extends o{name="ServerException";$fault="server";constructor(e){super({name:"ServerException",$fault:"server",...e}),Object.setPrototypeOf(this,d.prototype)}}class p extends o{name="UpdateInProgressException";$fault="client";constructor(e){super({name:"UpdateInProgressException",$fault:"client",...e}),Object.setPrototypeOf(this,p.prototype)}}class f extends o{name="NamespaceNotFoundException";$fault="client";constructor(e){super({name:"NamespaceNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,f.prototype)}}class h extends o{name="ClusterNotFoundException";$fault="client";constructor(e){super({name:"ClusterNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,h.prototype)}}class m extends o{name="PlatformTaskDefinitionIncompatibilityException";$fault="client";constructor(e){super({name:"PlatformTaskDefinitionIncompatibilityException",$fault:"client",...e}),Object.setPrototypeOf(this,m.prototype)}}class y extends o{name="PlatformUnknownException";$fault="client";constructor(e){super({name:"PlatformUnknownException",$fault:"client",...e}),Object.setPrototypeOf(this,y.prototype)}}class g extends o{name="UnsupportedFeatureException";$fault="client";constructor(e){super({name:"UnsupportedFeatureException",$fault:"client",...e}),Object.setPrototypeOf(this,g.prototype)}}class x extends o{name="ServiceNotActiveException";$fault="client";constructor(e){super({name:"ServiceNotActiveException",$fault:"client",...e}),Object.setPrototypeOf(this,x.prototype)}}class b extends o{name="ServiceNotFoundException";$fault="client";constructor(e){super({name:"ServiceNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,b.prototype)}}class w extends o{name="TargetNotFoundException";$fault="client";constructor(e){super({name:"TargetNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,w.prototype)}}class v extends o{name="ClusterContainsContainerInstancesException";$fault="client";constructor(e){super({name:"ClusterContainsContainerInstancesException",$fault:"client",...e}),Object.setPrototypeOf(this,v.prototype)}}class E extends o{name="ClusterContainsServicesException";$fault="client";constructor(e){super({name:"ClusterContainsServicesException",$fault:"client",...e}),Object.setPrototypeOf(this,E.prototype)}}class S extends o{name="ClusterContainsTasksException";$fault="client";constructor(e){super({name:"ClusterContainsTasksException",$fault:"client",...e}),Object.setPrototypeOf(this,S.prototype)}}class P extends o{name="TaskSetNotFoundException";$fault="client";constructor(e){super({name:"TaskSetNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,P.prototype)}}class O extends o{name="TargetNotConnectedException";$fault="client";constructor(e){super({name:"TargetNotConnectedException",$fault:"client",...e}),Object.setPrototypeOf(this,O.prototype)}}class $ extends o{name="ResourceNotFoundException";$fault="client";constructor(e){super({name:"ResourceNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,$.prototype)}}class A extends o{name="AttributeLimitExceededException";$fault="client";constructor(e){super({name:"AttributeLimitExceededException",$fault:"client",...e}),Object.setPrototypeOf(this,A.prototype)}}class M extends o{name="ResourceInUseException";$fault="client";constructor(e){super({name:"ResourceInUseException",$fault:"client",...e}),Object.setPrototypeOf(this,M.prototype)}}class I extends o{name="BlockedException";$fault="client";constructor(e){super({name:"BlockedException",$fault:"client",...e}),Object.setPrototypeOf(this,I.prototype)}}class C extends o{name="ConflictException";$fault="client";resourceIds;constructor(e){super({name:"ConflictException",$fault:"client",...e}),Object.setPrototypeOf(this,C.prototype),this.resourceIds=e.resourceIds}}class k extends o{name="MissingVersionException";$fault="client";constructor(e){super({name:"MissingVersionException",$fault:"client",...e}),Object.setPrototypeOf(this,k.prototype)}}class T extends o{name="NoUpdateAvailableException";$fault="client";constructor(e){super({name:"NoUpdateAvailableException",$fault:"client",...e}),Object.setPrototypeOf(this,T.prototype)}}let R=async(e,t)=>eU(t,ej("CreateService"),"/",void 0,JSON.stringify((0,i.Ss)(e))),N=async(e,t)=>eU(t,ej("DescribeClusters"),"/",void 0,JSON.stringify((0,i.Ss)(e))),D=async(e,t)=>eU(t,ej("DescribeServices"),"/",void 0,JSON.stringify((0,i.Ss)(e))),U=async(e,t)=>eU(t,ej("DescribeTaskDefinition"),"/",void 0,JSON.stringify((0,i.Ss)(e))),j=async(e,t)=>eU(t,ej("ListClusters"),"/",void 0,JSON.stringify((0,i.Ss)(e))),F=async(e,t)=>eU(t,ej("ListTaskDefinitions"),"/",void 0,JSON.stringify((0,i.Ss)(e))),_=async(e,t)=>eU(t,ej("UpdateService"),"/",void 0,JSON.stringify((0,i.Ss)(e))),L=async(e,t)=>{if(e.statusCode>=300)return G(e,t);let r=await (0,n.Y2)(e.body,t),s={};return s=ew(r,t),{$metadata:eN(e),...s}},z=async(e,t)=>{if(e.statusCode>=300)return G(e,t);let r=await (0,n.Y2)(e.body,t),s={};return s=(0,i.Ss)(r),{$metadata:eN(e),...s}},B=async(e,t)=>{if(e.statusCode>=300)return G(e,t);let r=await (0,n.Y2)(e.body,t),s={};return s=eS(r,t),{$metadata:eN(e),...s}},K=async(e,t)=>{if(e.statusCode>=300)return G(e,t);let r=await (0,n.Y2)(e.body,t),s={};return s=eP(r,t),{$metadata:eN(e),...s}},V=async(e,t)=>{if(e.statusCode>=300)return G(e,t);let r=await (0,n.Y2)(e.body,t),s={};return s=(0,i.Ss)(r),{$metadata:eN(e),...s}},H=async(e,t)=>{if(e.statusCode>=300)return G(e,t);let r=await (0,n.Y2)(e.body,t),s={};return s=(0,i.Ss)(r),{$metadata:eN(e),...s}},q=async(e,t)=>{if(e.statusCode>=300)return G(e,t);let r=await (0,n.Y2)(e.body,t),s={};return s=eR(r,t),{$metadata:eN(e),...s}},G=async(e,t)=>{let r={...e,body:await (0,n.CG)(e.body,t)},s=(0,n.cJ)(e,r.body);switch(s){case"ClientException":case"com.amazonaws.ecs#ClientException":throw await J(r,t);case"InvalidParameterException":case"com.amazonaws.ecs#InvalidParameterException":throw await en(r,t);case"LimitExceededException":case"com.amazonaws.ecs#LimitExceededException":throw await es(r,t);case"ServerException":case"com.amazonaws.ecs#ServerException":throw await ep(r,t);case"UpdateInProgressException":case"com.amazonaws.ecs#UpdateInProgressException":throw await eb(r,t);case"NamespaceNotFoundException":case"com.amazonaws.ecs#NamespaceNotFoundException":throw await eo(r,t);case"AccessDeniedException":case"com.amazonaws.ecs#AccessDeniedException":throw await W(r,t);case"ClusterNotFoundException":case"com.amazonaws.ecs#ClusterNotFoundException":throw await et(r,t);case"PlatformTaskDefinitionIncompatibilityException":case"com.amazonaws.ecs#PlatformTaskDefinitionIncompatibilityException":throw await ec(r,t);case"PlatformUnknownException":case"com.amazonaws.ecs#PlatformUnknownException":throw await eu(r,t);case"UnsupportedFeatureException":case"com.amazonaws.ecs#UnsupportedFeatureException":throw await ex(r,t);case"ServiceNotActiveException":case"com.amazonaws.ecs#ServiceNotActiveException":throw await ef(r,t);case"ServiceNotFoundException":case"com.amazonaws.ecs#ServiceNotFoundException":throw await eh(r,t);case"TargetNotFoundException":case"com.amazonaws.ecs#TargetNotFoundException":throw await ey(r,t);case"ClusterContainsContainerInstancesException":case"com.amazonaws.ecs#ClusterContainsContainerInstancesException":throw await Z(r,t);case"ClusterContainsServicesException":case"com.amazonaws.ecs#ClusterContainsServicesException":throw await Q(r,t);case"ClusterContainsTasksException":case"com.amazonaws.ecs#ClusterContainsTasksException":throw await ee(r,t);case"TaskSetNotFoundException":case"com.amazonaws.ecs#TaskSetNotFoundException":throw await eg(r,t);case"TargetNotConnectedException":case"com.amazonaws.ecs#TargetNotConnectedException":throw await em(r,t);case"ResourceNotFoundException":case"com.amazonaws.ecs#ResourceNotFoundException":throw await ed(r,t);case"AttributeLimitExceededException":case"com.amazonaws.ecs#AttributeLimitExceededException":throw await X(r,t);case"ResourceInUseException":case"com.amazonaws.ecs#ResourceInUseException":throw await el(r,t);case"BlockedException":case"com.amazonaws.ecs#BlockedException":throw await Y(r,t);case"ConflictException":case"com.amazonaws.ecs#ConflictException":throw await er(r,t);case"MissingVersionException":case"com.amazonaws.ecs#MissingVersionException":throw await ei(r,t);case"NoUpdateAvailableException":case"com.amazonaws.ecs#NoUpdateAvailableException":throw await ea(r,t);default:return eD({output:e,parsedBody:r.body,errorCode:s})}},W=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new a({$metadata:eN(e),...n});return(0,i.Mw)(s,r)},X=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new A({$metadata:eN(e),...n});return(0,i.Mw)(s,r)},Y=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new I({$metadata:eN(e),...n});return(0,i.Mw)(s,r)},J=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new c({$metadata:eN(e),...n});return(0,i.Mw)(s,r)},Z=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new v({$metadata:eN(e),...n});return(0,i.Mw)(s,r)},Q=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new E({$metadata:eN(e),...n});return(0,i.Mw)(s,r)},ee=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new S({$metadata:eN(e),...n});return(0,i.Mw)(s,r)},et=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new h({$metadata:eN(e),...n});return(0,i.Mw)(s,r)},er=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new C({$metadata:eN(e),...n});return(0,i.Mw)(s,r)},en=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new u({$metadata:eN(e),...n});return(0,i.Mw)(s,r)},es=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new l({$metadata:eN(e),...n});return(0,i.Mw)(s,r)},ei=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new k({$metadata:eN(e),...n});return(0,i.Mw)(s,r)},eo=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new f({$metadata:eN(e),...n});return(0,i.Mw)(s,r)},ea=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new T({$metadata:eN(e),...n});return(0,i.Mw)(s,r)},ec=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new m({$metadata:eN(e),...n});return(0,i.Mw)(s,r)},eu=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new y({$metadata:eN(e),...n});return(0,i.Mw)(s,r)},el=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new M({$metadata:eN(e),...n});return(0,i.Mw)(s,r)},ed=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new $({$metadata:eN(e),...n});return(0,i.Mw)(s,r)},ep=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new d({$metadata:eN(e),...n});return(0,i.Mw)(s,r)},ef=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new x({$metadata:eN(e),...n});return(0,i.Mw)(s,r)},eh=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new b({$metadata:eN(e),...n});return(0,i.Mw)(s,r)},em=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new O({$metadata:eN(e),...n});return(0,i.Mw)(s,r)},ey=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new w({$metadata:eN(e),...n});return(0,i.Mw)(s,r)},eg=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new P({$metadata:eN(e),...n});return(0,i.Mw)(s,r)},ex=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new g({$metadata:eN(e),...n});return(0,i.Mw)(s,r)},eb=async(e,t)=>{let r=e.body,n=(0,i.Ss)(r),s=new p({$metadata:eN(e),...n});return(0,i.Mw)(s,r)},ew=(e,t)=>(0,i.s)(e,{service:e=>e$(e,t)}),ev=(e,t)=>(0,i.s)(e,{capacityProviderStrategy:i.Ss,createdAt:e=>(0,i.Y0)((0,i.l3)((0,i.r$)(e))),desiredCount:i.ET,failedTasks:i.ET,fargateEphemeralStorage:i.Ss,id:i.lK,launchType:i.lK,networkConfiguration:i.Ss,pendingCount:i.ET,platformFamily:i.lK,platformVersion:i.lK,rolloutState:i.lK,rolloutStateReason:i.lK,runningCount:i.ET,serviceConnectConfiguration:i.Ss,serviceConnectResources:i.Ss,status:i.lK,taskDefinition:i.lK,updatedAt:e=>(0,i.Y0)((0,i.l3)((0,i.r$)(e))),volumeConfigurations:i.Ss,vpcLatticeConfigurations:i.Ss}),eE=(e,t)=>(e||[]).filter(e=>null!=e).map(e=>ev(e,t)),eS=(e,t)=>(0,i.s)(e,{failures:i.Ss,services:e=>eI(e,t)}),eP=(e,t)=>(0,i.s)(e,{tags:i.Ss,taskDefinition:e=>eC(e,t)}),eO=(e,t)=>(0,i.s)(e,{unit:i.lK,value:i.JW}),e$=(e,t)=>(0,i.s)(e,{availabilityZoneRebalancing:i.lK,capacityProviderStrategy:i.Ss,clusterArn:i.lK,createdAt:e=>(0,i.Y0)((0,i.l3)((0,i.r$)(e))),createdBy:i.lK,deploymentConfiguration:i.Ss,deploymentController:i.Ss,deployments:e=>eE(e,t),desiredCount:i.ET,enableECSManagedTags:i.ak,enableExecuteCommand:i.ak,events:e=>eM(e,t),healthCheckGracePeriodSeconds:i.ET,launchType:i.lK,loadBalancers:i.Ss,networkConfiguration:i.Ss,pendingCount:i.ET,placementConstraints:i.Ss,placementStrategy:i.Ss,platformFamily:i.lK,platformVersion:i.lK,propagateTags:i.lK,roleArn:i.lK,runningCount:i.ET,schedulingStrategy:i.lK,serviceArn:i.lK,serviceName:i.lK,serviceRegistries:i.Ss,status:i.lK,tags:i.Ss,taskDefinition:i.lK,taskSets:e=>eT(e,t)}),eA=(e,t)=>(0,i.s)(e,{createdAt:e=>(0,i.Y0)((0,i.l3)((0,i.r$)(e))),id:i.lK,message:i.lK}),eM=(e,t)=>(e||[]).filter(e=>null!=e).map(e=>eA(e,t)),eI=(e,t)=>(e||[]).filter(e=>null!=e).map(e=>e$(e,t)),eC=(e,t)=>(0,i.s)(e,{compatibilities:i.Ss,containerDefinitions:i.Ss,cpu:i.lK,deregisteredAt:e=>(0,i.Y0)((0,i.l3)((0,i.r$)(e))),enableFaultInjection:i.ak,ephemeralStorage:i.Ss,executionRoleArn:i.lK,family:i.lK,inferenceAccelerators:i.Ss,ipcMode:i.lK,memory:i.lK,networkMode:i.lK,pidMode:i.lK,placementConstraints:i.Ss,proxyConfiguration:i.Ss,registeredAt:e=>(0,i.Y0)((0,i.l3)((0,i.r$)(e))),registeredBy:i.lK,requiresAttributes:i.Ss,requiresCompatibilities:i.Ss,revision:i.ET,runtimePlatform:i.Ss,status:i.lK,taskDefinitionArn:i.lK,taskRoleArn:i.lK,volumes:i.Ss}),ek=(e,t)=>(0,i.s)(e,{capacityProviderStrategy:i.Ss,clusterArn:i.lK,computedDesiredCount:i.ET,createdAt:e=>(0,i.Y0)((0,i.l3)((0,i.r$)(e))),externalId:i.lK,fargateEphemeralStorage:i.Ss,id:i.lK,launchType:i.lK,loadBalancers:i.Ss,networkConfiguration:i.Ss,pendingCount:i.ET,platformFamily:i.lK,platformVersion:i.lK,runningCount:i.ET,scale:e=>eO(e,t),serviceArn:i.lK,serviceRegistries:i.Ss,stabilityStatus:i.lK,stabilityStatusAt:e=>(0,i.Y0)((0,i.l3)((0,i.r$)(e))),startedBy:i.lK,status:i.lK,tags:i.Ss,taskDefinition:i.lK,taskSetArn:i.lK,updatedAt:e=>(0,i.Y0)((0,i.l3)((0,i.r$)(e)))}),eT=(e,t)=>(e||[]).filter(e=>null!=e).map(e=>ek(e,t)),eR=(e,t)=>(0,i.s)(e,{service:e=>e$(e,t)}),eN=e=>({httpStatusCode:e.statusCode,requestId:e.headers["x-amzn-requestid"]??e.headers["x-amzn-request-id"]??e.headers["x-amz-request-id"],extendedRequestId:e.headers["x-amz-id-2"],cfId:e.headers["x-amz-cf-id"]}),eD=(0,i.jr)(o),eU=async(e,t,r,n,i)=>{let{hostname:o,protocol:a="https",port:c,path:u}=await e.endpoint(),l={protocol:a,hostname:o,port:c,method:"POST",path:u.endsWith("/")?u.slice(0,-1)+r:u+r,headers:t};return void 0!==n&&(l.hostname=n),void 0!==i&&(l.body=i),new s.Kd(l)};function ej(e){return{"content-type":"application/x-amz-json-1.1","x-amz-target":`AmazonEC2ContainerServiceV20141113.${e}`}}},3182:(e,t,r)=>{"use strict";r.d(t,{M:()=>ep});var n=r(1095),s=r(5937),i=r(8377),o=r(1136),a=r(1651),c=r(4383),u=r(649),l=r(8176),d=r(6320),p=r(5268),f=r(7258),h=r(9848);let m=async(e,t,r)=>({operation:(0,h.u)(t).operation,region:await (0,h.t)(e.region)()||(()=>{throw Error("expected `region` to be configured for `aws.auth#sigv4`")})()}),y=e=>{let t=[];return e.operation,t.push({schemeId:"aws.auth#sigv4",signingProperties:{name:"elasticloadbalancing",region:e.region},propertiesExtractor:(e,t)=>({signingProperties:{config:e,context:t}})}),t},g=e=>({...(0,f.h)(e)});var x=r(5412);let b={rE:"3.741.0"};var w=r(5071),v=r(9391),E=r(9124),S=r(2423),P=r(6850),O=r(2073),$=r(8768),A=r(4262),M=r(9764),I=r(2637),C=r(3411),k=r(6137),T=r(8636);let R="required",N="argv",D="isSet",U="booleanEquals",j="error",F="endpoint",_="tree",L="PartitionResult",z="getAttr",B={[R]:!1,type:"String"},K={[R]:!0,default:!1,type:"Boolean"},V={ref:"Endpoint"},H={fn:U,[N]:[{ref:"UseFIPS"},!0]},q={fn:U,[N]:[{ref:"UseDualStack"},!0]},G={},W={fn:z,[N]:[{ref:L},"supportsFIPS"]},X={ref:L},Y={fn:U,[N]:[!0,{fn:z,[N]:[X,"supportsDualStack"]}]},J=[H],Z=[q],Q=[{ref:"Region"}],ee={version:"1.0",parameters:{Region:B,UseDualStack:K,UseFIPS:K,Endpoint:B},rules:[{conditions:[{fn:D,[N]:[V]}],rules:[{conditions:J,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:j},{conditions:Z,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:j},{endpoint:{url:V,properties:G,headers:G},type:F}],type:_},{conditions:[{fn:D,[N]:Q}],rules:[{conditions:[{fn:"aws.partition",[N]:Q,assign:L}],rules:[{conditions:[H,q],rules:[{conditions:[{fn:U,[N]:[!0,W]},Y],rules:[{endpoint:{url:"https://elasticloadbalancing-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:G,headers:G},type:F}],type:_},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:j}],type:_},{conditions:J,rules:[{conditions:[{fn:U,[N]:[W,!0]}],rules:[{conditions:[{fn:"stringEquals",[N]:[{fn:z,[N]:[X,"name"]},"aws-us-gov"]}],endpoint:{url:"https://elasticloadbalancing.{Region}.amazonaws.com",properties:G,headers:G},type:F},{endpoint:{url:"https://elasticloadbalancing-fips.{Region}.{PartitionResult#dnsSuffix}",properties:G,headers:G},type:F}],type:_},{error:"FIPS is enabled but this partition does not support FIPS",type:j}],type:_},{conditions:Z,rules:[{conditions:[Y],rules:[{endpoint:{url:"https://elasticloadbalancing.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:G,headers:G},type:F}],type:_},{error:"DualStack is enabled but this partition does not support DualStack",type:j}],type:_},{endpoint:{url:"https://elasticloadbalancing.{Region}.{PartitionResult#dnsSuffix}",properties:G,headers:G},type:F}],type:_}],type:_},{error:"Invalid Configuration: Missing Region",type:j}]},et=new T.kS({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),er=(e,t={})=>et.get(e,()=>(0,T.sO)(ee,{endpointParams:e,logger:t.logger}));T.mw.aws=k.UF;let en=e=>({apiVersion:"2015-12-01",base64Decoder:e?.base64Decoder??M.E,base64Encoder:e?.base64Encoder??M.n,disableHostPrefix:e?.disableHostPrefix??!1,endpointProvider:e?.endpointProvider??er,extensions:e?.extensions??[],httpAuthSchemeProvider:e?.httpAuthSchemeProvider??y,httpAuthSchemes:e?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:e=>e.getIdentityProvider("aws.auth#sigv4"),signer:new $.f2}],logger:e?.logger??new p.N4,serviceId:e?.serviceId??"Elastic Load Balancing v2",urlParser:e?.urlParser??A.D,utf8Decoder:e?.utf8Decoder??I.a,utf8Encoder:e?.utf8Encoder??C.P});var es=r(1996);let ei=e=>{let t=(0,es.I)(e),r=()=>t().then(p.lT),n=en(e);return{...n,...e,runtime:"browser",defaultsMode:t,bodyLengthChecker:e?.bodyLengthChecker??P.n,credentialDefaultProvider:e?.credentialDefaultProvider??(e=>()=>Promise.reject(Error("Credential is missing"))),defaultUserAgentProvider:e?.defaultUserAgentProvider??(0,v.p)({serviceId:n.serviceId,clientVersion:b.rE}),maxAttempts:e?.maxAttempts??O.Gz,region:e?.region??(0,S.B)("Region is missing"),requestHandler:E.NC.create(e?.requestHandler??r),retryMode:e?.retryMode??(async()=>(await r()).retryMode||O.L0),sha256:e?.sha256??w.I,streamCollector:e?.streamCollector??E.kv,useDualstackEndpoint:e?.useDualstackEndpoint??(()=>Promise.resolve(a.VW)),useFipsEndpoint:e?.useFipsEndpoint??(()=>Promise.resolve(a.Hj))}};var eo=r(3614),ea=r(7609);let ec=e=>{let t=e.httpAuthSchemes,r=e.httpAuthSchemeProvider,n=e.credentials;return{setHttpAuthScheme(e){let r=t.findIndex(t=>t.schemeId===e.schemeId);-1===r?t.push(e):t.splice(r,1,e)},httpAuthSchemes:()=>t,setHttpAuthSchemeProvider(e){r=e},httpAuthSchemeProvider:()=>r,setCredentials(e){n=e},credentials:()=>n}},eu=e=>({httpAuthSchemes:e.httpAuthSchemes(),httpAuthSchemeProvider:e.httpAuthSchemeProvider(),credentials:e.credentials()}),el=e=>e,ed=(e,t)=>{let r={...el((0,eo.Rq)(e)),...el((0,p.xA)(e)),...el((0,ea.eS)(e)),...el(ec(e))};return t.forEach(e=>e.configure(r)),{...e,...(0,eo.$3)(r),...(0,p.uv)(r),...(0,ea.jt)(r),...eu(r)}};class ep extends p.Kj{config;constructor(...[e]){let t=ei(e||{}),r=(0,x.v)(t),p=(0,o.Dc)(r),f=(0,d.$z)(p),h=(0,a.TD)(f),y=(0,n.OV)(h),b=ed(g((0,l.Co)(y)),e?.extensions||[]);super(b),this.config=b,this.middlewareStack.use((0,o.sM)(this.config)),this.middlewareStack.use((0,d.ey)(this.config)),this.middlewareStack.use((0,u.vK)(this.config)),this.middlewareStack.use((0,n.TC)(this.config)),this.middlewareStack.use((0,s.Y7)(this.config)),this.middlewareStack.use((0,i.n4)(this.config)),this.middlewareStack.use((0,c.wB)(this.config,{httpAuthSchemeParametersProvider:m,identityProviderConfigProvider:async e=>new c.h$({"aws.auth#sigv4":e.credentials})})),this.middlewareStack.use((0,c.lW)(this.config))}destroy(){super.destroy()}}},8725:(e,t,r)=>{"use strict";r.d(t,{O:()=>c});var n=r(8176),s=r(228),i=r(5268),o=r(5412),a=r(5419);class c extends i.uB.classBuilder().ep(o.S).m(function(e,t,r,i){return[(0,s.TM)(r,this.serialize,this.deserialize),(0,n.rD)(r,e.getEndpointParameterInstructions())]}).s("ElasticLoadBalancing_v10","CreateRule",{}).n("ElasticLoadBalancingV2Client","CreateRuleCommand").f(void 0,void 0).ser(a.ts).de(a.sK).build(){}},3665:(e,t,r)=>{"use strict";r.d(t,{G:()=>c});var n=r(8176),s=r(228),i=r(5268),o=r(5412),a=r(5419);class c extends i.uB.classBuilder().ep(o.S).m(function(e,t,r,i){return[(0,s.TM)(r,this.serialize,this.deserialize),(0,n.rD)(r,e.getEndpointParameterInstructions())]}).s("ElasticLoadBalancing_v10","CreateTargetGroup",{}).n("ElasticLoadBalancingV2Client","CreateTargetGroupCommand").f(void 0,void 0).ser(a.Fp).de(a.wy).build(){}},7279:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var n=r(8176),s=r(228),i=r(5268),o=r(5412),a=r(5419);class c extends i.uB.classBuilder().ep(o.S).m(function(e,t,r,i){return[(0,s.TM)(r,this.serialize,this.deserialize),(0,n.rD)(r,e.getEndpointParameterInstructions())]}).s("ElasticLoadBalancing_v10","DescribeListeners",{}).n("ElasticLoadBalancingV2Client","DescribeListenersCommand").f(void 0,void 0).ser(a.Xf).de(a.YJ).build(){}},1769:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var n=r(8176),s=r(228),i=r(5268),o=r(5412),a=r(5419);class c extends i.uB.classBuilder().ep(o.S).m(function(e,t,r,i){return[(0,s.TM)(r,this.serialize,this.deserialize),(0,n.rD)(r,e.getEndpointParameterInstructions())]}).s("ElasticLoadBalancing_v10","DescribeLoadBalancers",{}).n("ElasticLoadBalancingV2Client","DescribeLoadBalancersCommand").f(void 0,void 0).ser(a.pB).de(a.wS).build(){}},3097:(e,t,r)=>{"use strict";r.d(t,{G:()=>c});var n=r(8176),s=r(228),i=r(5268),o=r(5412),a=r(5419);class c extends i.uB.classBuilder().ep(o.S).m(function(e,t,r,i){return[(0,s.TM)(r,this.serialize,this.deserialize),(0,n.rD)(r,e.getEndpointParameterInstructions())]}).s("ElasticLoadBalancing_v10","DescribeRules",{}).n("ElasticLoadBalancingV2Client","DescribeRulesCommand").f(void 0,void 0).ser(a.nq).de(a.ge).build(){}},5563:(e,t,r)=>{"use strict";r.d(t,{s:()=>c});var n=r(8176),s=r(228),i=r(5268),o=r(5412),a=r(5419);class c extends i.uB.classBuilder().ep(o.S).m(function(e,t,r,i){return[(0,s.TM)(r,this.serialize,this.deserialize),(0,n.rD)(r,e.getEndpointParameterInstructions())]}).s("ElasticLoadBalancing_v10","DescribeTargetGroups",{}).n("ElasticLoadBalancingV2Client","DescribeTargetGroupsCommand").f(void 0,void 0).ser(a.vA).de(a.S_).build(){}},5412:(e,t,r)=>{"use strict";r.d(t,{S:()=>s,v:()=>n});let n=e=>({...e,useDualstackEndpoint:e.useDualstackEndpoint??!1,useFipsEndpoint:e.useFipsEndpoint??!1,defaultSigningName:"elasticloadbalancing"}),s={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}}},8754:(e,t,r)=>{"use strict";r.d(t,{n:()=>s});var n=r(5268);class s extends n.TJ{constructor(e){super(e),Object.setPrototypeOf(this,s.prototype)}}},8269:(e,t,r)=>{"use strict";r.d(t,{A8:()=>_,Ac:()=>H,Av:()=>q,Bz:()=>N,CZ:()=>ee,ES:()=>f,EW:()=>F,F$:()=>a,FQ:()=>u,G3:()=>G,Hc:()=>J,I8:()=>x,Is:()=>y,JO:()=>l,Jg:()=>c,Km:()=>et,Lw:()=>S,MC:()=>K,Mz:()=>k,Ny:()=>A,TO:()=>ei,WJ:()=>L,WT:()=>z,XL:()=>X,Yn:()=>w,_P:()=>h,_w:()=>O,_z:()=>p,bN:()=>Q,c1:()=>er,c2:()=>g,cD:()=>d,cw:()=>R,gk:()=>P,hB:()=>Z,jL:()=>$,lB:()=>en,lU:()=>T,mv:()=>V,nK:()=>o,oR:()=>m,p1:()=>b,qi:()=>j,rT:()=>D,rc:()=>E,t_:()=>v,u4:()=>C,uu:()=>s,v:()=>es,vY:()=>i,ve:()=>W,w6:()=>I,wf:()=>B,x7:()=>Y,yg:()=>U,yl:()=>M});var n=r(8754);class s extends n.n{name="CertificateNotFoundException";$fault="client";Message;constructor(e){super({name:"CertificateNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,s.prototype),this.Message=e.Message}}class i extends n.n{name="ListenerNotFoundException";$fault="client";Message;constructor(e){super({name:"ListenerNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,i.prototype),this.Message=e.Message}}class o extends n.n{name="TooManyCertificatesException";$fault="client";Message;constructor(e){super({name:"TooManyCertificatesException",$fault:"client",...e}),Object.setPrototypeOf(this,o.prototype),this.Message=e.Message}}class a extends n.n{name="DuplicateTagKeysException";$fault="client";Message;constructor(e){super({name:"DuplicateTagKeysException",$fault:"client",...e}),Object.setPrototypeOf(this,a.prototype),this.Message=e.Message}}class c extends n.n{name="LoadBalancerNotFoundException";$fault="client";Message;constructor(e){super({name:"LoadBalancerNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,c.prototype),this.Message=e.Message}}class u extends n.n{name="RuleNotFoundException";$fault="client";Message;constructor(e){super({name:"RuleNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,u.prototype),this.Message=e.Message}}class l extends n.n{name="TargetGroupNotFoundException";$fault="client";Message;constructor(e){super({name:"TargetGroupNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,l.prototype),this.Message=e.Message}}class d extends n.n{name="TooManyTagsException";$fault="client";Message;constructor(e){super({name:"TooManyTagsException",$fault:"client",...e}),Object.setPrototypeOf(this,d.prototype),this.Message=e.Message}}class p extends n.n{name="TrustStoreNotFoundException";$fault="client";Message;constructor(e){super({name:"TrustStoreNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,p.prototype),this.Message=e.Message}}class f extends n.n{name="InvalidRevocationContentException";$fault="client";Message;constructor(e){super({name:"InvalidRevocationContentException",$fault:"client",...e}),Object.setPrototypeOf(this,f.prototype),this.Message=e.Message}}class h extends n.n{name="RevocationContentNotFoundException";$fault="client";Message;constructor(e){super({name:"RevocationContentNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,h.prototype),this.Message=e.Message}}class m extends n.n{name="TooManyTrustStoreRevocationEntriesException";$fault="client";Message;constructor(e){super({name:"TooManyTrustStoreRevocationEntriesException",$fault:"client",...e}),Object.setPrototypeOf(this,m.prototype),this.Message=e.Message}}class y extends n.n{name="AllocationIdNotFoundException";$fault="client";Message;constructor(e){super({name:"AllocationIdNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,y.prototype),this.Message=e.Message}}class g extends n.n{name="ALPNPolicyNotSupportedException";$fault="client";Message;constructor(e){super({name:"ALPNPolicyNotSupportedException",$fault:"client",...e}),Object.setPrototypeOf(this,g.prototype),this.Message=e.Message}}class x extends n.n{name="AvailabilityZoneNotSupportedException";$fault="client";Message;constructor(e){super({name:"AvailabilityZoneNotSupportedException",$fault:"client",...e}),Object.setPrototypeOf(this,x.prototype),this.Message=e.Message}}class b extends n.n{name="CaCertificatesBundleNotFoundException";$fault="client";Message;constructor(e){super({name:"CaCertificatesBundleNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,b.prototype),this.Message=e.Message}}class w extends n.n{name="CapacityDecreaseRequestsLimitExceededException";$fault="client";Message;constructor(e){super({name:"CapacityDecreaseRequestsLimitExceededException",$fault:"client",...e}),Object.setPrototypeOf(this,w.prototype),this.Message=e.Message}}class v extends n.n{name="CapacityReservationPendingException";$fault="client";Message;constructor(e){super({name:"CapacityReservationPendingException",$fault:"client",...e}),Object.setPrototypeOf(this,v.prototype),this.Message=e.Message}}class E extends n.n{name="CapacityUnitsLimitExceededException";$fault="client";Message;constructor(e){super({name:"CapacityUnitsLimitExceededException",$fault:"client",...e}),Object.setPrototypeOf(this,E.prototype),this.Message=e.Message}}class S extends n.n{name="DuplicateListenerException";$fault="client";Message;constructor(e){super({name:"DuplicateListenerException",$fault:"client",...e}),Object.setPrototypeOf(this,S.prototype),this.Message=e.Message}}class P extends n.n{name="IncompatibleProtocolsException";$fault="client";Message;constructor(e){super({name:"IncompatibleProtocolsException",$fault:"client",...e}),Object.setPrototypeOf(this,P.prototype),this.Message=e.Message}}class O extends n.n{name="InvalidConfigurationRequestException";$fault="client";Message;constructor(e){super({name:"InvalidConfigurationRequestException",$fault:"client",...e}),Object.setPrototypeOf(this,O.prototype),this.Message=e.Message}}class $ extends n.n{name="InvalidLoadBalancerActionException";$fault="client";Message;constructor(e){super({name:"InvalidLoadBalancerActionException",$fault:"client",...e}),Object.setPrototypeOf(this,$.prototype),this.Message=e.Message}}class A extends n.n{name="SSLPolicyNotFoundException";$fault="client";Message;constructor(e){super({name:"SSLPolicyNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,A.prototype),this.Message=e.Message}}class M extends n.n{name="TargetGroupAssociationLimitException";$fault="client";Message;constructor(e){super({name:"TargetGroupAssociationLimitException",$fault:"client",...e}),Object.setPrototypeOf(this,M.prototype),this.Message=e.Message}}class I extends n.n{name="TooManyActionsException";$fault="client";Message;constructor(e){super({name:"TooManyActionsException",$fault:"client",...e}),Object.setPrototypeOf(this,I.prototype),this.Message=e.Message}}class C extends n.n{name="TooManyListenersException";$fault="client";Message;constructor(e){super({name:"TooManyListenersException",$fault:"client",...e}),Object.setPrototypeOf(this,C.prototype),this.Message=e.Message}}class k extends n.n{name="TooManyRegistrationsForTargetIdException";$fault="client";Message;constructor(e){super({name:"TooManyRegistrationsForTargetIdException",$fault:"client",...e}),Object.setPrototypeOf(this,k.prototype),this.Message=e.Message}}class T extends n.n{name="TooManyTargetsException";$fault="client";Message;constructor(e){super({name:"TooManyTargetsException",$fault:"client",...e}),Object.setPrototypeOf(this,T.prototype),this.Message=e.Message}}class R extends n.n{name="TooManyUniqueTargetGroupsPerLoadBalancerException";$fault="client";Message;constructor(e){super({name:"TooManyUniqueTargetGroupsPerLoadBalancerException",$fault:"client",...e}),Object.setPrototypeOf(this,R.prototype),this.Message=e.Message}}class N extends n.n{name="TrustStoreNotReadyException";$fault="client";Message;constructor(e){super({name:"TrustStoreNotReadyException",$fault:"client",...e}),Object.setPrototypeOf(this,N.prototype),this.Message=e.Message}}class D extends n.n{name="UnsupportedProtocolException";$fault="client";Message;constructor(e){super({name:"UnsupportedProtocolException",$fault:"client",...e}),Object.setPrototypeOf(this,D.prototype),this.Message=e.Message}}class U extends n.n{name="DuplicateLoadBalancerNameException";$fault="client";Message;constructor(e){super({name:"DuplicateLoadBalancerNameException",$fault:"client",...e}),Object.setPrototypeOf(this,U.prototype),this.Message=e.Message}}class j extends n.n{name="InvalidSchemeException";$fault="client";Message;constructor(e){super({name:"InvalidSchemeException",$fault:"client",...e}),Object.setPrototypeOf(this,j.prototype),this.Message=e.Message}}class F extends n.n{name="InvalidSecurityGroupException";$fault="client";Message;constructor(e){super({name:"InvalidSecurityGroupException",$fault:"client",...e}),Object.setPrototypeOf(this,F.prototype),this.Message=e.Message}}class _ extends n.n{name="InvalidSubnetException";$fault="client";Message;constructor(e){super({name:"InvalidSubnetException",$fault:"client",...e}),Object.setPrototypeOf(this,_.prototype),this.Message=e.Message}}class L extends n.n{name="OperationNotPermittedException";$fault="client";Message;constructor(e){super({name:"OperationNotPermittedException",$fault:"client",...e}),Object.setPrototypeOf(this,L.prototype),this.Message=e.Message}}class z extends n.n{name="ResourceInUseException";$fault="client";Message;constructor(e){super({name:"ResourceInUseException",$fault:"client",...e}),Object.setPrototypeOf(this,z.prototype),this.Message=e.Message}}class B extends n.n{name="SubnetNotFoundException";$fault="client";Message;constructor(e){super({name:"SubnetNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,B.prototype),this.Message=e.Message}}class K extends n.n{name="TooManyLoadBalancersException";$fault="client";Message;constructor(e){super({name:"TooManyLoadBalancersException",$fault:"client",...e}),Object.setPrototypeOf(this,K.prototype),this.Message=e.Message}}class V extends n.n{name="PriorityInUseException";$fault="client";Message;constructor(e){super({name:"PriorityInUseException",$fault:"client",...e}),Object.setPrototypeOf(this,V.prototype),this.Message=e.Message}}class H extends n.n{name="TooManyRulesException";$fault="client";Message;constructor(e){super({name:"TooManyRulesException",$fault:"client",...e}),Object.setPrototypeOf(this,H.prototype),this.Message=e.Message}}class q extends n.n{name="TooManyTargetGroupsException";$fault="client";Message;constructor(e){super({name:"TooManyTargetGroupsException",$fault:"client",...e}),Object.setPrototypeOf(this,q.prototype),this.Message=e.Message}}class G extends n.n{name="DuplicateTargetGroupNameException";$fault="client";Message;constructor(e){super({name:"DuplicateTargetGroupNameException",$fault:"client",...e}),Object.setPrototypeOf(this,G.prototype),this.Message=e.Message}}class W extends n.n{name="DuplicateTrustStoreNameException";$fault="client";Message;constructor(e){super({name:"DuplicateTrustStoreNameException",$fault:"client",...e}),Object.setPrototypeOf(this,W.prototype),this.Message=e.Message}}class X extends n.n{name="InvalidCaCertificatesBundleException";$fault="client";Message;constructor(e){super({name:"InvalidCaCertificatesBundleException",$fault:"client",...e}),Object.setPrototypeOf(this,X.prototype),this.Message=e.Message}}class Y extends n.n{name="TooManyTrustStoresException";$fault="client";Message;constructor(e){super({name:"TooManyTrustStoresException",$fault:"client",...e}),Object.setPrototypeOf(this,Y.prototype),this.Message=e.Message}}class J extends n.n{name="DeleteAssociationSameAccountException";$fault="client";Message;constructor(e){super({name:"DeleteAssociationSameAccountException",$fault:"client",...e}),Object.setPrototypeOf(this,J.prototype),this.Message=e.Message}}class Z extends n.n{name="TrustStoreAssociationNotFoundException";$fault="client";Message;constructor(e){super({name:"TrustStoreAssociationNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,Z.prototype),this.Message=e.Message}}class Q extends n.n{name="TrustStoreInUseException";$fault="client";Message;constructor(e){super({name:"TrustStoreInUseException",$fault:"client",...e}),Object.setPrototypeOf(this,Q.prototype),this.Message=e.Message}}class ee extends n.n{name="InvalidTargetException";$fault="client";Message;constructor(e){super({name:"InvalidTargetException",$fault:"client",...e}),Object.setPrototypeOf(this,ee.prototype),this.Message=e.Message}}class et extends n.n{name="HealthUnavailableException";$fault="server";Message;constructor(e){super({name:"HealthUnavailableException",$fault:"server",...e}),Object.setPrototypeOf(this,et.prototype),this.Message=e.Message}}class er extends n.n{name="RevocationIdNotFoundException";$fault="client";Message;constructor(e){super({name:"RevocationIdNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,er.prototype),this.Message=e.Message}}class en extends n.n{name="ResourceNotFoundException";$fault="client";Message;constructor(e){super({name:"ResourceNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,en.prototype),this.Message=e.Message}}class es extends n.n{name="InsufficientCapacityException";$fault="server";Message;constructor(e){super({name:"InsufficientCapacityException",$fault:"server",...e}),Object.setPrototypeOf(this,es.prototype),this.Message=e.Message}}class ei extends n.n{name="PriorRequestNotCompleteException";$fault="client";Message;constructor(e){super({name:"PriorRequestNotCompleteException",$fault:"client",...e}),Object.setPrototypeOf(this,ei.prototype),this.Message=e.Message}}},2015:(e,t,r)=>{"use strict";r.d(t,{p:()=>ek});var n=r(1095),s=r(5937),i=r(8377),o=r(4383);let a=void 0;var c=r(8636);let u=(e,t=!1)=>{if(t){for(let t of e.split("."))if(!u(t))return!1;return!0}return!(!(0,c.X8)(e)||e.length<3||e.length>63||e!==e.toLowerCase()||(0,c.oX)(e))},l=JSON.parse('{"partitions":[{"id":"aws","outputs":{"dnsSuffix":"amazonaws.com","dualStackDnsSuffix":"api.aws","implicitGlobalRegion":"us-east-1","name":"aws","supportsDualStack":true,"supportsFIPS":true},"regionRegex":"^(us|eu|ap|sa|ca|me|af|il|mx)\\\\-\\\\w+\\\\-\\\\d+$","regions":{"af-south-1":{"description":"Africa (Cape Town)"},"ap-east-1":{"description":"Asia Pacific (Hong Kong)"},"ap-northeast-1":{"description":"Asia Pacific (Tokyo)"},"ap-northeast-2":{"description":"Asia Pacific (Seoul)"},"ap-northeast-3":{"description":"Asia Pacific (Osaka)"},"ap-south-1":{"description":"Asia Pacific (Mumbai)"},"ap-south-2":{"description":"Asia Pacific (Hyderabad)"},"ap-southeast-1":{"description":"Asia Pacific (Singapore)"},"ap-southeast-2":{"description":"Asia Pacific (Sydney)"},"ap-southeast-3":{"description":"Asia Pacific (Jakarta)"},"ap-southeast-4":{"description":"Asia Pacific (Melbourne)"},"ap-southeast-5":{"description":"Asia Pacific (Malaysia)"},"ap-southeast-7":{"description":"Asia Pacific (Thailand)"},"aws-global":{"description":"AWS Standard global region"},"ca-central-1":{"description":"Canada (Central)"},"ca-west-1":{"description":"Canada West (Calgary)"},"eu-central-1":{"description":"Europe (Frankfurt)"},"eu-central-2":{"description":"Europe (Zurich)"},"eu-north-1":{"description":"Europe (Stockholm)"},"eu-south-1":{"description":"Europe (Milan)"},"eu-south-2":{"description":"Europe (Spain)"},"eu-west-1":{"description":"Europe (Ireland)"},"eu-west-2":{"description":"Europe (London)"},"eu-west-3":{"description":"Europe (Paris)"},"il-central-1":{"description":"Israel (Tel Aviv)"},"me-central-1":{"description":"Middle East (UAE)"},"me-south-1":{"description":"Middle East (Bahrain)"},"mx-central-1":{"description":"Mexico (Central)"},"sa-east-1":{"description":"South America (Sao Paulo)"},"us-east-1":{"description":"US East (N. Virginia)"},"us-east-2":{"description":"US East (Ohio)"},"us-west-1":{"description":"US West (N. California)"},"us-west-2":{"description":"US West (Oregon)"}}},{"id":"aws-cn","outputs":{"dnsSuffix":"amazonaws.com.cn","dualStackDnsSuffix":"api.amazonwebservices.com.cn","implicitGlobalRegion":"cn-northwest-1","name":"aws-cn","supportsDualStack":true,"supportsFIPS":true},"regionRegex":"^cn\\\\-\\\\w+\\\\-\\\\d+$","regions":{"aws-cn-global":{"description":"AWS China global region"},"cn-north-1":{"description":"China (Beijing)"},"cn-northwest-1":{"description":"China (Ningxia)"}}},{"id":"aws-us-gov","outputs":{"dnsSuffix":"amazonaws.com","dualStackDnsSuffix":"api.aws","implicitGlobalRegion":"us-gov-west-1","name":"aws-us-gov","supportsDualStack":true,"supportsFIPS":true},"regionRegex":"^us\\\\-gov\\\\-\\\\w+\\\\-\\\\d+$","regions":{"aws-us-gov-global":{"description":"AWS GovCloud (US) global region"},"us-gov-east-1":{"description":"AWS GovCloud (US-East)"},"us-gov-west-1":{"description":"AWS GovCloud (US-West)"}}},{"id":"aws-iso","outputs":{"dnsSuffix":"c2s.ic.gov","dualStackDnsSuffix":"c2s.ic.gov","implicitGlobalRegion":"us-iso-east-1","name":"aws-iso","supportsDualStack":false,"supportsFIPS":true},"regionRegex":"^us\\\\-iso\\\\-\\\\w+\\\\-\\\\d+$","regions":{"aws-iso-global":{"description":"AWS ISO (US) global region"},"us-iso-east-1":{"description":"US ISO East"},"us-iso-west-1":{"description":"US ISO WEST"}}},{"id":"aws-iso-b","outputs":{"dnsSuffix":"sc2s.sgov.gov","dualStackDnsSuffix":"sc2s.sgov.gov","implicitGlobalRegion":"us-isob-east-1","name":"aws-iso-b","supportsDualStack":false,"supportsFIPS":true},"regionRegex":"^us\\\\-isob\\\\-\\\\w+\\\\-\\\\d+$","regions":{"aws-iso-b-global":{"description":"AWS ISOB (US) global region"},"us-isob-east-1":{"description":"US ISOB East (Ohio)"}}},{"id":"aws-iso-e","outputs":{"dnsSuffix":"cloud.adc-e.uk","dualStackDnsSuffix":"cloud.adc-e.uk","implicitGlobalRegion":"eu-isoe-west-1","name":"aws-iso-e","supportsDualStack":false,"supportsFIPS":true},"regionRegex":"^eu\\\\-isoe\\\\-\\\\w+\\\\-\\\\d+$","regions":{"eu-isoe-west-1":{"description":"EU ISOE West"}}},{"id":"aws-iso-f","outputs":{"dnsSuffix":"csp.hci.ic.gov","dualStackDnsSuffix":"csp.hci.ic.gov","implicitGlobalRegion":"us-isof-south-1","name":"aws-iso-f","supportsDualStack":false,"supportsFIPS":true},"regionRegex":"^us\\\\-isof\\\\-\\\\w+\\\\-\\\\d+$","regions":{"aws-iso-f-global":{"description":"AWS ISOF global region"},"us-isof-east-1":{"description":"US ISOF EAST"},"us-isof-south-1":{"description":"US ISOF SOUTH"}}}],"version":"1.1"}'),d=()=>"",p={isVirtualHostableS3Bucket:u,parseArn:e=>{let t=e.split(":");if(t.length<6)return null;let[r,n,s,i,o,...a]=t;return"arn"!==r||""===n||""===s||""===a.join(":")?null:{partition:n,service:s,region:i,accountId:o,resourceId:a.map(e=>e.split("/")).flat()}},partition:e=>{let{partitions:t}=l;for(let r of t){let{regions:t,outputs:n}=r;for(let[r,s]of Object.entries(t))if(r===e)return{...n,...s}}for(let r of t){let{regionRegex:t,outputs:n}=r;if(new RegExp(t).test(e))return{...n}}let r=t.find(e=>"aws"===e.id);if(!r)throw Error("Provided region was not found in the partition array or regex, and default partition with id 'aws' doesn't exist.");return{...r.outputs}}};c.mw.aws=p;var f=r(7609);function h(e,t,r){e.__aws_sdk_context?e.__aws_sdk_context.features||(e.__aws_sdk_context.features={}):e.__aws_sdk_context={features:{}},e.__aws_sdk_context.features[t]=r}let m=/\d{12}\.ddb/;async function y(e,t,r){let n=r.request;if(n?.headers?.["smithy-protocol"]==="rpc-v2-cbor"&&h(e,"PROTOCOL_RPC_V2_CBOR","M"),"function"==typeof t.retryStrategy){let r=await t.retryStrategy();"function"==typeof r.acquireInitialRetryToken?r.constructor?.name?.includes("Adaptive")?h(e,"RETRY_MODE_ADAPTIVE","F"):h(e,"RETRY_MODE_STANDARD","E"):h(e,"RETRY_MODE_LEGACY","D")}if("function"==typeof t.accountIdEndpointMode){let r=e.endpointV2;switch(String(r?.url?.hostname).match(m)&&h(e,"ACCOUNT_ID_ENDPOINT","O"),await t.accountIdEndpointMode?.()){case"disabled":h(e,"ACCOUNT_ID_MODE_DISABLED","Q");break;case"preferred":h(e,"ACCOUNT_ID_MODE_PREFERRED","P");break;case"required":h(e,"ACCOUNT_ID_MODE_REQUIRED","R")}}let s=e.__smithy_context?.selectedHttpAuthScheme?.identity;if(s?.$source)for(let[t,r]of(s.accountId&&h(e,"RESOLVED_ACCOUNT_ID","T"),Object.entries(s.$source??{})))h(e,t,r)}let g="user-agent",x="x-amz-user-agent",b=/[^\!\$\%\&\'\*\+\-\.\^\_\`\|\~\d\w]/g,w=/[^\!\$\%\&\'\*\+\-\.\^\_\`\|\~\d\w\#]/g,v=e=>(t,r)=>async n=>{let{request:s}=n;if(!f.Kd.isInstance(s))return t(n);let{headers:i}=s,o=r?.userAgent?.map(E)||[],a=(await e.defaultUserAgentProvider()).map(E);await y(r,e,n),a.push(`m/${function(e){let t="";for(let r in e){let n=e[r];if(t.length+n.length+1<=1024){t.length?t+=","+n:t+=n;continue}break}return t}(Object.assign({},r.__smithy_context?.features,r.__aws_sdk_context?.features))}`);let c=e?.customUserAgent?.map(E)||[],u=await e.userAgentAppId();u&&a.push(E([`app/${u}`]));let l=d(),p=(l?[l]:[]).concat([...a,...o,...c]).join(" "),h=[...a.filter(e=>e.startsWith("aws-sdk-")),...c].join(" ");return"browser"!==e.runtime?(h&&(i[x]=i[x]?`${i[g]} ${h}`:h),i[g]=p):i[x]=p,t({...n,request:s})},E=e=>{let t=e[0].split("/").map(e=>e.replace(b,"-")).join("/"),r=e[1]?.replace(w,"-"),n=t.indexOf("/"),s=t.substring(0,n),i=t.substring(n+1);return"api"===s&&(i=i.toLowerCase()),[s,i,r].filter(e=>e&&e.length>0).reduce((e,t,r)=>{switch(r){case 0:return t;case 1:return`${e}/${t}`;default:return`${e}#${t}`}},"")},S={name:"getUserAgentMiddleware",step:"build",priority:"low",tags:["SET_USER_AGENT","USER_AGENT"],override:!0},P=e=>({applyToStack:t=>{t.add(v(e),S)}});var O=r(1651),$=r(649),A=r(8176),M=r(6320),I=r(5268),C=r(8599);let k=e=>{let t,r,n=!1;e.credentials&&(n=!0,t=(0,o.K4)(e.credentials,o.OC,o.e)),t||(t=e.credentialDefaultProvider?(0,o.te)(e.credentialDefaultProvider(Object.assign({},e,{parentClientConfig:e}))):async()=>{throw Error("`credentials` is missing")});let s=async()=>t({callerClientConfig:e}),{signingEscapePath:i=!0,systemClockOffset:a=e.systemClockOffset||0,sha256:c}=e;return r=e.signer?(0,o.te)(e.signer):e.regionInfoProvider?()=>(0,o.te)(e.region)().then(async t=>[await e.regionInfoProvider(t,{useFipsEndpoint:await e.useFipsEndpoint(),useDualstackEndpoint:await e.useDualstackEndpoint()})||{},t]).then(([t,r])=>{let{signingRegion:n,signingService:o}=t;e.signingRegion=e.signingRegion||n||r,e.signingName=e.signingName||o||e.serviceId;let a={...e,credentials:s,region:e.signingRegion,service:e.signingName,sha256:c,uriEscapePath:i};return new(e.signerConstructor||C.BB)(a)}):async t=>{let r=(t=Object.assign({},{name:"sigv4",signingName:e.signingName||e.defaultSigningName,signingRegion:await (0,o.te)(e.region)(),properties:{}},t)).signingRegion,n=t.signingName;e.signingRegion=e.signingRegion||r,e.signingName=e.signingName||n||e.serviceId;let a={...e,credentials:s,region:e.signingRegion,service:e.signingName,sha256:c,uriEscapePath:i};return new(e.signerConstructor||C.BB)(a)},{...e,systemClockOffset:a,signingEscapePath:i,credentials:n?async()=>s().then(e=>(function(e,t,r){return e.$source||(e.$source={}),e.$source[t]="e",e})(e,"CREDENTIALS_CODE",0)):s,signer:r}};var T=r(9848);let R=async(e,t,r)=>({operation:(0,T.u)(t).operation,region:await (0,T.t)(e.region)()||(()=>{throw Error("expected `region` to be configured for `aws.auth#sigv4`")})()}),N=e=>{let t=[];return e.operation,t.push({schemeId:"aws.auth#sigv4",signingProperties:{name:"kms",region:e.region},propertiesExtractor:(e,t)=>({signingProperties:{config:e,context:t}})}),t},D=e=>({...k(e)});var U=r(2814);let j={rE:"3.750.0"};var F=r(5071),_=r(9391),L=r(9124),z=r(2423),B=r(6850),K=r(2073);let V=e=>new Date(Date.now()+e),H=e=>f.cS.isInstance(e)?e.headers?.date??e.headers?.Date:void 0,q=(e,t)=>Math.abs(V(t).getTime()-e)>=3e5,G=(e,t)=>{let r=Date.parse(e);return q(r,t)?r-Date.now():t},W=(e,t)=>{if(!t)throw Error(`Property \`${e}\` is not resolved for AWS SDK SigV4Auth`);return t},X=async e=>{let t=W("context",e.context),r=W("config",e.config),n=t.endpointV2?.properties?.authSchemes?.[0],s=W("signer",r.signer),i=await s(n);return{config:r,signer:i,signingRegion:e?.signingRegion,signingRegionSet:e?.signingRegionSet,signingName:e?.signingName}};class Y{async sign(e,t,r){if(!f.Kd.isInstance(e))throw Error("The request is not an instance of `HttpRequest` and cannot be signed");let n=await X(r),{config:s,signer:i}=n,{signingRegion:o,signingName:a}=n,c=r.context;if(c?.authSchemes?.length){let[e,t]=c.authSchemes;e?.name==="sigv4a"&&t?.name==="sigv4"&&(o=t?.signingRegion??o,a=t?.signingName??a)}return await i.sign(e,{signingDate:V(s.systemClockOffset),signingRegion:o,signingService:a})}errorHandler(e){return t=>{let r=t.ServerTime??H(t.$response);if(r){let n=W("config",e.config),s=n.systemClockOffset;n.systemClockOffset=G(r,n.systemClockOffset),n.systemClockOffset!==s&&t.$metadata&&(t.$metadata.clockSkewCorrected=!0)}throw t}}successHandler(e,t){let r=H(e);if(r){let e=W("config",t.config);e.systemClockOffset=G(r,e.systemClockOffset)}}}var J=r(4262),Z=r(9764),Q=r(2637),ee=r(3411);let et="required",er="argv",en="isSet",es="booleanEquals",ei="error",eo="endpoint",ea="tree",ec="PartitionResult",eu={[et]:!1,type:"String"},el={[et]:!0,default:!1,type:"Boolean"},ed={ref:"Endpoint"},ep={fn:es,[er]:[{ref:"UseFIPS"},!0]},ef={fn:es,[er]:[{ref:"UseDualStack"},!0]},eh={},em={fn:"getAttr",[er]:[{ref:ec},"supportsFIPS"]},ey={fn:es,[er]:[!0,{fn:"getAttr",[er]:[{ref:ec},"supportsDualStack"]}]},eg=[ep],ex=[ef],eb=[{ref:"Region"}],ew={version:"1.0",parameters:{Region:eu,UseDualStack:el,UseFIPS:el,Endpoint:eu},rules:[{conditions:[{fn:en,[er]:[ed]}],rules:[{conditions:eg,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:ei},{conditions:ex,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:ei},{endpoint:{url:ed,properties:eh,headers:eh},type:eo}],type:ea},{conditions:[{fn:en,[er]:eb}],rules:[{conditions:[{fn:"aws.partition",[er]:eb,assign:ec}],rules:[{conditions:[ep,ef],rules:[{conditions:[{fn:es,[er]:[!0,em]},ey],rules:[{endpoint:{url:"https://kms-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:eh,headers:eh},type:eo}],type:ea},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:ei}],type:ea},{conditions:eg,rules:[{conditions:[{fn:es,[er]:[em,!0]}],rules:[{endpoint:{url:"https://kms-fips.{Region}.{PartitionResult#dnsSuffix}",properties:eh,headers:eh},type:eo}],type:ea},{error:"FIPS is enabled but this partition does not support FIPS",type:ei}],type:ea},{conditions:ex,rules:[{conditions:[ey],rules:[{endpoint:{url:"https://kms.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:eh,headers:eh},type:eo}],type:ea},{error:"DualStack is enabled but this partition does not support DualStack",type:ei}],type:ea},{endpoint:{url:"https://kms.{Region}.{PartitionResult#dnsSuffix}",properties:eh,headers:eh},type:eo}],type:ea}],type:ea},{error:"Invalid Configuration: Missing Region",type:ei}]},ev=new c.kS({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),eE=(e,t={})=>ev.get(e,()=>(0,c.sO)(ew,{endpointParams:e,logger:t.logger}));c.mw.aws=p;let eS=e=>({apiVersion:"2014-11-01",base64Decoder:e?.base64Decoder??Z.E,base64Encoder:e?.base64Encoder??Z.n,disableHostPrefix:e?.disableHostPrefix??!1,endpointProvider:e?.endpointProvider??eE,extensions:e?.extensions??[],httpAuthSchemeProvider:e?.httpAuthSchemeProvider??N,httpAuthSchemes:e?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:e=>e.getIdentityProvider("aws.auth#sigv4"),signer:new Y}],logger:e?.logger??new I.N4,serviceId:e?.serviceId??"KMS",urlParser:e?.urlParser??J.D,utf8Decoder:e?.utf8Decoder??Q.a,utf8Encoder:e?.utf8Encoder??ee.P});var eP=r(1996);let eO=e=>{let t=(0,eP.I)(e),r=()=>t().then(I.lT),n=eS(e);return{...n,...e,runtime:"browser",defaultsMode:t,bodyLengthChecker:e?.bodyLengthChecker??B.n,credentialDefaultProvider:e?.credentialDefaultProvider??(e=>()=>Promise.reject(Error("Credential is missing"))),defaultUserAgentProvider:e?.defaultUserAgentProvider??(0,_.p)({serviceId:n.serviceId,clientVersion:j.rE}),maxAttempts:e?.maxAttempts??K.Gz,region:e?.region??(0,z.B)("Region is missing"),requestHandler:L.NC.create(e?.requestHandler??r),retryMode:e?.retryMode??(async()=>(await r()).retryMode||K.L0),sha256:e?.sha256??F.I,streamCollector:e?.streamCollector??L.kv,useDualstackEndpoint:e?.useDualstackEndpoint??(()=>Promise.resolve(O.VW)),useFipsEndpoint:e?.useFipsEndpoint??(()=>Promise.resolve(O.Hj))}};var e$=r(3614);let eA=e=>{let t=e.httpAuthSchemes,r=e.httpAuthSchemeProvider,n=e.credentials;return{setHttpAuthScheme(e){let r=t.findIndex(t=>t.schemeId===e.schemeId);-1===r?t.push(e):t.splice(r,1,e)},httpAuthSchemes:()=>t,setHttpAuthSchemeProvider(e){r=e},httpAuthSchemeProvider:()=>r,setCredentials(e){n=e},credentials:()=>n}},eM=e=>({httpAuthSchemes:e.httpAuthSchemes(),httpAuthSchemeProvider:e.httpAuthSchemeProvider(),credentials:e.credentials()}),eI=e=>e,eC=(e,t)=>{let r={...eI((0,e$.Rq)(e)),...eI((0,I.xA)(e)),...eI((0,f.eS)(e)),...eI(eA(e))};return t.forEach(e=>e.configure(r)),{...e,...(0,e$.$3)(r),...(0,I.uv)(r),...(0,f.jt)(r),...eM(r)}};class ek extends I.Kj{config;constructor(...[e]){let t=eO(e||{}),r=function(e){let t=(0,o.te)(e.userAgentAppId??a);return{...e,customUserAgent:"string"==typeof e.customUserAgent?[[e.customUserAgent]]:e.customUserAgent,userAgentAppId:async()=>{let r=await t();if(!(void 0===r||"string"==typeof r&&r.length<=50)){let t=e.logger?.constructor?.name!=="NoOpLogger"&&e.logger?e.logger:console;"string"!=typeof r?t?.warn("userAgentAppId must be a string or undefined."):r.length>50&&t?.warn("The provided userAgentAppId exceeds the maximum length of 50 characters.")}return r}}}((0,U.v)(t)),c=(0,M.$z)(r),u=(0,O.TD)(c),l=(0,n.OV)(u),d=eC(D((0,A.Co)(l)),e?.extensions||[]);super(d),this.config=d,this.middlewareStack.use(P(this.config)),this.middlewareStack.use((0,M.ey)(this.config)),this.middlewareStack.use((0,$.vK)(this.config)),this.middlewareStack.use((0,n.TC)(this.config)),this.middlewareStack.use((0,s.Y7)(this.config)),this.middlewareStack.use((0,i.n4)(this.config)),this.middlewareStack.use((0,o.wB)(this.config,{httpAuthSchemeParametersProvider:R,identityProviderConfigProvider:async e=>new o.h$({"aws.auth#sigv4":e.credentials})})),this.middlewareStack.use((0,o.lW)(this.config))}destroy(){super.destroy()}}},8432:(e,t,r)=>{"use strict";r.d(t,{v:()=>u});var n=r(8176),s=r(228),i=r(5268),o=r(2814),a=r(1079),c=r(5587);class u extends i.uB.classBuilder().ep(o.S).m(function(e,t,r,i){return[(0,s.TM)(r,this.serialize,this.deserialize),(0,n.rD)(r,e.getEndpointParameterInstructions())]}).s("TrentService","Decrypt",{}).n("KMSClient","DecryptCommand").f(void 0,a.fj).ser(c._E).de(c.jN).build(){}},6232:(e,t,r)=>{"use strict";r.d(t,{b:()=>u});var n=r(8176),s=r(228),i=r(5268),o=r(2814),a=r(1079),c=r(5587);class u extends i.uB.classBuilder().ep(o.S).m(function(e,t,r,i){return[(0,s.TM)(r,this.serialize,this.deserialize),(0,n.rD)(r,e.getEndpointParameterInstructions())]}).s("TrentService","Encrypt",{}).n("KMSClient","EncryptCommand").f(a.zO,void 0).ser(c.eS).de(c.x8).build(){}},2814:(e,t,r)=>{"use strict";r.d(t,{S:()=>s,v:()=>n});let n=e=>({...e,useDualstackEndpoint:e.useDualstackEndpoint??!1,useFipsEndpoint:e.useFipsEndpoint??!1,defaultSigningName:"kms"}),s={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}}},8873:(e,t,r)=>{"use strict";r.d(t,{G:()=>s});var n=r(5268);class s extends n.TJ{constructor(e){super(e),Object.setPrototypeOf(this,s.prototype)}}},1079:(e,t,r)=>{"use strict";r.d(t,{$U:()=>u,AD:()=>_,B$:()=>F,BK:()=>$,Bo:()=>G,Bx:()=>g,C2:()=>b,ET:()=>M,Fo:()=>m,G1:()=>k,H5:()=>J,Hv:()=>R,IF:()=>Y,Jm:()=>P,ML:()=>A,MW:()=>v,Qy:()=>x,S5:()=>T,T2:()=>d,Tk:()=>q,UC:()=>w,X1:()=>z,X6:()=>l,Yz:()=>c,_Z:()=>D,bg:()=>V,cb:()=>y,dt:()=>H,e$:()=>j,eJ:()=>Z,eo:()=>o,fj:()=>Q,iK:()=>i,iu:()=>h,ju:()=>W,m9:()=>O,oP:()=>U,oZ:()=>E,ot:()=>L,r8:()=>X,rn:()=>I,s7:()=>N,su:()=>C,w$:()=>a,wh:()=>f,wj:()=>B,wl:()=>p,x2:()=>K,ye:()=>S,zO:()=>ee});var n=r(5268),s=r(8873);class i extends s.G{name="AlreadyExistsException";$fault="client";constructor(e){super({name:"AlreadyExistsException",$fault:"client",...e}),Object.setPrototypeOf(this,i.prototype)}}class o extends s.G{name="DependencyTimeoutException";$fault="server";constructor(e){super({name:"DependencyTimeoutException",$fault:"server",...e}),Object.setPrototypeOf(this,o.prototype)}}class a extends s.G{name="InvalidArnException";$fault="client";constructor(e){super({name:"InvalidArnException",$fault:"client",...e}),Object.setPrototypeOf(this,a.prototype)}}class c extends s.G{name="KMSInternalException";$fault="server";constructor(e){super({name:"KMSInternalException",$fault:"server",...e}),Object.setPrototypeOf(this,c.prototype)}}class u extends s.G{name="KMSInvalidStateException";$fault="client";constructor(e){super({name:"KMSInvalidStateException",$fault:"client",...e}),Object.setPrototypeOf(this,u.prototype)}}class l extends s.G{name="NotFoundException";$fault="client";constructor(e){super({name:"NotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,l.prototype)}}class d extends s.G{name="CloudHsmClusterInUseException";$fault="client";constructor(e){super({name:"CloudHsmClusterInUseException",$fault:"client",...e}),Object.setPrototypeOf(this,d.prototype)}}class p extends s.G{name="CloudHsmClusterInvalidConfigurationException";$fault="client";constructor(e){super({name:"CloudHsmClusterInvalidConfigurationException",$fault:"client",...e}),Object.setPrototypeOf(this,p.prototype)}}class f extends s.G{name="CloudHsmClusterNotActiveException";$fault="client";constructor(e){super({name:"CloudHsmClusterNotActiveException",$fault:"client",...e}),Object.setPrototypeOf(this,f.prototype)}}class h extends s.G{name="CloudHsmClusterNotFoundException";$fault="client";constructor(e){super({name:"CloudHsmClusterNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,h.prototype)}}class m extends s.G{name="CloudHsmClusterNotRelatedException";$fault="client";constructor(e){super({name:"CloudHsmClusterNotRelatedException",$fault:"client",...e}),Object.setPrototypeOf(this,m.prototype)}}class y extends s.G{name="ConflictException";$fault="client";constructor(e){super({name:"ConflictException",$fault:"client",...e}),Object.setPrototypeOf(this,y.prototype)}}class g extends s.G{name="CustomKeyStoreInvalidStateException";$fault="client";constructor(e){super({name:"CustomKeyStoreInvalidStateException",$fault:"client",...e}),Object.setPrototypeOf(this,g.prototype)}}class x extends s.G{name="CustomKeyStoreNotFoundException";$fault="client";constructor(e){super({name:"CustomKeyStoreNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,x.prototype)}}class b extends s.G{name="InvalidAliasNameException";$fault="client";constructor(e){super({name:"InvalidAliasNameException",$fault:"client",...e}),Object.setPrototypeOf(this,b.prototype)}}class w extends s.G{name="LimitExceededException";$fault="client";constructor(e){super({name:"LimitExceededException",$fault:"client",...e}),Object.setPrototypeOf(this,w.prototype)}}class v extends s.G{name="CustomKeyStoreNameInUseException";$fault="client";constructor(e){super({name:"CustomKeyStoreNameInUseException",$fault:"client",...e}),Object.setPrototypeOf(this,v.prototype)}}class E extends s.G{name="IncorrectTrustAnchorException";$fault="client";constructor(e){super({name:"IncorrectTrustAnchorException",$fault:"client",...e}),Object.setPrototypeOf(this,E.prototype)}}class S extends s.G{name="XksProxyIncorrectAuthenticationCredentialException";$fault="client";constructor(e){super({name:"XksProxyIncorrectAuthenticationCredentialException",$fault:"client",...e}),Object.setPrototypeOf(this,S.prototype)}}class P extends s.G{name="XksProxyInvalidConfigurationException";$fault="client";constructor(e){super({name:"XksProxyInvalidConfigurationException",$fault:"client",...e}),Object.setPrototypeOf(this,P.prototype)}}class O extends s.G{name="XksProxyInvalidResponseException";$fault="client";constructor(e){super({name:"XksProxyInvalidResponseException",$fault:"client",...e}),Object.setPrototypeOf(this,O.prototype)}}class $ extends s.G{name="XksProxyUriEndpointInUseException";$fault="client";constructor(e){super({name:"XksProxyUriEndpointInUseException",$fault:"client",...e}),Object.setPrototypeOf(this,$.prototype)}}class A extends s.G{name="XksProxyUriInUseException";$fault="client";constructor(e){super({name:"XksProxyUriInUseException",$fault:"client",...e}),Object.setPrototypeOf(this,A.prototype)}}class M extends s.G{name="XksProxyUriUnreachableException";$fault="client";constructor(e){super({name:"XksProxyUriUnreachableException",$fault:"client",...e}),Object.setPrototypeOf(this,M.prototype)}}class I extends s.G{name="XksProxyVpcEndpointServiceInUseException";$fault="client";constructor(e){super({name:"XksProxyVpcEndpointServiceInUseException",$fault:"client",...e}),Object.setPrototypeOf(this,I.prototype)}}class C extends s.G{name="XksProxyVpcEndpointServiceInvalidConfigurationException";$fault="client";constructor(e){super({name:"XksProxyVpcEndpointServiceInvalidConfigurationException",$fault:"client",...e}),Object.setPrototypeOf(this,C.prototype)}}class k extends s.G{name="XksProxyVpcEndpointServiceNotFoundException";$fault="client";constructor(e){super({name:"XksProxyVpcEndpointServiceNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,k.prototype)}}class T extends s.G{name="DisabledException";$fault="client";constructor(e){super({name:"DisabledException",$fault:"client",...e}),Object.setPrototypeOf(this,T.prototype)}}class R extends s.G{name="DryRunOperationException";$fault="client";constructor(e){super({name:"DryRunOperationException",$fault:"client",...e}),Object.setPrototypeOf(this,R.prototype)}}class N extends s.G{name="InvalidGrantTokenException";$fault="client";constructor(e){super({name:"InvalidGrantTokenException",$fault:"client",...e}),Object.setPrototypeOf(this,N.prototype)}}class D extends s.G{name="MalformedPolicyDocumentException";$fault="client";constructor(e){super({name:"MalformedPolicyDocumentException",$fault:"client",...e}),Object.setPrototypeOf(this,D.prototype)}}class U extends s.G{name="TagException";$fault="client";constructor(e){super({name:"TagException",$fault:"client",...e}),Object.setPrototypeOf(this,U.prototype)}}class j extends s.G{name="UnsupportedOperationException";$fault="client";constructor(e){super({name:"UnsupportedOperationException",$fault:"client",...e}),Object.setPrototypeOf(this,j.prototype)}}class F extends s.G{name="XksKeyAlreadyInUseException";$fault="client";constructor(e){super({name:"XksKeyAlreadyInUseException",$fault:"client",...e}),Object.setPrototypeOf(this,F.prototype)}}class _ extends s.G{name="XksKeyInvalidConfigurationException";$fault="client";constructor(e){super({name:"XksKeyInvalidConfigurationException",$fault:"client",...e}),Object.setPrototypeOf(this,_.prototype)}}class L extends s.G{name="XksKeyNotFoundException";$fault="client";constructor(e){super({name:"XksKeyNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,L.prototype)}}class z extends s.G{name="CustomKeyStoreHasCMKsException";$fault="client";constructor(e){super({name:"CustomKeyStoreHasCMKsException",$fault:"client",...e}),Object.setPrototypeOf(this,z.prototype)}}class B extends s.G{name="IncorrectKeyException";$fault="client";constructor(e){super({name:"IncorrectKeyException",$fault:"client",...e}),Object.setPrototypeOf(this,B.prototype)}}class K extends s.G{name="InvalidCiphertextException";$fault="client";constructor(e){super({name:"InvalidCiphertextException",$fault:"client",...e}),Object.setPrototypeOf(this,K.prototype)}}class V extends s.G{name="InvalidKeyUsageException";$fault="client";constructor(e){super({name:"InvalidKeyUsageException",$fault:"client",...e}),Object.setPrototypeOf(this,V.prototype)}}class H extends s.G{name="KeyUnavailableException";$fault="server";constructor(e){super({name:"KeyUnavailableException",$fault:"server",...e}),Object.setPrototypeOf(this,H.prototype)}}class q extends s.G{name="InvalidMarkerException";$fault="client";constructor(e){super({name:"InvalidMarkerException",$fault:"client",...e}),Object.setPrototypeOf(this,q.prototype)}}class G extends s.G{name="ExpiredImportTokenException";$fault="client";constructor(e){super({name:"ExpiredImportTokenException",$fault:"client",...e}),Object.setPrototypeOf(this,G.prototype)}}class W extends s.G{name="IncorrectKeyMaterialException";$fault="client";constructor(e){super({name:"IncorrectKeyMaterialException",$fault:"client",...e}),Object.setPrototypeOf(this,W.prototype)}}class X extends s.G{name="InvalidImportTokenException";$fault="client";constructor(e){super({name:"InvalidImportTokenException",$fault:"client",...e}),Object.setPrototypeOf(this,X.prototype)}}class Y extends s.G{name="InvalidGrantIdException";$fault="client";constructor(e){super({name:"InvalidGrantIdException",$fault:"client",...e}),Object.setPrototypeOf(this,Y.prototype)}}class J extends s.G{name="KMSInvalidMacException";$fault="client";constructor(e){super({name:"KMSInvalidMacException",$fault:"client",...e}),Object.setPrototypeOf(this,J.prototype)}}class Z extends s.G{name="KMSInvalidSignatureException";$fault="client";constructor(e){super({name:"KMSInvalidSignatureException",$fault:"client",...e}),Object.setPrototypeOf(this,Z.prototype)}}let Q=e=>({...e,...e.Plaintext&&{Plaintext:n.$H}}),ee=e=>({...e,...e.Plaintext&&{Plaintext:n.$H}})},5587:(e,t,r)=>{"use strict";r.d(t,{jN:()=>f,x8:()=>h,_E:()=>d,eS:()=>p});var n=r(5268);let s=(e,t)=>(0,n.Px)(e,t).then(e=>t.utf8Encoder(e)),i=(e,t)=>s(e,t).then(e=>{if(e.length)try{return JSON.parse(e)}catch(t){throw t?.name==="SyntaxError"&&Object.defineProperty(t,"$responseBodyText",{value:e}),t}return{}}),o=async(e,t)=>{let r=await i(e,t);return r.message=r.message??r.Message,r},a=(e,t)=>{let r=e=>{let t=e;return"number"==typeof t&&(t=t.toString()),t.indexOf(",")>=0&&(t=t.split(",")[0]),t.indexOf(":")>=0&&(t=t.split(":")[0]),t.indexOf("#")>=0&&(t=t.split("#")[1]),t},n=Object.keys(e.headers).find(e=>"x-amzn-errortype"===e.toLowerCase());return void 0!==n?r(e.headers[n]):void 0!==t.code?r(t.code):void 0!==t.__type?r(t.__type):void 0};var c=r(7609),u=r(8873),l=r(1079);let d=async(e,t)=>{let r;let n=ex("Decrypt");return r=JSON.stringify(el(e,t)),eg(t,n,"/",void 0,r)},p=async(e,t)=>{let r;let n=ex("Encrypt");return r=JSON.stringify(ed(e,t)),eg(t,n,"/",void 0,r)},f=async(e,t)=>{if(e.statusCode>=300)return m(e,t);let r=await i(e.body,t),n={};return n=ef(r,t),{$metadata:em(e),...n}},h=async(e,t)=>{if(e.statusCode>=300)return m(e,t);let r=await i(e.body,t),n={};return n=eh(r,t),{$metadata:em(e),...n}},m=async(e,t)=>{let r={...e,body:await o(e.body,t)},n=a(e,r.body);switch(n){case"DependencyTimeoutException":case"com.amazonaws.kms#DependencyTimeoutException":throw await A(r,t);case"InvalidArnException":case"com.amazonaws.kms#InvalidArnException":throw await D(r,t);case"KMSInternalException":case"com.amazonaws.kms#KMSInternalException":throw await K(r,t);case"KMSInvalidStateException":case"com.amazonaws.kms#KMSInvalidStateException":throw await q(r,t);case"NotFoundException":case"com.amazonaws.kms#NotFoundException":throw await X(r,t);case"CloudHsmClusterInvalidConfigurationException":case"com.amazonaws.kms#CloudHsmClusterInvalidConfigurationException":throw await x(r,t);case"CloudHsmClusterNotActiveException":case"com.amazonaws.kms#CloudHsmClusterNotActiveException":throw await b(r,t);case"CustomKeyStoreInvalidStateException":case"com.amazonaws.kms#CustomKeyStoreInvalidStateException":throw await P(r,t);case"CustomKeyStoreNotFoundException":case"com.amazonaws.kms#CustomKeyStoreNotFoundException":throw await $(r,t);case"AlreadyExistsException":case"com.amazonaws.kms#AlreadyExistsException":throw await y(r,t);case"InvalidAliasNameException":case"com.amazonaws.kms#InvalidAliasNameException":throw await N(r,t);case"LimitExceededException":case"com.amazonaws.kms#LimitExceededException":throw await G(r,t);case"CloudHsmClusterInUseException":case"com.amazonaws.kms#CloudHsmClusterInUseException":throw await g(r,t);case"CloudHsmClusterNotFoundException":case"com.amazonaws.kms#CloudHsmClusterNotFoundException":throw await w(r,t);case"CustomKeyStoreNameInUseException":case"com.amazonaws.kms#CustomKeyStoreNameInUseException":throw await O(r,t);case"IncorrectTrustAnchorException":case"com.amazonaws.kms#IncorrectTrustAnchorException":throw await R(r,t);case"XksProxyIncorrectAuthenticationCredentialException":case"com.amazonaws.kms#XksProxyIncorrectAuthenticationCredentialException":throw await et(r,t);case"XksProxyInvalidConfigurationException":case"com.amazonaws.kms#XksProxyInvalidConfigurationException":throw await er(r,t);case"XksProxyInvalidResponseException":case"com.amazonaws.kms#XksProxyInvalidResponseException":throw await en(r,t);case"XksProxyUriEndpointInUseException":case"com.amazonaws.kms#XksProxyUriEndpointInUseException":throw await es(r,t);case"XksProxyUriInUseException":case"com.amazonaws.kms#XksProxyUriInUseException":throw await ei(r,t);case"XksProxyUriUnreachableException":case"com.amazonaws.kms#XksProxyUriUnreachableException":throw await eo(r,t);case"XksProxyVpcEndpointServiceInUseException":case"com.amazonaws.kms#XksProxyVpcEndpointServiceInUseException":throw await ea(r,t);case"XksProxyVpcEndpointServiceInvalidConfigurationException":case"com.amazonaws.kms#XksProxyVpcEndpointServiceInvalidConfigurationException":throw await ec(r,t);case"XksProxyVpcEndpointServiceNotFoundException":case"com.amazonaws.kms#XksProxyVpcEndpointServiceNotFoundException":throw await eu(r,t);case"DisabledException":case"com.amazonaws.kms#DisabledException":throw await M(r,t);case"DryRunOperationException":case"com.amazonaws.kms#DryRunOperationException":throw await I(r,t);case"InvalidGrantTokenException":case"com.amazonaws.kms#InvalidGrantTokenException":throw await F(r,t);case"MalformedPolicyDocumentException":case"com.amazonaws.kms#MalformedPolicyDocumentException":throw await W(r,t);case"TagException":case"com.amazonaws.kms#TagException":throw await Y(r,t);case"UnsupportedOperationException":case"com.amazonaws.kms#UnsupportedOperationException":throw await J(r,t);case"XksKeyAlreadyInUseException":case"com.amazonaws.kms#XksKeyAlreadyInUseException":throw await Z(r,t);case"XksKeyInvalidConfigurationException":case"com.amazonaws.kms#XksKeyInvalidConfigurationException":throw await Q(r,t);case"XksKeyNotFoundException":case"com.amazonaws.kms#XksKeyNotFoundException":throw await ee(r,t);case"IncorrectKeyException":case"com.amazonaws.kms#IncorrectKeyException":throw await k(r,t);case"InvalidCiphertextException":case"com.amazonaws.kms#InvalidCiphertextException":throw await U(r,t);case"InvalidKeyUsageException":case"com.amazonaws.kms#InvalidKeyUsageException":throw await L(r,t);case"KeyUnavailableException":case"com.amazonaws.kms#KeyUnavailableException":throw await B(r,t);case"CustomKeyStoreHasCMKsException":case"com.amazonaws.kms#CustomKeyStoreHasCMKsException":throw await S(r,t);case"InvalidMarkerException":case"com.amazonaws.kms#InvalidMarkerException":throw await z(r,t);case"ExpiredImportTokenException":case"com.amazonaws.kms#ExpiredImportTokenException":throw await C(r,t);case"IncorrectKeyMaterialException":case"com.amazonaws.kms#IncorrectKeyMaterialException":throw await T(r,t);case"InvalidImportTokenException":case"com.amazonaws.kms#InvalidImportTokenException":throw await _(r,t);case"InvalidGrantIdException":case"com.amazonaws.kms#InvalidGrantIdException":throw await j(r,t);case"ConflictException":case"com.amazonaws.kms#ConflictException":throw await E(r,t);case"CloudHsmClusterNotRelatedException":case"com.amazonaws.kms#CloudHsmClusterNotRelatedException":throw await v(r,t);case"KMSInvalidSignatureException":case"com.amazonaws.kms#KMSInvalidSignatureException":throw await H(r,t);case"KMSInvalidMacException":case"com.amazonaws.kms#KMSInvalidMacException":throw await V(r,t);default:return ey({output:e,parsedBody:r.body,errorCode:n})}},y=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.iK({$metadata:em(e),...s});return(0,n.Mw)(i,r)},g=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.T2({$metadata:em(e),...s});return(0,n.Mw)(i,r)},x=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.wl({$metadata:em(e),...s});return(0,n.Mw)(i,r)},b=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.wh({$metadata:em(e),...s});return(0,n.Mw)(i,r)},w=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.iu({$metadata:em(e),...s});return(0,n.Mw)(i,r)},v=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.Fo({$metadata:em(e),...s});return(0,n.Mw)(i,r)},E=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.cb({$metadata:em(e),...s});return(0,n.Mw)(i,r)},S=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.X1({$metadata:em(e),...s});return(0,n.Mw)(i,r)},P=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.Bx({$metadata:em(e),...s});return(0,n.Mw)(i,r)},O=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.MW({$metadata:em(e),...s});return(0,n.Mw)(i,r)},$=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.Qy({$metadata:em(e),...s});return(0,n.Mw)(i,r)},A=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.eo({$metadata:em(e),...s});return(0,n.Mw)(i,r)},M=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.S5({$metadata:em(e),...s});return(0,n.Mw)(i,r)},I=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.Hv({$metadata:em(e),...s});return(0,n.Mw)(i,r)},C=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.Bo({$metadata:em(e),...s});return(0,n.Mw)(i,r)},k=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.wj({$metadata:em(e),...s});return(0,n.Mw)(i,r)},T=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.ju({$metadata:em(e),...s});return(0,n.Mw)(i,r)},R=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.oZ({$metadata:em(e),...s});return(0,n.Mw)(i,r)},N=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.C2({$metadata:em(e),...s});return(0,n.Mw)(i,r)},D=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.w$({$metadata:em(e),...s});return(0,n.Mw)(i,r)},U=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.x2({$metadata:em(e),...s});return(0,n.Mw)(i,r)},j=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.IF({$metadata:em(e),...s});return(0,n.Mw)(i,r)},F=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.s7({$metadata:em(e),...s});return(0,n.Mw)(i,r)},_=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.r8({$metadata:em(e),...s});return(0,n.Mw)(i,r)},L=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.bg({$metadata:em(e),...s});return(0,n.Mw)(i,r)},z=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.Tk({$metadata:em(e),...s});return(0,n.Mw)(i,r)},B=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.dt({$metadata:em(e),...s});return(0,n.Mw)(i,r)},K=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.Yz({$metadata:em(e),...s});return(0,n.Mw)(i,r)},V=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.H5({$metadata:em(e),...s});return(0,n.Mw)(i,r)},H=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.eJ({$metadata:em(e),...s});return(0,n.Mw)(i,r)},q=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.$U({$metadata:em(e),...s});return(0,n.Mw)(i,r)},G=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.UC({$metadata:em(e),...s});return(0,n.Mw)(i,r)},W=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l._Z({$metadata:em(e),...s});return(0,n.Mw)(i,r)},X=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.X6({$metadata:em(e),...s});return(0,n.Mw)(i,r)},Y=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.oP({$metadata:em(e),...s});return(0,n.Mw)(i,r)},J=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.e$({$metadata:em(e),...s});return(0,n.Mw)(i,r)},Z=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.B$({$metadata:em(e),...s});return(0,n.Mw)(i,r)},Q=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.AD({$metadata:em(e),...s});return(0,n.Mw)(i,r)},ee=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.ot({$metadata:em(e),...s});return(0,n.Mw)(i,r)},et=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.ye({$metadata:em(e),...s});return(0,n.Mw)(i,r)},er=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.Jm({$metadata:em(e),...s});return(0,n.Mw)(i,r)},en=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.m9({$metadata:em(e),...s});return(0,n.Mw)(i,r)},es=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.BK({$metadata:em(e),...s});return(0,n.Mw)(i,r)},ei=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.ML({$metadata:em(e),...s});return(0,n.Mw)(i,r)},eo=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.ET({$metadata:em(e),...s});return(0,n.Mw)(i,r)},ea=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.rn({$metadata:em(e),...s});return(0,n.Mw)(i,r)},ec=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.su({$metadata:em(e),...s});return(0,n.Mw)(i,r)},eu=async(e,t)=>{let r=e.body,s=(0,n.Ss)(r),i=new l.G1({$metadata:em(e),...s});return(0,n.Mw)(i,r)},el=(e,t)=>(0,n.s)(e,{CiphertextBlob:t.base64Encoder,DryRun:[],EncryptionAlgorithm:[],EncryptionContext:n.Ss,GrantTokens:n.Ss,KeyId:[],Recipient:e=>ep(e,t)}),ed=(e,t)=>(0,n.s)(e,{DryRun:[],EncryptionAlgorithm:[],EncryptionContext:n.Ss,GrantTokens:n.Ss,KeyId:[],Plaintext:t.base64Encoder}),ep=(e,t)=>(0,n.s)(e,{AttestationDocument:t.base64Encoder,KeyEncryptionAlgorithm:[]}),ef=(e,t)=>(0,n.s)(e,{CiphertextForRecipient:t.base64Decoder,EncryptionAlgorithm:n.lK,KeyId:n.lK,Plaintext:t.base64Decoder}),eh=(e,t)=>(0,n.s)(e,{CiphertextBlob:t.base64Decoder,EncryptionAlgorithm:n.lK,KeyId:n.lK}),em=e=>({httpStatusCode:e.statusCode,requestId:e.headers["x-amzn-requestid"]??e.headers["x-amzn-request-id"]??e.headers["x-amz-request-id"],extendedRequestId:e.headers["x-amz-id-2"],cfId:e.headers["x-amz-cf-id"]}),ey=(0,n.jr)(u.G),eg=async(e,t,r,n,s)=>{let{hostname:i,protocol:o="https",port:a,path:u}=await e.endpoint(),l={protocol:o,hostname:i,port:a,method:"POST",path:u.endsWith("/")?u.slice(0,-1)+r:u+r,headers:t};return void 0!==n&&(l.hostname=n),void 0!==s&&(l.body=s),new c.Kd(l)};function ex(e){return{"content-type":"application/x-amz-json-1.1","x-amz-target":`TrentService.${e}`}}},787:(e,t,r)=>{"use strict";r.d(t,{L:()=>eS});var n=r(1095),s=r(5937),i=r(8377),o=r(1136),a=r(1651),c=r(4383),u=r(649),l=r(8176),d=r(6320),p=r(5268),f=r(7258),h=r(9848);let m=async(e,t,r)=>({operation:(0,h.u)(t).operation,region:await (0,h.t)(e.region)()||(()=>{throw Error("expected `region` to be configured for `aws.auth#sigv4`")})()}),y=e=>{let t=[];return e.operation,t.push({schemeId:"aws.auth#sigv4",signingProperties:{name:"route53",region:e.region},propertiesExtractor:(e,t)=>({signingProperties:{config:e,context:t}})}),t},g=e=>({...(0,f.h)(e)});var x=r(9247);let b={rE:"3.741.0"};var w=r(5071),v=r(9391),E=r(9124),S=r(2423),P=r(6850),O=r(2073),$=r(8768),A=r(4262),M=r(9764),I=r(2637),C=r(3411),k=r(6137),T=r(8636);let R="required",N="argv",D="authSchemes",U="signingName",j="signingRegion",F="isSet",_="booleanEquals",L="error",z="endpoint",B="tree",K="PartitionResult",V="stringEquals",H="sigv4",q="route53",G={[R]:!1,type:"String"},W={[R]:!0,default:!1,type:"Boolean"},X={ref:"Endpoint"},Y={fn:_,[N]:[{ref:"UseFIPS"},!0]},J={fn:_,[N]:[{ref:"UseDualStack"},!0]},Z={},Q={fn:V,[N]:[{fn:"getAttr",[N]:[{ref:K},"name"]},"aws"]},ee={fn:"getAttr",[N]:[{ref:K},"name"]},et={fn:_,[N]:[{ref:"UseFIPS"},!1]},er={fn:_,[N]:[{ref:"UseDualStack"},!1]},en={[D]:[{name:H,[U]:q,[j]:"us-east-1"}]},es={fn:V,[N]:[ee,"aws-us-gov"]},ei={url:"https://route53.us-gov.amazonaws.com",properties:{[D]:[{name:H,[U]:q,[j]:"us-gov-west-1"}]},headers:{}},eo={fn:"getAttr",[N]:[{ref:K},"supportsFIPS"]},ea={fn:_,[N]:[!0,{fn:"getAttr",[N]:[{ref:K},"supportsDualStack"]}]},ec=[Y],eu=[J],el=[{ref:"Region"}],ed={version:"1.0",parameters:{Region:G,UseDualStack:W,UseFIPS:W,Endpoint:G},rules:[{conditions:[{fn:F,[N]:[X]}],rules:[{conditions:ec,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:L},{conditions:eu,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:L},{endpoint:{url:X,properties:Z,headers:Z},type:z}],type:B},{conditions:[{fn:F,[N]:el}],rules:[{conditions:[{fn:"aws.partition",[N]:el,assign:K}],rules:[{conditions:[Q,et,er],endpoint:{url:"https://route53.amazonaws.com",properties:en,headers:Z},type:z},{conditions:[Q,Y,er],endpoint:{url:"https://route53-fips.amazonaws.com",properties:en,headers:Z},type:z},{conditions:[{fn:V,[N]:[ee,"aws-cn"]},et,er],endpoint:{url:"https://route53.amazonaws.com.cn",properties:{[D]:[{name:H,[U]:q,[j]:"cn-northwest-1"}]},headers:Z},type:z},{conditions:[es,et,er],endpoint:ei,type:z},{conditions:[es,Y,er],endpoint:ei,type:z},{conditions:[{fn:V,[N]:[ee,"aws-iso"]},et,er],endpoint:{url:"https://route53.c2s.ic.gov",properties:{[D]:[{name:H,[U]:q,[j]:"us-iso-east-1"}]},headers:Z},type:z},{conditions:[{fn:V,[N]:[ee,"aws-iso-b"]},et,er],endpoint:{url:"https://route53.sc2s.sgov.gov",properties:{[D]:[{name:H,[U]:q,[j]:"us-isob-east-1"}]},headers:Z},type:z},{conditions:[{fn:V,[N]:[ee,"aws-iso-e"]},et,er],endpoint:{url:"https://route53.cloud.adc-e.uk",properties:{[D]:[{name:H,[U]:q,[j]:"eu-isoe-west-1"}]},headers:Z},type:z},{conditions:[{fn:V,[N]:[ee,"aws-iso-f"]},et,er],endpoint:{url:"https://route53.csp.hci.ic.gov",properties:{[D]:[{name:H,[U]:q,[j]:"us-isof-south-1"}]},headers:Z},type:z},{conditions:[Y,J],rules:[{conditions:[{fn:_,[N]:[!0,eo]},ea],rules:[{endpoint:{url:"https://route53-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:Z,headers:Z},type:z}],type:B},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:L}],type:B},{conditions:ec,rules:[{conditions:[{fn:_,[N]:[eo,!0]}],rules:[{endpoint:{url:"https://route53-fips.{Region}.{PartitionResult#dnsSuffix}",properties:Z,headers:Z},type:z}],type:B},{error:"FIPS is enabled but this partition does not support FIPS",type:L}],type:B},{conditions:eu,rules:[{conditions:[ea],rules:[{endpoint:{url:"https://route53.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:Z,headers:Z},type:z}],type:B},{error:"DualStack is enabled but this partition does not support DualStack",type:L}],type:B},{endpoint:{url:"https://route53.{Region}.{PartitionResult#dnsSuffix}",properties:Z,headers:Z},type:z}],type:B}],type:B},{error:"Invalid Configuration: Missing Region",type:L}]},ep=new T.kS({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),ef=(e,t={})=>ep.get(e,()=>(0,T.sO)(ed,{endpointParams:e,logger:t.logger}));T.mw.aws=k.UF;let eh=e=>({apiVersion:"2013-04-01",base64Decoder:e?.base64Decoder??M.E,base64Encoder:e?.base64Encoder??M.n,disableHostPrefix:e?.disableHostPrefix??!1,endpointProvider:e?.endpointProvider??ef,extensions:e?.extensions??[],httpAuthSchemeProvider:e?.httpAuthSchemeProvider??y,httpAuthSchemes:e?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:e=>e.getIdentityProvider("aws.auth#sigv4"),signer:new $.f2}],logger:e?.logger??new p.N4,serviceId:e?.serviceId??"Route 53",urlParser:e?.urlParser??A.D,utf8Decoder:e?.utf8Decoder??I.a,utf8Encoder:e?.utf8Encoder??C.P});var em=r(1996);let ey=e=>{let t=(0,em.I)(e),r=()=>t().then(p.lT),n=eh(e);return{...n,...e,runtime:"browser",defaultsMode:t,bodyLengthChecker:e?.bodyLengthChecker??P.n,credentialDefaultProvider:e?.credentialDefaultProvider??(e=>()=>Promise.reject(Error("Credential is missing"))),defaultUserAgentProvider:e?.defaultUserAgentProvider??(0,v.p)({serviceId:n.serviceId,clientVersion:b.rE}),maxAttempts:e?.maxAttempts??O.Gz,region:e?.region??(0,S.B)("Region is missing"),requestHandler:E.NC.create(e?.requestHandler??r),retryMode:e?.retryMode??(async()=>(await r()).retryMode||O.L0),sha256:e?.sha256??w.I,streamCollector:e?.streamCollector??E.kv,useDualstackEndpoint:e?.useDualstackEndpoint??(()=>Promise.resolve(a.VW)),useFipsEndpoint:e?.useFipsEndpoint??(()=>Promise.resolve(a.Hj))}};var eg=r(3614),ex=r(7609);let eb=e=>{let t=e.httpAuthSchemes,r=e.httpAuthSchemeProvider,n=e.credentials;return{setHttpAuthScheme(e){let r=t.findIndex(t=>t.schemeId===e.schemeId);-1===r?t.push(e):t.splice(r,1,e)},httpAuthSchemes:()=>t,setHttpAuthSchemeProvider(e){r=e},httpAuthSchemeProvider:()=>r,setCredentials(e){n=e},credentials:()=>n}},ew=e=>({httpAuthSchemes:e.httpAuthSchemes(),httpAuthSchemeProvider:e.httpAuthSchemeProvider(),credentials:e.credentials()}),ev=e=>e,eE=(e,t)=>{let r={...ev((0,eg.Rq)(e)),...ev((0,p.xA)(e)),...ev((0,ex.eS)(e)),...ev(eb(e))};return t.forEach(e=>e.configure(r)),{...e,...(0,eg.$3)(r),...(0,p.uv)(r),...(0,ex.jt)(r),...ew(r)}};class eS extends p.Kj{config;constructor(...[e]){let t=ey(e||{}),r=(0,x.v)(t),p=(0,o.Dc)(r),f=(0,d.$z)(p),h=(0,a.TD)(f),y=(0,n.OV)(h),b=eE(g((0,l.Co)(y)),e?.extensions||[]);super(b),this.config=b,this.middlewareStack.use((0,o.sM)(this.config)),this.middlewareStack.use((0,d.ey)(this.config)),this.middlewareStack.use((0,u.vK)(this.config)),this.middlewareStack.use((0,n.TC)(this.config)),this.middlewareStack.use((0,s.Y7)(this.config)),this.middlewareStack.use((0,i.n4)(this.config)),this.middlewareStack.use((0,c.wB)(this.config,{httpAuthSchemeParametersProvider:m,identityProviderConfigProvider:async e=>new c.h$({"aws.auth#sigv4":e.credentials})})),this.middlewareStack.use((0,c.lW)(this.config))}destroy(){super.destroy()}}},4670:(e,t,r)=>{"use strict";r.d(t,{y:()=>u});var n=r(4196),s=r(8176),i=r(228),o=r(5268),a=r(9247),c=r(907);class u extends o.uB.classBuilder().ep(a.S).m(function(e,t,r,o){return[(0,i.TM)(r,this.serialize,this.deserialize),(0,s.rD)(r,e.getEndpointParameterInstructions()),(0,n.e)(r),(0,n.Ek)(r)]}).s("AWSDnsV20130401","ChangeResourceRecordSets",{}).n("Route53Client","ChangeResourceRecordSetsCommand").f(void 0,void 0).ser(c.$Y).de(c.En).build(){}},5444:(e,t,r)=>{"use strict";r.d(t,{c:()=>u});var n=r(4196),s=r(8176),i=r(228),o=r(5268),a=r(9247),c=r(907);class u extends o.uB.classBuilder().ep(a.S).m(function(e,t,r,o){return[(0,i.TM)(r,this.serialize,this.deserialize),(0,s.rD)(r,e.getEndpointParameterInstructions()),(0,n.Ek)(r)]}).s("AWSDnsV20130401","ListHostedZones",{}).n("Route53Client","ListHostedZonesCommand").f(void 0,void 0).ser(c.lw).de(c.ub).build(){}},9247:(e,t,r)=>{"use strict";r.d(t,{S:()=>s,v:()=>n});let n=e=>({...e,useDualstackEndpoint:e.useDualstackEndpoint??!1,useFipsEndpoint:e.useFipsEndpoint??!1,defaultSigningName:"route53"}),s={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}}},5378:(e,t,r)=>{"use strict";r.d(t,{M:()=>s});var n=r(5268);class s extends n.TJ{constructor(e){super(e),Object.setPrototypeOf(this,s.prototype)}}},3318:(e,t,r)=>{"use strict";r.d(t,{$4:()=>m,$P:()=>c,$v:()=>_,BV:()=>o,Cv:()=>W,EF:()=>y,ES:()=>d,Eq:()=>S,H1:()=>G,JR:()=>g,LF:()=>O,Ms:()=>u,Nd:()=>ea,OE:()=>b,Od:()=>N,Pv:()=>l,Qp:()=>M,R9:()=>eu,Rc:()=>ep,S8:()=>a,TW:()=>U,UD:()=>p,WC:()=>eh,X7:()=>el,Y7:()=>h,YJ:()=>ec,Zm:()=>ed,aN:()=>en,an:()=>ee,b$:()=>er,bK:()=>j,cC:()=>X,ci:()=>ex,cn:()=>k,co:()=>eo,d3:()=>eg,df:()=>ei,ec:()=>D,f:()=>I,f0:()=>T,ff:()=>C,fz:()=>$,gD:()=>f,gK:()=>L,hO:()=>Y,il:()=>x,io:()=>V,jS:()=>z,jq:()=>P,ky:()=>F,ls:()=>v,n2:()=>A,o9:()=>i,oJ:()=>w,ol:()=>Z,pO:()=>em,qU:()=>H,sR:()=>ef,sS:()=>R,sq:()=>B,tg:()=>ey,ts:()=>Q,vk:()=>q,vq:()=>es,wN:()=>K,wo:()=>et,wt:()=>s,x5:()=>E,yw:()=>J});var n=r(5378);class s extends n.M{name="ConcurrentModification";$fault="client";constructor(e){super({name:"ConcurrentModification",$fault:"client",...e}),Object.setPrototypeOf(this,s.prototype)}}class i extends n.M{name="InvalidInput";$fault="client";constructor(e){super({name:"InvalidInput",$fault:"client",...e}),Object.setPrototypeOf(this,i.prototype)}}class o extends n.M{name="InvalidKeySigningKeyStatus";$fault="client";constructor(e){super({name:"InvalidKeySigningKeyStatus",$fault:"client",...e}),Object.setPrototypeOf(this,o.prototype)}}class a extends n.M{name="InvalidKMSArn";$fault="client";constructor(e){super({name:"InvalidKMSArn",$fault:"client",...e}),Object.setPrototypeOf(this,a.prototype)}}class c extends n.M{name="InvalidSigningStatus";$fault="client";constructor(e){super({name:"InvalidSigningStatus",$fault:"client",...e}),Object.setPrototypeOf(this,c.prototype)}}class u extends n.M{name="NoSuchKeySigningKey";$fault="client";constructor(e){super({name:"NoSuchKeySigningKey",$fault:"client",...e}),Object.setPrototypeOf(this,u.prototype)}}class l extends n.M{name="ConflictingDomainExists";$fault="client";constructor(e){super({name:"ConflictingDomainExists",$fault:"client",...e}),Object.setPrototypeOf(this,l.prototype)}}class d extends n.M{name="InvalidVPCId";$fault="client";constructor(e){super({name:"InvalidVPCId",$fault:"client",...e}),Object.setPrototypeOf(this,d.prototype)}}class p extends n.M{name="LimitsExceeded";$fault="client";constructor(e){super({name:"LimitsExceeded",$fault:"client",...e}),Object.setPrototypeOf(this,p.prototype)}}class f extends n.M{name="NoSuchHostedZone";$fault="client";constructor(e){super({name:"NoSuchHostedZone",$fault:"client",...e}),Object.setPrototypeOf(this,f.prototype)}}class h extends n.M{name="NotAuthorizedException";$fault="client";constructor(e){super({name:"NotAuthorizedException",$fault:"client",...e}),Object.setPrototypeOf(this,h.prototype)}}class m extends n.M{name="PriorRequestNotComplete";$fault="client";constructor(e){super({name:"PriorRequestNotComplete",$fault:"client",...e}),Object.setPrototypeOf(this,m.prototype)}}class y extends n.M{name="PublicZoneVPCAssociation";$fault="client";constructor(e){super({name:"PublicZoneVPCAssociation",$fault:"client",...e}),Object.setPrototypeOf(this,y.prototype)}}class g extends n.M{name="CidrBlockInUseException";$fault="client";Message;constructor(e){super({name:"CidrBlockInUseException",$fault:"client",...e}),Object.setPrototypeOf(this,g.prototype),this.Message=e.Message}}class x extends n.M{name="CidrCollectionVersionMismatchException";$fault="client";Message;constructor(e){super({name:"CidrCollectionVersionMismatchException",$fault:"client",...e}),Object.setPrototypeOf(this,x.prototype),this.Message=e.Message}}class b extends n.M{name="NoSuchCidrCollectionException";$fault="client";Message;constructor(e){super({name:"NoSuchCidrCollectionException",$fault:"client",...e}),Object.setPrototypeOf(this,b.prototype),this.Message=e.Message}}class w extends n.M{name="InvalidChangeBatch";$fault="client";messages;constructor(e){super({name:"InvalidChangeBatch",$fault:"client",...e}),Object.setPrototypeOf(this,w.prototype),this.messages=e.messages}}class v extends n.M{name="NoSuchHealthCheck";$fault="client";constructor(e){super({name:"NoSuchHealthCheck",$fault:"client",...e}),Object.setPrototypeOf(this,v.prototype)}}class E extends n.M{name="ThrottlingException";$fault="client";constructor(e){super({name:"ThrottlingException",$fault:"client",...e}),Object.setPrototypeOf(this,E.prototype)}}class S extends n.M{name="CidrCollectionAlreadyExistsException";$fault="client";Message;constructor(e){super({name:"CidrCollectionAlreadyExistsException",$fault:"client",...e}),Object.setPrototypeOf(this,S.prototype),this.Message=e.Message}}class P extends n.M{name="HealthCheckAlreadyExists";$fault="client";constructor(e){super({name:"HealthCheckAlreadyExists",$fault:"client",...e}),Object.setPrototypeOf(this,P.prototype)}}class O extends n.M{name="TooManyHealthChecks";$fault="client";constructor(e){super({name:"TooManyHealthChecks",$fault:"client",...e}),Object.setPrototypeOf(this,O.prototype)}}class $ extends n.M{name="DelegationSetNotAvailable";$fault="client";constructor(e){super({name:"DelegationSetNotAvailable",$fault:"client",...e}),Object.setPrototypeOf(this,$.prototype)}}class A extends n.M{name="DelegationSetNotReusable";$fault="client";constructor(e){super({name:"DelegationSetNotReusable",$fault:"client",...e}),Object.setPrototypeOf(this,A.prototype)}}class M extends n.M{name="HostedZoneAlreadyExists";$fault="client";constructor(e){super({name:"HostedZoneAlreadyExists",$fault:"client",...e}),Object.setPrototypeOf(this,M.prototype)}}class I extends n.M{name="InvalidDomainName";$fault="client";constructor(e){super({name:"InvalidDomainName",$fault:"client",...e}),Object.setPrototypeOf(this,I.prototype)}}class C extends n.M{name="NoSuchDelegationSet";$fault="client";constructor(e){super({name:"NoSuchDelegationSet",$fault:"client",...e}),Object.setPrototypeOf(this,C.prototype)}}class k extends n.M{name="TooManyHostedZones";$fault="client";constructor(e){super({name:"TooManyHostedZones",$fault:"client",...e}),Object.setPrototypeOf(this,k.prototype)}}class T extends n.M{name="InvalidArgument";$fault="client";constructor(e){super({name:"InvalidArgument",$fault:"client",...e}),Object.setPrototypeOf(this,T.prototype)}}class R extends n.M{name="InvalidKeySigningKeyName";$fault="client";constructor(e){super({name:"InvalidKeySigningKeyName",$fault:"client",...e}),Object.setPrototypeOf(this,R.prototype)}}class N extends n.M{name="KeySigningKeyAlreadyExists";$fault="client";constructor(e){super({name:"KeySigningKeyAlreadyExists",$fault:"client",...e}),Object.setPrototypeOf(this,N.prototype)}}class D extends n.M{name="TooManyKeySigningKeys";$fault="client";constructor(e){super({name:"TooManyKeySigningKeys",$fault:"client",...e}),Object.setPrototypeOf(this,D.prototype)}}class U extends n.M{name="InsufficientCloudWatchLogsResourcePolicy";$fault="client";constructor(e){super({name:"InsufficientCloudWatchLogsResourcePolicy",$fault:"client",...e}),Object.setPrototypeOf(this,U.prototype)}}class j extends n.M{name="NoSuchCloudWatchLogsLogGroup";$fault="client";constructor(e){super({name:"NoSuchCloudWatchLogsLogGroup",$fault:"client",...e}),Object.setPrototypeOf(this,j.prototype)}}class F extends n.M{name="QueryLoggingConfigAlreadyExists";$fault="client";constructor(e){super({name:"QueryLoggingConfigAlreadyExists",$fault:"client",...e}),Object.setPrototypeOf(this,F.prototype)}}class _ extends n.M{name="DelegationSetAlreadyCreated";$fault="client";constructor(e){super({name:"DelegationSetAlreadyCreated",$fault:"client",...e}),Object.setPrototypeOf(this,_.prototype)}}class L extends n.M{name="DelegationSetAlreadyReusable";$fault="client";constructor(e){super({name:"DelegationSetAlreadyReusable",$fault:"client",...e}),Object.setPrototypeOf(this,L.prototype)}}class z extends n.M{name="HostedZoneNotFound";$fault="client";constructor(e){super({name:"HostedZoneNotFound",$fault:"client",...e}),Object.setPrototypeOf(this,z.prototype)}}class B extends n.M{name="InvalidTrafficPolicyDocument";$fault="client";constructor(e){super({name:"InvalidTrafficPolicyDocument",$fault:"client",...e}),Object.setPrototypeOf(this,B.prototype)}}class K extends n.M{name="TooManyTrafficPolicies";$fault="client";constructor(e){super({name:"TooManyTrafficPolicies",$fault:"client",...e}),Object.setPrototypeOf(this,K.prototype)}}class V extends n.M{name="TrafficPolicyAlreadyExists";$fault="client";constructor(e){super({name:"TrafficPolicyAlreadyExists",$fault:"client",...e}),Object.setPrototypeOf(this,V.prototype)}}class H extends n.M{name="NoSuchTrafficPolicy";$fault="client";constructor(e){super({name:"NoSuchTrafficPolicy",$fault:"client",...e}),Object.setPrototypeOf(this,H.prototype)}}class q extends n.M{name="TooManyTrafficPolicyInstances";$fault="client";constructor(e){super({name:"TooManyTrafficPolicyInstances",$fault:"client",...e}),Object.setPrototypeOf(this,q.prototype)}}class G extends n.M{name="TrafficPolicyInstanceAlreadyExists";$fault="client";constructor(e){super({name:"TrafficPolicyInstanceAlreadyExists",$fault:"client",...e}),Object.setPrototypeOf(this,G.prototype)}}class W extends n.M{name="TooManyTrafficPolicyVersionsForCurrentPolicy";$fault="client";constructor(e){super({name:"TooManyTrafficPolicyVersionsForCurrentPolicy",$fault:"client",...e}),Object.setPrototypeOf(this,W.prototype)}}class X extends n.M{name="TooManyVPCAssociationAuthorizations";$fault="client";constructor(e){super({name:"TooManyVPCAssociationAuthorizations",$fault:"client",...e}),Object.setPrototypeOf(this,X.prototype)}}class Y extends n.M{name="KeySigningKeyInParentDSRecord";$fault="client";constructor(e){super({name:"KeySigningKeyInParentDSRecord",$fault:"client",...e}),Object.setPrototypeOf(this,Y.prototype)}}class J extends n.M{name="KeySigningKeyInUse";$fault="client";constructor(e){super({name:"KeySigningKeyInUse",$fault:"client",...e}),Object.setPrototypeOf(this,J.prototype)}}class Z extends n.M{name="CidrCollectionInUseException";$fault="client";Message;constructor(e){super({name:"CidrCollectionInUseException",$fault:"client",...e}),Object.setPrototypeOf(this,Z.prototype),this.Message=e.Message}}class Q extends n.M{name="HealthCheckInUse";$fault="client";constructor(e){super({name:"HealthCheckInUse",$fault:"client",...e}),Object.setPrototypeOf(this,Q.prototype)}}class ee extends n.M{name="HostedZoneNotEmpty";$fault="client";constructor(e){super({name:"HostedZoneNotEmpty",$fault:"client",...e}),Object.setPrototypeOf(this,ee.prototype)}}class et extends n.M{name="NoSuchQueryLoggingConfig";$fault="client";constructor(e){super({name:"NoSuchQueryLoggingConfig",$fault:"client",...e}),Object.setPrototypeOf(this,et.prototype)}}class er extends n.M{name="DelegationSetInUse";$fault="client";constructor(e){super({name:"DelegationSetInUse",$fault:"client",...e}),Object.setPrototypeOf(this,er.prototype)}}class en extends n.M{name="TrafficPolicyInUse";$fault="client";constructor(e){super({name:"TrafficPolicyInUse",$fault:"client",...e}),Object.setPrototypeOf(this,en.prototype)}}class es extends n.M{name="NoSuchTrafficPolicyInstance";$fault="client";constructor(e){super({name:"NoSuchTrafficPolicyInstance",$fault:"client",...e}),Object.setPrototypeOf(this,es.prototype)}}class ei extends n.M{name="VPCAssociationAuthorizationNotFound";$fault="client";constructor(e){super({name:"VPCAssociationAuthorizationNotFound",$fault:"client",...e}),Object.setPrototypeOf(this,ei.prototype)}}class eo extends n.M{name="DNSSECNotFound";$fault="client";constructor(e){super({name:"DNSSECNotFound",$fault:"client",...e}),Object.setPrototypeOf(this,eo.prototype)}}class ea extends n.M{name="LastVPCAssociation";$fault="client";constructor(e){super({name:"LastVPCAssociation",$fault:"client",...e}),Object.setPrototypeOf(this,ea.prototype)}}class ec extends n.M{name="VPCAssociationNotFound";$fault="client";constructor(e){super({name:"VPCAssociationNotFound",$fault:"client",...e}),Object.setPrototypeOf(this,ec.prototype)}}class eu extends n.M{name="HostedZonePartiallyDelegated";$fault="client";constructor(e){super({name:"HostedZonePartiallyDelegated",$fault:"client",...e}),Object.setPrototypeOf(this,eu.prototype)}}class el extends n.M{name="KeySigningKeyWithActiveStatusNotFound";$fault="client";constructor(e){super({name:"KeySigningKeyWithActiveStatusNotFound",$fault:"client",...e}),Object.setPrototypeOf(this,el.prototype)}}class ed extends n.M{name="NoSuchChange";$fault="client";constructor(e){super({name:"NoSuchChange",$fault:"client",...e}),Object.setPrototypeOf(this,ed.prototype)}}class ep extends n.M{name="NoSuchGeoLocation";$fault="client";constructor(e){super({name:"NoSuchGeoLocation",$fault:"client",...e}),Object.setPrototypeOf(this,ep.prototype)}}class ef extends n.M{name="IncompatibleVersion";$fault="client";constructor(e){super({name:"IncompatibleVersion",$fault:"client",...e}),Object.setPrototypeOf(this,ef.prototype)}}class eh extends n.M{name="HostedZoneNotPrivate";$fault="client";constructor(e){super({name:"HostedZoneNotPrivate",$fault:"client",...e}),Object.setPrototypeOf(this,eh.prototype)}}class em extends n.M{name="NoSuchCidrLocationException";$fault="client";Message;constructor(e){super({name:"NoSuchCidrLocationException",$fault:"client",...e}),Object.setPrototypeOf(this,em.prototype),this.Message=e.Message}}class ey extends n.M{name="InvalidPaginationToken";$fault="client";constructor(e){super({name:"InvalidPaginationToken",$fault:"client",...e}),Object.setPrototypeOf(this,ey.prototype)}}class eg extends n.M{name="HealthCheckVersionMismatch";$fault="client";constructor(e){super({name:"HealthCheckVersionMismatch",$fault:"client",...e}),Object.setPrototypeOf(this,eg.prototype)}}class ex extends n.M{name="ConflictingTypes";$fault="client";constructor(e){super({name:"ConflictingTypes",$fault:"client",...e}),Object.setPrototypeOf(this,ex.prototype)}}},5835:(e,t,r)=>{"use strict";r.d(t,{i:()=>eS});var n=r(1095),s=r(5937),i=r(8377),o=r(1136),a=r(1651),c=r(4383),u=r(649),l=r(8176),d=r(6320),p=r(5268),f=r(7258),h=r(9848);let m=async(e,t,r)=>({operation:(0,h.u)(t).operation,region:await (0,h.t)(e.region)()||(()=>{throw Error("expected `region` to be configured for `aws.auth#sigv4`")})()}),y=e=>{let t=[];switch(e.operation){case"AssumeRoleWithSAML":case"AssumeRoleWithWebIdentity":t.push({schemeId:"smithy.api#noAuth"});break;default:t.push({schemeId:"aws.auth#sigv4",signingProperties:{name:"sts",region:e.region},propertiesExtractor:(e,t)=>({signingProperties:{config:e,context:t}})})}return t},g=e=>({...e,stsClientCtor:eS}),x=e=>{let t=g(e);return{...(0,f.h)(t)}};var b=r(7207);let w={rE:"3.741.0"};var v=r(5071),E=r(9391),S=r(9124),P=r(2423),O=r(6850),$=r(2073),A=r(8768),M=r(4262),I=r(9764),C=r(2637),k=r(3411),T=r(6137),R=r(8636);let N="required",D="type",U="argv",j="booleanEquals",F="stringEquals",_="sigv4",L="us-east-1",z="endpoint",B="https://sts.{Region}.{PartitionResult#dnsSuffix}",K="tree",V="error",H="getAttr",q={[N]:!1,[D]:"String"},G={[N]:!0,default:!1,[D]:"Boolean"},W={ref:"Endpoint"},X={fn:"isSet",[U]:[{ref:"Region"}]},Y={ref:"Region"},J={fn:"aws.partition",[U]:[Y],assign:"PartitionResult"},Z={ref:"UseFIPS"},Q={ref:"UseDualStack"},ee={url:"https://sts.amazonaws.com",properties:{authSchemes:[{name:_,signingName:"sts",signingRegion:L}]},headers:{}},et={},er={conditions:[{fn:F,[U]:[Y,"aws-global"]}],[z]:ee,[D]:z},en={fn:j,[U]:[Z,!0]},es={fn:j,[U]:[Q,!0]},ei={fn:H,[U]:[{ref:"PartitionResult"},"supportsFIPS"]},eo={ref:"PartitionResult"},ea={fn:j,[U]:[!0,{fn:H,[U]:[eo,"supportsDualStack"]}]},ec=[{fn:"isSet",[U]:[W]}],eu=[en],el=[es],ed={version:"1.0",parameters:{Region:q,UseDualStack:G,UseFIPS:G,Endpoint:q,UseGlobalEndpoint:G},rules:[{conditions:[{fn:j,[U]:[{ref:"UseGlobalEndpoint"},!0]},{fn:"not",[U]:ec},X,J,{fn:j,[U]:[Z,!1]},{fn:j,[U]:[Q,!1]}],rules:[{conditions:[{fn:F,[U]:[Y,"ap-northeast-1"]}],endpoint:ee,[D]:z},{conditions:[{fn:F,[U]:[Y,"ap-south-1"]}],endpoint:ee,[D]:z},{conditions:[{fn:F,[U]:[Y,"ap-southeast-1"]}],endpoint:ee,[D]:z},{conditions:[{fn:F,[U]:[Y,"ap-southeast-2"]}],endpoint:ee,[D]:z},er,{conditions:[{fn:F,[U]:[Y,"ca-central-1"]}],endpoint:ee,[D]:z},{conditions:[{fn:F,[U]:[Y,"eu-central-1"]}],endpoint:ee,[D]:z},{conditions:[{fn:F,[U]:[Y,"eu-north-1"]}],endpoint:ee,[D]:z},{conditions:[{fn:F,[U]:[Y,"eu-west-1"]}],endpoint:ee,[D]:z},{conditions:[{fn:F,[U]:[Y,"eu-west-2"]}],endpoint:ee,[D]:z},{conditions:[{fn:F,[U]:[Y,"eu-west-3"]}],endpoint:ee,[D]:z},{conditions:[{fn:F,[U]:[Y,"sa-east-1"]}],endpoint:ee,[D]:z},{conditions:[{fn:F,[U]:[Y,L]}],endpoint:ee,[D]:z},{conditions:[{fn:F,[U]:[Y,"us-east-2"]}],endpoint:ee,[D]:z},{conditions:[{fn:F,[U]:[Y,"us-west-1"]}],endpoint:ee,[D]:z},{conditions:[{fn:F,[U]:[Y,"us-west-2"]}],endpoint:ee,[D]:z},{endpoint:{url:B,properties:{authSchemes:[{name:_,signingName:"sts",signingRegion:"{Region}"}]},headers:et},[D]:z}],[D]:K},{conditions:ec,rules:[{conditions:eu,error:"Invalid Configuration: FIPS and custom endpoint are not supported",[D]:V},{conditions:el,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",[D]:V},{endpoint:{url:W,properties:et,headers:et},[D]:z}],[D]:K},{conditions:[X],rules:[{conditions:[J],rules:[{conditions:[en,es],rules:[{conditions:[{fn:j,[U]:[!0,ei]},ea],rules:[{endpoint:{url:"https://sts-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:et,headers:et},[D]:z}],[D]:K},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",[D]:V}],[D]:K},{conditions:eu,rules:[{conditions:[{fn:j,[U]:[ei,!0]}],rules:[{conditions:[{fn:F,[U]:[{fn:H,[U]:[eo,"name"]},"aws-us-gov"]}],endpoint:{url:"https://sts.{Region}.amazonaws.com",properties:et,headers:et},[D]:z},{endpoint:{url:"https://sts-fips.{Region}.{PartitionResult#dnsSuffix}",properties:et,headers:et},[D]:z}],[D]:K},{error:"FIPS is enabled but this partition does not support FIPS",[D]:V}],[D]:K},{conditions:el,rules:[{conditions:[ea],rules:[{endpoint:{url:"https://sts.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:et,headers:et},[D]:z}],[D]:K},{error:"DualStack is enabled but this partition does not support DualStack",[D]:V}],[D]:K},er,{endpoint:{url:B,properties:et,headers:et},[D]:z}],[D]:K}],[D]:K},{error:"Invalid Configuration: Missing Region",[D]:V}]},ep=new R.kS({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS","UseGlobalEndpoint"]}),ef=(e,t={})=>ep.get(e,()=>(0,R.sO)(ed,{endpointParams:e,logger:t.logger}));R.mw.aws=T.UF;let eh=e=>({apiVersion:"2011-06-15",base64Decoder:e?.base64Decoder??I.E,base64Encoder:e?.base64Encoder??I.n,disableHostPrefix:e?.disableHostPrefix??!1,endpointProvider:e?.endpointProvider??ef,extensions:e?.extensions??[],httpAuthSchemeProvider:e?.httpAuthSchemeProvider??y,httpAuthSchemes:e?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:e=>e.getIdentityProvider("aws.auth#sigv4"),signer:new A.f2},{schemeId:"smithy.api#noAuth",identityProvider:e=>e.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new c.mR}],logger:e?.logger??new p.N4,serviceId:e?.serviceId??"STS",urlParser:e?.urlParser??M.D,utf8Decoder:e?.utf8Decoder??C.a,utf8Encoder:e?.utf8Encoder??k.P});var em=r(1996);let ey=e=>{let t=(0,em.I)(e),r=()=>t().then(p.lT),n=eh(e);return{...n,...e,runtime:"browser",defaultsMode:t,bodyLengthChecker:e?.bodyLengthChecker??O.n,credentialDefaultProvider:e?.credentialDefaultProvider??(e=>()=>Promise.reject(Error("Credential is missing"))),defaultUserAgentProvider:e?.defaultUserAgentProvider??(0,E.p)({serviceId:n.serviceId,clientVersion:w.rE}),maxAttempts:e?.maxAttempts??$.Gz,region:e?.region??(0,P.B)("Region is missing"),requestHandler:S.NC.create(e?.requestHandler??r),retryMode:e?.retryMode??(async()=>(await r()).retryMode||$.L0),sha256:e?.sha256??v.I,streamCollector:e?.streamCollector??S.kv,useDualstackEndpoint:e?.useDualstackEndpoint??(()=>Promise.resolve(a.VW)),useFipsEndpoint:e?.useFipsEndpoint??(()=>Promise.resolve(a.Hj))}};var eg=r(3614),ex=r(7609);let eb=e=>{let t=e.httpAuthSchemes,r=e.httpAuthSchemeProvider,n=e.credentials;return{setHttpAuthScheme(e){let r=t.findIndex(t=>t.schemeId===e.schemeId);-1===r?t.push(e):t.splice(r,1,e)},httpAuthSchemes:()=>t,setHttpAuthSchemeProvider(e){r=e},httpAuthSchemeProvider:()=>r,setCredentials(e){n=e},credentials:()=>n}},ew=e=>({httpAuthSchemes:e.httpAuthSchemes(),httpAuthSchemeProvider:e.httpAuthSchemeProvider(),credentials:e.credentials()}),ev=e=>e,eE=(e,t)=>{let r={...ev((0,eg.Rq)(e)),...ev((0,p.xA)(e)),...ev((0,ex.eS)(e)),...ev(eb(e))};return t.forEach(e=>e.configure(r)),{...e,...(0,eg.$3)(r),...(0,p.uv)(r),...(0,ex.jt)(r),...ew(r)}};class eS extends p.Kj{config;constructor(...[e]){let t=ey(e||{}),r=(0,b.v)(t),p=(0,o.Dc)(r),f=(0,d.$z)(p),h=(0,a.TD)(f),y=(0,n.OV)(h),g=eE(x((0,l.Co)(y)),e?.extensions||[]);super(g),this.config=g,this.middlewareStack.use((0,o.sM)(this.config)),this.middlewareStack.use((0,d.ey)(this.config)),this.middlewareStack.use((0,u.vK)(this.config)),this.middlewareStack.use((0,n.TC)(this.config)),this.middlewareStack.use((0,s.Y7)(this.config)),this.middlewareStack.use((0,i.n4)(this.config)),this.middlewareStack.use((0,c.wB)(this.config,{httpAuthSchemeParametersProvider:m,identityProviderConfigProvider:async e=>new c.h$({"aws.auth#sigv4":e.credentials})})),this.middlewareStack.use((0,c.lW)(this.config))}destroy(){super.destroy()}}},7150:(e,t,r)=>{"use strict";r.d(t,{z:()=>Q});var n=r(8176),s=r(228),i=r(5268),o=r(7207),a=r(5469),c=r(7609);class u extends i.TJ{constructor(e){super(e),Object.setPrototypeOf(this,u.prototype)}}class l extends u{name="ExpiredTokenException";$fault="client";constructor(e){super({name:"ExpiredTokenException",$fault:"client",...e}),Object.setPrototypeOf(this,l.prototype)}}class d extends u{name="MalformedPolicyDocumentException";$fault="client";constructor(e){super({name:"MalformedPolicyDocumentException",$fault:"client",...e}),Object.setPrototypeOf(this,d.prototype)}}class p extends u{name="PackedPolicyTooLargeException";$fault="client";constructor(e){super({name:"PackedPolicyTooLargeException",$fault:"client",...e}),Object.setPrototypeOf(this,p.prototype)}}class f extends u{name="RegionDisabledException";$fault="client";constructor(e){super({name:"RegionDisabledException",$fault:"client",...e}),Object.setPrototypeOf(this,f.prototype)}}class h extends u{name="IDPRejectedClaimException";$fault="client";constructor(e){super({name:"IDPRejectedClaimException",$fault:"client",...e}),Object.setPrototypeOf(this,h.prototype)}}class m extends u{name="InvalidIdentityTokenException";$fault="client";constructor(e){super({name:"InvalidIdentityTokenException",$fault:"client",...e}),Object.setPrototypeOf(this,m.prototype)}}class y extends u{name="IDPCommunicationErrorException";$fault="client";constructor(e){super({name:"IDPCommunicationErrorException",$fault:"client",...e}),Object.setPrototypeOf(this,y.prototype)}}class g extends u{name="InvalidAuthorizationMessageException";$fault="client";constructor(e){super({name:"InvalidAuthorizationMessageException",$fault:"client",...e}),Object.setPrototypeOf(this,g.prototype)}}let x=async(e,t)=>{let r;return r=J({...I(e,t),[V]:G,[X]:K}),z(t,B,"/",void 0,r)},b=async(e,t)=>{if(e.statusCode>=300)return w(e,t);let r=await (0,a.t_)(e.body,t),n={};return n=k(r.GetCallerIdentityResult,t),{$metadata:_(e),...n}},w=async(e,t)=>{let r={...e,body:await (0,a.FI)(e.body,t)},n=Z(e,r.body);switch(n){case"ExpiredTokenException":case"com.amazonaws.sts#ExpiredTokenException":throw await v(r,t);case"MalformedPolicyDocument":case"com.amazonaws.sts#MalformedPolicyDocumentException":throw await $(r,t);case"PackedPolicyTooLarge":case"com.amazonaws.sts#PackedPolicyTooLargeException":throw await A(r,t);case"RegionDisabledException":case"com.amazonaws.sts#RegionDisabledException":throw await M(r,t);case"IDPRejectedClaim":case"com.amazonaws.sts#IDPRejectedClaimException":throw await S(r,t);case"InvalidIdentityToken":case"com.amazonaws.sts#InvalidIdentityTokenException":throw await O(r,t);case"IDPCommunicationError":case"com.amazonaws.sts#IDPCommunicationErrorException":throw await E(r,t);case"InvalidAuthorizationMessageException":case"com.amazonaws.sts#InvalidAuthorizationMessageException":throw await P(r,t);default:return L({output:e,parsedBody:r.body.Error,errorCode:n})}},v=async(e,t)=>{let r=e.body,n=C(r.Error,t),s=new l({$metadata:_(e),...n});return(0,i.Mw)(s,r)},E=async(e,t)=>{let r=e.body,n=T(r.Error,t),s=new y({$metadata:_(e),...n});return(0,i.Mw)(s,r)},S=async(e,t)=>{let r=e.body,n=R(r.Error,t),s=new h({$metadata:_(e),...n});return(0,i.Mw)(s,r)},P=async(e,t)=>{let r=e.body,n=N(r.Error,t),s=new g({$metadata:_(e),...n});return(0,i.Mw)(s,r)},O=async(e,t)=>{let r=e.body,n=D(r.Error,t),s=new m({$metadata:_(e),...n});return(0,i.Mw)(s,r)},$=async(e,t)=>{let r=e.body,n=U(r.Error,t),s=new d({$metadata:_(e),...n});return(0,i.Mw)(s,r)},A=async(e,t)=>{let r=e.body,n=j(r.Error,t),s=new p({$metadata:_(e),...n});return(0,i.Mw)(s,r)},M=async(e,t)=>{let r=e.body,n=F(r.Error,t),s=new f({$metadata:_(e),...n});return(0,i.Mw)(s,r)},I=(e,t)=>({}),C=(e,t)=>{let r={};return null!=e[Y]&&(r[Y]=(0,i.lK)(e[Y])),r},k=(e,t)=>{let r={};return null!=e[W]&&(r[W]=(0,i.lK)(e[W])),null!=e[H]&&(r[H]=(0,i.lK)(e[H])),null!=e[q]&&(r[q]=(0,i.lK)(e[q])),r},T=(e,t)=>{let r={};return null!=e[Y]&&(r[Y]=(0,i.lK)(e[Y])),r},R=(e,t)=>{let r={};return null!=e[Y]&&(r[Y]=(0,i.lK)(e[Y])),r},N=(e,t)=>{let r={};return null!=e[Y]&&(r[Y]=(0,i.lK)(e[Y])),r},D=(e,t)=>{let r={};return null!=e[Y]&&(r[Y]=(0,i.lK)(e[Y])),r},U=(e,t)=>{let r={};return null!=e[Y]&&(r[Y]=(0,i.lK)(e[Y])),r},j=(e,t)=>{let r={};return null!=e[Y]&&(r[Y]=(0,i.lK)(e[Y])),r},F=(e,t)=>{let r={};return null!=e[Y]&&(r[Y]=(0,i.lK)(e[Y])),r},_=e=>({httpStatusCode:e.statusCode,requestId:e.headers["x-amzn-requestid"]??e.headers["x-amzn-request-id"]??e.headers["x-amz-request-id"],extendedRequestId:e.headers["x-amz-id-2"],cfId:e.headers["x-amz-cf-id"]}),L=(0,i.jr)(u),z=async(e,t,r,n,s)=>{let{hostname:i,protocol:o="https",port:a,path:u}=await e.endpoint(),l={protocol:o,hostname:i,port:a,method:"POST",path:u.endsWith("/")?u.slice(0,-1)+r:u+r,headers:t};return void 0!==n&&(l.hostname=n),void 0!==s&&(l.body=s),new c.Kd(l)},B={"content-type":"application/x-www-form-urlencoded"},K="2011-06-15",V="Action",H="Account",q="Arn",G="GetCallerIdentity",W="UserId",X="Version",Y="message",J=e=>Object.entries(e).map(([e,t])=>(0,i.$6)(e)+"="+(0,i.$6)(t)).join("&"),Z=(e,t)=>t.Error?.Code!==void 0?t.Error.Code:404==e.statusCode?"NotFound":void 0;class Q extends i.uB.classBuilder().ep(o.S).m(function(e,t,r,i){return[(0,s.TM)(r,this.serialize,this.deserialize),(0,n.rD)(r,e.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","GetCallerIdentity",{}).n("STSClient","GetCallerIdentityCommand").f(void 0,void 0).ser(x).de(b).build(){}},7207:(e,t,r)=>{"use strict";r.d(t,{S:()=>s,v:()=>n});let n=e=>({...e,useDualstackEndpoint:e.useDualstackEndpoint??!1,useFipsEndpoint:e.useFipsEndpoint??!1,useGlobalEndpoint:e.useGlobalEndpoint??!1,defaultSigningName:"sts"}),s={UseGlobalEndpoint:{type:"builtInParams",name:"useGlobalEndpoint"},UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}}},8768:(e,t,r)=>{"use strict";r.d(t,{f2:()=>l});var n=r(7609);let s=e=>new Date(Date.now()+e),i=e=>n.cS.isInstance(e)?e.headers?.date??e.headers?.Date:void 0,o=(e,t)=>Math.abs(s(t).getTime()-e)>=3e5,a=(e,t)=>{let r=Date.parse(e);return o(r,t)?r-Date.now():t},c=(e,t)=>{if(!t)throw Error(`Property \`${e}\` is not resolved for AWS SDK SigV4Auth`);return t},u=async e=>{let t=c("context",e.context),r=c("config",e.config),n=t.endpointV2?.properties?.authSchemes?.[0],s=c("signer",r.signer),i=await s(n);return{config:r,signer:i,signingRegion:e?.signingRegion,signingRegionSet:e?.signingRegionSet,signingName:e?.signingName}};class l{async sign(e,t,r){if(!n.Kd.isInstance(e))throw Error("The request is not an instance of `HttpRequest` and cannot be signed");let i=await u(r),{config:o,signer:a}=i,{signingRegion:c,signingName:l}=i,d=r.context;if(d?.authSchemes?.length){let[e,t]=d.authSchemes;e?.name==="sigv4a"&&t?.name==="sigv4"&&(c=t?.signingRegion??c,l=t?.signingName??l)}return await a.sign(e,{signingDate:s(o.systemClockOffset),signingRegion:c,signingService:l})}errorHandler(e){return t=>{let r=t.ServerTime??i(t.$response);if(r){let n=c("config",e.config),s=n.systemClockOffset;n.systemClockOffset=a(r,n.systemClockOffset),n.systemClockOffset!==s&&t.$metadata&&(t.$metadata.clockSkewCorrected=!0)}throw t}}successHandler(e,t){let r=i(e);if(r){let e=c("config",t.config);e.systemClockOffset=a(r,e.systemClockOffset)}}}},7258:(e,t,r)=>{"use strict";r.d(t,{h:()=>i});var n=r(4383),s=r(8599);let i=e=>{let t,r,i=!1;e.credentials&&(i=!0,t=(0,n.K4)(e.credentials,n.OC,n.e)),t||(t=e.credentialDefaultProvider?(0,n.te)(e.credentialDefaultProvider(Object.assign({},e,{parentClientConfig:e}))):async()=>{throw Error("`credentials` is missing")});let o=async()=>t({callerClientConfig:e}),{signingEscapePath:a=!0,systemClockOffset:c=e.systemClockOffset||0,sha256:u}=e;return r=e.signer?(0,n.te)(e.signer):e.regionInfoProvider?()=>(0,n.te)(e.region)().then(async t=>[await e.regionInfoProvider(t,{useFipsEndpoint:await e.useFipsEndpoint(),useDualstackEndpoint:await e.useDualstackEndpoint()})||{},t]).then(([t,r])=>{let{signingRegion:n,signingService:i}=t;e.signingRegion=e.signingRegion||n||r,e.signingName=e.signingName||i||e.serviceId;let c={...e,credentials:o,region:e.signingRegion,service:e.signingName,sha256:u,uriEscapePath:a};return new(e.signerConstructor||s.BB)(c)}):async t=>{let r=(t=Object.assign({},{name:"sigv4",signingName:e.signingName||e.defaultSigningName,signingRegion:await (0,n.te)(e.region)(),properties:{}},t)).signingRegion,i=t.signingName;e.signingRegion=e.signingRegion||r,e.signingName=e.signingName||i||e.serviceId;let c={...e,credentials:o,region:e.signingRegion,service:e.signingName,sha256:u,uriEscapePath:a};return new(e.signerConstructor||s.BB)(c)},{...e,systemClockOffset:c,signingEscapePath:a,credentials:i?async()=>o().then(e=>(function(e,t,r){return e.$source||(e.$source={}),e.$source[t]="e",e})(e,"CREDENTIALS_CODE",0)):o,signer:r}}},8218:(e,t,r)=>{"use strict";r.d(t,{w:()=>s});var n=r(5268);let s=(e,t)=>(0,n.Px)(e,t).then(e=>t.utf8Encoder(e))},1919:(e,t,r)=>{"use strict";r.d(t,{CG:()=>i,Y2:()=>s,cJ:()=>o});var n=r(8218);let s=(e,t)=>(0,n.w)(e,t).then(e=>{if(e.length)try{return JSON.parse(e)}catch(t){throw t?.name==="SyntaxError"&&Object.defineProperty(t,"$responseBodyText",{value:e}),t}return{}}),i=async(e,t)=>{let r=await s(e,t);return r.message=r.message??r.Message,r},o=(e,t)=>{let r=e=>{let t=e;return"number"==typeof t&&(t=t.toString()),t.indexOf(",")>=0&&(t=t.split(",")[0]),t.indexOf(":")>=0&&(t=t.split(":")[0]),t.indexOf("#")>=0&&(t=t.split("#")[1]),t},n=Object.keys(e.headers).find(e=>"x-amzn-errortype"===e.toLowerCase());return void 0!==n?r(e.headers[n]):void 0!==t.code?r(t.code):void 0!==t.__type?r(t.__type):void 0}},5469:(e,t,r)=>{"use strict";r.d(t,{FI:()=>a,FZ:()=>c,t_:()=>o});var n=r(5268),s=r(6454),i=r(8218);let o=(e,t)=>(0,i.w)(e,t).then(e=>{if(e.length){let t;let r=new s.XMLParser({attributeNamePrefix:"",htmlEntities:!0,ignoreAttributes:!1,ignoreDeclaration:!0,parseTagValue:!1,trimValues:!1,tagValueProcessor:(e,t)=>""===t.trim()&&t.includes("\n")?"":void 0});r.addEntity("#xD","\r"),r.addEntity("#10","\n");try{t=r.parse(e,!0)}catch(t){throw t&&"object"==typeof t&&Object.defineProperty(t,"$responseBodyText",{value:e}),t}let i="#text",o=Object.keys(t)[0],a=t[o];return a[i]&&(a[o]=a[i],delete a[i]),(0,n.rm)(a)}return{}}),a=async(e,t)=>{let r=await o(e,t);return r.Error&&(r.Error.message=r.Error.message??r.Error.Message),r},c=(e,t)=>t?.Error?.Code!==void 0?t.Error.Code:t?.Code!==void 0?t.Code:404==e.statusCode?"NotFound":void 0},1095:(e,t,r)=>{"use strict";r.d(t,{OV:()=>s,TC:()=>a});var n=r(7609);function s(e){return e}let i=e=>t=>async r=>{if(!n.Kd.isInstance(r.request))return t(r);let{request:s}=r,{handlerProtocol:i=""}=e.requestHandler.metadata||{};if(i.indexOf("h2")>=0&&!s.headers[":authority"])delete s.headers.host,s.headers[":authority"]=s.hostname+(s.port?":"+s.port:"");else if(!s.headers.host){let e=s.hostname;null!=s.port&&(e+=`:${s.port}`),s.headers.host=e}return t(r)},o={name:"hostHeaderMiddleware",step:"build",priority:"low",tags:["HOST"],override:!0},a=e=>({applyToStack:t=>{t.add(i(e),o)}})},5937:(e,t,r)=>{"use strict";r.d(t,{Y7:()=>i});let n=()=>(e,t)=>async r=>{try{let n=await e(r),{clientName:s,commandName:i,logger:o,dynamoDbDocumentClientOptions:a={}}=t,{overrideInputFilterSensitiveLog:c,overrideOutputFilterSensitiveLog:u}=a,l=c??t.inputFilterSensitiveLog,d=u??t.outputFilterSensitiveLog,{$metadata:p,...f}=n.output;return o?.info?.({clientName:s,commandName:i,input:l(r.input),output:d(f),metadata:p}),n}catch(c){let{clientName:e,commandName:n,logger:s,dynamoDbDocumentClientOptions:i={}}=t,{overrideInputFilterSensitiveLog:o}=i,a=o??t.inputFilterSensitiveLog;throw s?.error?.({clientName:e,commandName:n,input:a(r.input),error:c,metadata:c.$metadata}),c}},s={name:"loggerMiddleware",tags:["LOGGER"],step:"initialize",override:!0},i=e=>({applyToStack:e=>{e.add(n(),s)}})},8377:(e,t,r)=>{"use strict";r.d(t,{n4:()=>c});var n=r(7609),s=r(7836);let i="X-Amzn-Trace-Id",o=e=>t=>async r=>{let{request:o}=r;if(!n.Kd.isInstance(o)||"node"!==e.runtime||o.headers.hasOwnProperty(i))return t(r);let a=s.env.AWS_LAMBDA_FUNCTION_NAME,c=s.env._X_AMZN_TRACE_ID;return(e=>"string"==typeof e&&e.length>0)(a)&&(e=>"string"==typeof e&&e.length>0)(c)&&(o.headers[i]=c),t({...r,request:o})},a={step:"build",tags:["RECURSION_DETECTION"],name:"recursionDetectionMiddleware",override:!0,priority:"low"},c=e=>({applyToStack:t=>{t.add(o(e),a)}})},4196:(e,t,r)=>{"use strict";r.d(t,{e:()=>i,Ek:()=>c});let n=/^\/(hostedzone|change|delegationset)\//,s={step:"initialize",tags:["ROUTE53_IDS","CHANGE_RESOURCE_RECORD_SETS"],name:"changeResourceRecordSetsMiddleware",override:!0},i=e=>({applyToStack:e=>{e.add(e=>async t=>{let{ChangeBatch:r}=t.input,s=[];for(let e of r.Changes){let{AliasTarget:t}=e.ResourceRecordSet;t?s.push({...e,ResourceRecordSet:{...e.ResourceRecordSet,AliasTarget:{...t,HostedZoneId:t.HostedZoneId.replace(n,"")}}}):s.push(e)}return e({...t,input:{...t.input,ChangeBatch:{...r,Changes:s}}})},s)}}),o=["DelegationSetId","HostedZoneId","Id"],a={step:"initialize",tags:["ROUTE53_IDS"],name:"idNormalizerMiddleware",override:!0},c=e=>({applyToStack:e=>{e.add(e=>async t=>{let r={...t.input};for(let e of o){let t=r[e];t&&(r[e]=t.replace(n,""))}return e({...t,input:r})},a)}})},1136:(e,t,r)=>{"use strict";r.d(t,{sM:()=>x,Dc:()=>i});var n=r(4383);let s=void 0;function i(e){let t=(0,n.te)(e.userAgentAppId??s);return{...e,customUserAgent:"string"==typeof e.customUserAgent?[[e.customUserAgent]]:e.customUserAgent,userAgentAppId:async()=>{let r=await t();if(!(void 0===r||"string"==typeof r&&r.length<=50)){let t=e.logger?.constructor?.name!=="NoOpLogger"&&e.logger?e.logger:console;"string"!=typeof r?t?.warn("userAgentAppId must be a string or undefined."):r.length>50&&t?.warn("The provided userAgentAppId exceeds the maximum length of 50 characters.")}return r}}}var o=r(6137),a=r(7609);function c(e,t,r){e.__aws_sdk_context?e.__aws_sdk_context.features||(e.__aws_sdk_context.features={}):e.__aws_sdk_context={features:{}},e.__aws_sdk_context.features[t]=r}let u=/\d{12}\.ddb/;async function l(e,t,r){let n=r.request;if(n?.headers?.["smithy-protocol"]==="rpc-v2-cbor"&&c(e,"PROTOCOL_RPC_V2_CBOR","M"),"function"==typeof t.retryStrategy){let r=await t.retryStrategy();"function"==typeof r.acquireInitialRetryToken?r.constructor?.name?.includes("Adaptive")?c(e,"RETRY_MODE_ADAPTIVE","F"):c(e,"RETRY_MODE_STANDARD","E"):c(e,"RETRY_MODE_LEGACY","D")}if("function"==typeof t.accountIdEndpointMode){let r=e.endpointV2;switch(String(r?.url?.hostname).match(u)&&c(e,"ACCOUNT_ID_ENDPOINT","O"),await t.accountIdEndpointMode?.()){case"disabled":c(e,"ACCOUNT_ID_MODE_DISABLED","Q");break;case"preferred":c(e,"ACCOUNT_ID_MODE_PREFERRED","P");break;case"required":c(e,"ACCOUNT_ID_MODE_REQUIRED","R")}}let s=e.__smithy_context?.selectedHttpAuthScheme?.identity;if(s?.$source)for(let[t,r]of(s.accountId&&c(e,"RESOLVED_ACCOUNT_ID","T"),Object.entries(s.$source??{})))c(e,t,r)}let d="user-agent",p="x-amz-user-agent",f=/[^\!\$\%\&\'\*\+\-\.\^\_\`\|\~\d\w]/g,h=/[^\!\$\%\&\'\*\+\-\.\^\_\`\|\~\d\w\#]/g,m=e=>(t,r)=>async n=>{let{request:s}=n;if(!a.Kd.isInstance(s))return t(n);let{headers:i}=s,c=r?.userAgent?.map(y)||[],u=(await e.defaultUserAgentProvider()).map(y);await l(r,e,n),u.push(`m/${function(e){let t="";for(let r in e){let n=e[r];if(t.length+n.length+1<=1024){t.length?t+=","+n:t+=n;continue}break}return t}(Object.assign({},r.__smithy_context?.features,r.__aws_sdk_context?.features))}`);let f=e?.customUserAgent?.map(y)||[],h=await e.userAgentAppId();h&&u.push(y([`app/${h}`]));let m=(0,o.vL)(),g=(m?[m]:[]).concat([...u,...c,...f]).join(" "),x=[...u.filter(e=>e.startsWith("aws-sdk-")),...f].join(" ");return"browser"!==e.runtime?(x&&(i[p]=i[p]?`${i[d]} ${x}`:x),i[d]=g):i[p]=g,t({...n,request:s})},y=e=>{let t=e[0].split("/").map(e=>e.replace(f,"-")).join("/"),r=e[1]?.replace(h,"-"),n=t.indexOf("/"),s=t.substring(0,n),i=t.substring(n+1);return"api"===s&&(i=i.toLowerCase()),[s,i,r].filter(e=>e&&e.length>0).reduce((e,t,r)=>{switch(r){case 0:return t;case 1:return`${e}/${t}`;default:return`${e}#${t}`}},"")},g={name:"getUserAgentMiddleware",step:"build",priority:"low",tags:["SET_USER_AGENT","USER_AGENT"],override:!0},x=e=>({applyToStack:t=>{t.add(m(e),g)}})},3614:(e,t,r)=>{"use strict";r.d(t,{Rq:()=>n,$3:()=>s});let n=e=>{let t=async()=>{if(void 0===e.region)throw Error("Region is missing from runtimeConfig");let t=e.region;return"string"==typeof t?t:t()};return{setRegion(e){t=e},region:()=>t}},s=e=>({region:e.region()})},6137:(e,t,r)=>{"use strict";r.d(t,{UF:()=>a,vL:()=>o});var n=r(8636);let s=(e,t=!1)=>{if(t){for(let t of e.split("."))if(!s(t))return!1;return!0}return!(!(0,n.X8)(e)||e.length<3||e.length>63||e!==e.toLowerCase()||(0,n.oX)(e))},i=JSON.parse('{"partitions":[{"id":"aws","outputs":{"dnsSuffix":"amazonaws.com","dualStackDnsSuffix":"api.aws","implicitGlobalRegion":"us-east-1","name":"aws","supportsDualStack":true,"supportsFIPS":true},"regionRegex":"^(us|eu|ap|sa|ca|me|af|il|mx)\\\\-\\\\w+\\\\-\\\\d+$","regions":{"af-south-1":{"description":"Africa (Cape Town)"},"ap-east-1":{"description":"Asia Pacific (Hong Kong)"},"ap-northeast-1":{"description":"Asia Pacific (Tokyo)"},"ap-northeast-2":{"description":"Asia Pacific (Seoul)"},"ap-northeast-3":{"description":"Asia Pacific (Osaka)"},"ap-south-1":{"description":"Asia Pacific (Mumbai)"},"ap-south-2":{"description":"Asia Pacific (Hyderabad)"},"ap-southeast-1":{"description":"Asia Pacific (Singapore)"},"ap-southeast-2":{"description":"Asia Pacific (Sydney)"},"ap-southeast-3":{"description":"Asia Pacific (Jakarta)"},"ap-southeast-4":{"description":"Asia Pacific (Melbourne)"},"ap-southeast-5":{"description":"Asia Pacific (Malaysia)"},"ap-southeast-7":{"description":"Asia Pacific (Thailand)"},"aws-global":{"description":"AWS Standard global region"},"ca-central-1":{"description":"Canada (Central)"},"ca-west-1":{"description":"Canada West (Calgary)"},"eu-central-1":{"description":"Europe (Frankfurt)"},"eu-central-2":{"description":"Europe (Zurich)"},"eu-north-1":{"description":"Europe (Stockholm)"},"eu-south-1":{"description":"Europe (Milan)"},"eu-south-2":{"description":"Europe (Spain)"},"eu-west-1":{"description":"Europe (Ireland)"},"eu-west-2":{"description":"Europe (London)"},"eu-west-3":{"description":"Europe (Paris)"},"il-central-1":{"description":"Israel (Tel Aviv)"},"me-central-1":{"description":"Middle East (UAE)"},"me-south-1":{"description":"Middle East (Bahrain)"},"mx-central-1":{"description":"Mexico (Central)"},"sa-east-1":{"description":"South America (Sao Paulo)"},"us-east-1":{"description":"US East (N. Virginia)"},"us-east-2":{"description":"US East (Ohio)"},"us-west-1":{"description":"US West (N. California)"},"us-west-2":{"description":"US West (Oregon)"}}},{"id":"aws-cn","outputs":{"dnsSuffix":"amazonaws.com.cn","dualStackDnsSuffix":"api.amazonwebservices.com.cn","implicitGlobalRegion":"cn-northwest-1","name":"aws-cn","supportsDualStack":true,"supportsFIPS":true},"regionRegex":"^cn\\\\-\\\\w+\\\\-\\\\d+$","regions":{"aws-cn-global":{"description":"AWS China global region"},"cn-north-1":{"description":"China (Beijing)"},"cn-northwest-1":{"description":"China (Ningxia)"}}},{"id":"aws-us-gov","outputs":{"dnsSuffix":"amazonaws.com","dualStackDnsSuffix":"api.aws","implicitGlobalRegion":"us-gov-west-1","name":"aws-us-gov","supportsDualStack":true,"supportsFIPS":true},"regionRegex":"^us\\\\-gov\\\\-\\\\w+\\\\-\\\\d+$","regions":{"aws-us-gov-global":{"description":"AWS GovCloud (US) global region"},"us-gov-east-1":{"description":"AWS GovCloud (US-East)"},"us-gov-west-1":{"description":"AWS GovCloud (US-West)"}}},{"id":"aws-iso","outputs":{"dnsSuffix":"c2s.ic.gov","dualStackDnsSuffix":"c2s.ic.gov","implicitGlobalRegion":"us-iso-east-1","name":"aws-iso","supportsDualStack":false,"supportsFIPS":true},"regionRegex":"^us\\\\-iso\\\\-\\\\w+\\\\-\\\\d+$","regions":{"aws-iso-global":{"description":"AWS ISO (US) global region"},"us-iso-east-1":{"description":"US ISO East"},"us-iso-west-1":{"description":"US ISO WEST"}}},{"id":"aws-iso-b","outputs":{"dnsSuffix":"sc2s.sgov.gov","dualStackDnsSuffix":"sc2s.sgov.gov","implicitGlobalRegion":"us-isob-east-1","name":"aws-iso-b","supportsDualStack":false,"supportsFIPS":true},"regionRegex":"^us\\\\-isob\\\\-\\\\w+\\\\-\\\\d+$","regions":{"aws-iso-b-global":{"description":"AWS ISOB (US) global region"},"us-isob-east-1":{"description":"US ISOB East (Ohio)"}}},{"id":"aws-iso-e","outputs":{"dnsSuffix":"cloud.adc-e.uk","dualStackDnsSuffix":"cloud.adc-e.uk","implicitGlobalRegion":"eu-isoe-west-1","name":"aws-iso-e","supportsDualStack":false,"supportsFIPS":true},"regionRegex":"^eu\\\\-isoe\\\\-\\\\w+\\\\-\\\\d+$","regions":{"eu-isoe-west-1":{"description":"EU ISOE West"}}},{"id":"aws-iso-f","outputs":{"dnsSuffix":"csp.hci.ic.gov","dualStackDnsSuffix":"csp.hci.ic.gov","implicitGlobalRegion":"us-isof-south-1","name":"aws-iso-f","supportsDualStack":false,"supportsFIPS":true},"regionRegex":"^us\\\\-isof\\\\-\\\\w+\\\\-\\\\d+$","regions":{}}],"version":"1.1"}'),o=()=>"",a={isVirtualHostableS3Bucket:s,parseArn:e=>{let t=e.split(":");if(t.length<6)return null;let[r,n,s,i,o,...a]=t;return"arn"!==r||""===n||""===s||""===a.join(":")?null:{partition:n,service:s,region:i,accountId:o,resourceId:a.map(e=>e.split("/")).flat()}},partition:e=>{let{partitions:t}=i;for(let r of t){let{regions:t,outputs:n}=r;for(let[r,s]of Object.entries(t))if(r===e)return{...n,...s}}for(let r of t){let{regionRegex:t,outputs:n}=r;if(new RegExp(t).test(e))return{...n}}let r=t.find(e=>"aws"===e.id);if(!r)throw Error("Provided region was not found in the partition array or regex, and default partition with id 'aws' doesn't exist.");return{...r.outputs}}};n.mw.aws=a},9391:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var n=r(6880),s=r.n(n);let i=({serviceId:e,clientVersion:t})=>async r=>{let n="undefined"!=typeof window&&window?.navigator?.userAgent?s().parse(window.navigator.userAgent):void 0,i=[["aws-sdk-js",t],["ua","2.1"],[`os/${n?.os?.name||"other"}`,n?.os?.version],["lang/js"],["md/browser",`${n?.browser?.name??"unknown"}_${n?.browser?.version??"unknown"}`]];e&&i.push([`api/${e}`,t]);let o=await r?.userAgentAppId?.();return o&&i.push([`app/${o}`]),i}},7823:(e,t,r)=>{"use strict";r.d(t,{C:()=>s});class n{value;constructor(e){this.value=e}toString(){return(""+this.value).replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&apos;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\r/g,"&#x0D;").replace(/\n/g,"&#x0A;").replace(/\u0085/g,"&#x85;").replace(/\u2028/,"&#x2028;")}}class s{name;children;attributes={};static of(e,t,r){let i=new s(e);return void 0!==t&&i.addChildNode(new n(t)),void 0!==r&&i.withName(r),i}constructor(e,t=[]){this.name=e,this.children=t}withName(e){return this.name=e,this}addAttribute(e,t){return this.attributes[e]=t,this}addChildNode(e){return this.children.push(e),this}removeAttribute(e){return delete this.attributes[e],this}n(e){return this.name=e,this}c(e){return this.children.push(e),this}a(e,t){return null!=t&&(this.attributes[e]=t),this}cc(e,t,r=t){if(null!=e[t]){let n=s.of(t,e[t]).withName(r);this.c(n)}}l(e,t,r,n){null!=e[t]&&n().map(e=>{e.withName(r),this.c(e)})}lc(e,t,r,n){if(null!=e[t]){let e=n(),t=new s(r);e.map(e=>{t.c(e)}),this.c(t)}}toString(){let e=!!this.children.length,t=`<${this.name}`,r=this.attributes;for(let e of Object.keys(r)){let n=r[e];null!=n&&(t+=` ${e}="${(""+n).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;")}"`)}return t+(e?`>${this.children.map(e=>e.toString()).join("")}</${this.name}>`:"/>")}}},1651:(e,t,r)=>{"use strict";var n;r.d(t,{VW:()=>s,Hj:()=>i,TD:()=>c}),function(e){e.ENV="env",e.CONFIG="shared config entry"}(n||(n={}));let s=!1,i=!1;r(9848);let o=e=>"string"==typeof e&&(e.startsWith("fips-")||e.endsWith("-fips")),a=e=>o(e)?["fips-aws-global","aws-fips"].includes(e)?"us-east-1":e.replace(/fips-(dkr-|prod-)?|-fips/,""):e,c=e=>{let{region:t,useFipsEndpoint:r}=e;if(!t)throw Error("Region is missing");return{...e,region:async()=>"string"==typeof t?a(t):a(await t()),useFipsEndpoint:async()=>!!o("string"==typeof t?t:await t())||("function"!=typeof r?Promise.resolve(!!r):r())}}},4383:(e,t,r)=>{"use strict";r.d(t,{h$:()=>y,mR:()=>g,e:()=>b,wB:()=>o,lW:()=>p,OC:()=>x,K4:()=>w,te:()=>f,lI:()=>h.lI,J7:()=>m}),r(6100);var n=r(9848);let s=(e,t)=>(r,s)=>async i=>{let o=e.httpAuthSchemeProvider(await t.httpAuthSchemeParametersProvider(e,s,i.input)),a=function(e){let t=new Map;for(let r of e)t.set(r.schemeId,r);return t}(e.httpAuthSchemes),c=(0,n.u)(s),u=[];for(let r of o){let n=a.get(r.schemeId);if(!n){u.push(`HttpAuthScheme \`${r.schemeId}\` was not enabled for this service.`);continue}let i=n.identityProvider(await t.identityProviderConfigProvider(e));if(!i){u.push(`HttpAuthScheme \`${r.schemeId}\` did not have an IdentityProvider configured.`);continue}let{identityProperties:o={},signingProperties:l={}}=r.propertiesExtractor?.(e,s)||{};r.identityProperties=Object.assign(r.identityProperties||{},o),r.signingProperties=Object.assign(r.signingProperties||{},l),c.selectedHttpAuthScheme={httpAuthOption:r,identity:await i(r.identityProperties),signer:n.signer};break}if(!c.selectedHttpAuthScheme)throw Error(u.join("\n"));return r(i)},i={step:"serialize",tags:["HTTP_AUTH_SCHEME"],name:"httpAuthSchemeMiddleware",override:!0,relation:"before",toMiddleware:"endpointV2Middleware"},o=(e,{httpAuthSchemeParametersProvider:t,identityProviderConfigProvider:r})=>({applyToStack:n=>{n.addRelativeTo(s(e,{httpAuthSchemeParametersProvider:t,identityProviderConfigProvider:r}),i)}});r(228).Ou.name;var a=r(7609);let c=e=>e=>{throw e},u=(e,t)=>{},l=e=>(e,t)=>async r=>{if(!a.Kd.isInstance(r.request))return e(r);let s=(0,n.u)(t).selectedHttpAuthScheme;if(!s)throw Error("No HttpAuthScheme was selected: unable to sign request");let{httpAuthOption:{signingProperties:i={}},identity:o,signer:l}=s,d=await e({...r,request:await l.sign(r.request,o,i)}).catch((l.errorHandler||c)(i));return(l.successHandler||u)(d.response,i),d},d={step:"finalizeRequest",tags:["HTTP_SIGNING"],name:"httpSigningMiddleware",aliases:["apiKeyMiddleware","tokenMiddleware","awsAuthMiddleware"],override:!0,relation:"after",toMiddleware:"retryMiddleware"},p=e=>({applyToStack:t=>{t.addRelativeTo(l(e),d)}}),f=e=>{if("function"==typeof e)return e;let t=Promise.resolve(e);return()=>t};var h=r(2e3);function m(e,t,r){e.__smithy_context?e.__smithy_context.features||(e.__smithy_context.features={}):e.__smithy_context={features:{}},e.__smithy_context.features[t]=r}class y{constructor(e){for(let[t,r]of(this.authSchemes=new Map,Object.entries(e)))void 0!==r&&this.authSchemes.set(t,r)}getIdentityProvider(e){return this.authSchemes.get(e)}}class g{async sign(e,t,r){return e}}let x=e=>b(e)&&e.expiration.getTime()-Date.now()<3e5,b=e=>void 0!==e.expiration,w=(e,t,r)=>{let n,s,i;if(void 0===e)return;let o="function"!=typeof e?async()=>Promise.resolve(e):e,a=!1,c=async e=>{s||(s=o(e));try{n=await s,i=!0,a=!1}finally{s=void 0}return n};return void 0===t?async e=>((!i||e?.forceRefresh)&&(n=await c(e)),n):async e=>((!i||e?.forceRefresh)&&(n=await c(e)),a||(r(n)?t(n)&&await c(e):a=!0),n)}},2e3:(e,t,r)=>{"use strict";r.d(t,{Px:()=>a,$6:()=>c,lI:()=>d});var n=r(9764),s=r(3411),i=r(2637);class o extends Uint8Array{static fromString(e,t="utf-8"){if("string"==typeof e)return"base64"===t?o.mutate((0,n.E)(e)):o.mutate((0,i.a)(e));throw Error(`Unsupported conversion from ${typeof e} to Uint8ArrayBlobAdapter.`)}static mutate(e){return Object.setPrototypeOf(e,o.prototype),e}transformToString(e="utf-8"){return function(e,t="utf-8"){return"base64"===t?(0,n.n)(e):(0,s.P)(e)}(this,e)}}"function"==typeof ReadableStream&&ReadableStream,r(2928).Buffer,r(9124),r(8004);let a=async(e=new Uint8Array,t)=>{if(e instanceof Uint8Array)return o.mutate(e);if(!e)return o.mutate(new Uint8Array);let r=t.streamCollector(e);return o.mutate(await r)};function c(e){return encodeURIComponent(e).replace(/[!'()*]/g,function(e){return"%"+e.charCodeAt(0).toString(16).toUpperCase()})}var u=r(7609);let l=(e,t,r,n,s,i)=>{if(null!=t&&void 0!==t[r]){let t=n();if(t.length<=0)throw Error("Empty value provided for input HTTP label: "+r+".");e=e.replace(s,i?t.split("/").map(e=>c(e)).join("/"):c(t))}else throw Error("No value provided for input HTTP label: "+r+".");return e};function d(e,t){return new p(e,t)}class p{constructor(e,t){this.input=e,this.context=t,this.query={},this.method="",this.headers={},this.path="",this.body=null,this.hostname="",this.resolvePathStack=[]}async build(){let{hostname:e,protocol:t="https",port:r,path:n}=await this.context.endpoint();for(let e of(this.path=n,this.resolvePathStack))e(this.path);return new u.Kd({protocol:t,hostname:this.hostname||e,port:r,method:this.method,path:this.path,query:this.query,body:this.body,headers:this.headers})}hn(e){return this.hostname=e,this}bp(e){return this.resolvePathStack.push(t=>{this.path=`${t?.endsWith("/")?t.slice(0,-1):t||""}`+e}),this}p(e,t,r,n){return this.resolvePathStack.push(s=>{this.path=l(s,this.input,e,t,r,n)}),this}h(e){return this.headers=e,this}q(e){return this.query=e,this}b(e){return this.body=e,this}m(e){return this.method=e,this}}},9124:(e,t,r)=>{"use strict";r.d(t,{NC:()=>a,kv:()=>u});var n=r(7609),s=r(2531);function i(e,t){return new Request(e,t)}let o={supported:void 0};class a{static create(e){return"function"==typeof e?.handle?e:new a(e)}constructor(e){"function"==typeof e?this.configProvider=e().then(e=>e||{}):(this.config=e??{},this.configProvider=Promise.resolve(this.config)),void 0===o.supported&&(o.supported=!!("undefined"!=typeof Request&&"keepalive"in i("https://[::1]")))}destroy(){}async handle(e,{abortSignal:t}={}){this.config||(this.config=await this.configProvider);let r=this.config.requestTimeout,a=!0===this.config.keepAlive,c=this.config.credentials;if(t?.aborted){let e=Error("Request aborted");return e.name="AbortError",Promise.reject(e)}let u=e.path,l=function(e){let t=[];for(let r of Object.keys(e).sort()){let n=e[r];if(r=(0,s.o)(r),Array.isArray(n))for(let e=0,i=n.length;e<i;e++)t.push(`${r}=${(0,s.o)(n[e])}`);else{let e=r;(n||"string"==typeof n)&&(e+=`=${(0,s.o)(n)}`),t.push(e)}}return t.join("&")}(e.query||{});l&&(u+=`?${l}`),e.fragment&&(u+=`#${e.fragment}`);let d="";if(null!=e.username||null!=e.password){let t=e.username??"",r=e.password??"";d=`${t}:${r}@`}let{port:p,method:f}=e,h=`${e.protocol}//${d}${e.hostname}${p?`:${p}`:""}${u}`,m="GET"===f||"HEAD"===f?void 0:e.body,y={body:m,headers:new Headers(e.headers),method:f,credentials:c};this.config?.cache&&(y.cache=this.config.cache),m&&(y.duplex="half"),"undefined"!=typeof AbortController&&(y.signal=t),o.supported&&(y.keepalive=a),"function"==typeof this.config.requestInit&&Object.assign(y,this.config.requestInit(e));let g=()=>{},x=[fetch(i(h,y)).then(e=>{let t=e.headers,r={};for(let e of t.entries())r[e[0]]=e[1];return void 0!=e.body?{response:new n.cS({headers:r,reason:e.statusText,statusCode:e.status,body:e.body})}:e.blob().then(t=>({response:new n.cS({headers:r,reason:e.statusText,statusCode:e.status,body:t})}))}),function(e=0){return new Promise((t,r)=>{e&&setTimeout(()=>{let t=Error(`Request did not complete within ${e} ms`);t.name="TimeoutError",r(t)},e)})}(r)];return t&&x.push(new Promise((e,r)=>{let n=()=>{let e=Error("Request aborted");e.name="AbortError",r(e)};"function"==typeof t.addEventListener?(t.addEventListener("abort",n,{once:!0}),g=()=>t.removeEventListener("abort",n)):t.onabort=n})),Promise.race(x).finally(g)}updateHttpClientConfig(e,t){this.config=void 0,this.configProvider=this.configProvider.then(r=>(r[e]=t,r))}httpHandlerConfigs(){return this.config??{}}}var c=r(9764);let u=async e=>"function"==typeof Blob&&e instanceof Blob||e.constructor?.name==="Blob"?void 0!==Blob.prototype.arrayBuffer?new Uint8Array(await e.arrayBuffer()):l(e):d(e);async function l(e){let t=await new Promise((t,r)=>{let n=new FileReader;n.onloadend=()=>{if(2!==n.readyState)return r(Error("Reader aborted too early"));let e=n.result??"",s=e.indexOf(","),i=s>-1?s+1:e.length;t(e.substring(i))},n.onabort=()=>r(Error("Read aborted")),n.onerror=()=>r(n.error),n.readAsDataURL(e)});return new Uint8Array((0,c.E)(t))}async function d(e){let t=[],r=e.getReader(),n=!1,s=0;for(;!n;){let{done:e,value:i}=await r.read();i&&(t.push(i),s+=i.length),n=e}let i=new Uint8Array(s),o=0;for(let e of t)i.set(e,o),o+=e.length;return i}},2423:(e,t,r)=>{"use strict";r.d(t,{B:()=>n});let n=e=>()=>Promise.reject(e)},649:(e,t,r)=>{"use strict";r.d(t,{vK:()=>o});var n=r(7609);let s="content-length",i={step:"build",tags:["SET_CONTENT_LENGTH","CONTENT_LENGTH"],name:"contentLengthMiddleware",override:!0},o=e=>({applyToStack:t=>{var r;t.add((r=e.bodyLengthChecker,e=>async t=>{let i=t.request;if(n.Kd.isInstance(i)){let{body:e,headers:t}=i;if(e&&-1===Object.keys(t).map(e=>e.toLowerCase()).indexOf(s))try{let t=r(e);i.headers={...i.headers,[s]:String(t)}}catch(e){}}return e({...t,request:i})}),i)}})},8176:(e,t,r)=>{"use strict";r.d(t,{rD:()=>b,Co:()=>w});var n=r(4383),s=r(9848);let i=async e=>{let t=e?.Bucket||"";if("string"==typeof e.Bucket&&(e.Bucket=t.replace(/#/g,encodeURIComponent("#")).replace(/\?/g,encodeURIComponent("?"))),l(t)){if(!0===e.ForcePathStyle)throw Error("Path-style addressing cannot be used with ARN buckets")}else u(t)&&(-1===t.indexOf(".")||String(e.Endpoint).startsWith("http:"))&&t.toLowerCase()===t&&!(t.length<3)||(e.ForcePathStyle=!0);return e.DisableMultiRegionAccessPoints&&(e.disableMultiRegionAccessPoints=!0,e.DisableMRAP=!0),e},o=/^[a-z0-9][a-z0-9\.\-]{1,61}[a-z0-9]$/,a=/(\d+\.){3}\d+/,c=/\.\./,u=e=>o.test(e)&&!a.test(e)&&!c.test(e),l=e=>{let[t,r,n,,,s]=e.split(":"),i="arn"===t&&e.split(":").length>=6,o=!!(i&&r&&n&&s);if(i&&!o)throw Error(`Invalid ARN: ${e} was an invalid ARN.`);return o},d=(e,t,r)=>{let n=async()=>{let n=r[e]??r[t];return"function"==typeof n?n():n};return"credentialScope"===e||"CredentialScope"===t?async()=>{let e="function"==typeof r.credentials?await r.credentials():r.credentials;return e?.credentialScope??e?.CredentialScope}:"accountId"===e||"AccountId"===t?async()=>{let e="function"==typeof r.credentials?await r.credentials():r.credentials;return e?.accountId??e?.AccountId}:"endpoint"===e||"endpoint"===t?async()=>{let e=await n();if(e&&"object"==typeof e){if("url"in e)return e.url.href;if("hostname"in e){let{protocol:t,hostname:r,port:n,path:s}=e;return`${t}//${r}${n?":"+n:""}${s}`}}return e}:n},p=async e=>void 0;var f=r(4262);let h=e=>"object"==typeof e?"url"in e?(0,f.D)(e.url):e:(0,f.D)(e),m=async(e,t,r,n)=>{if(!r.endpoint){let e;(e=r.serviceConfiguredEndpoint?await r.serviceConfiguredEndpoint():await p(r.serviceId))&&(r.endpoint=()=>Promise.resolve(h(e)))}let s=await y(e,t,r);if("function"!=typeof r.endpointProvider)throw Error("config.endpointProvider is not set.");return r.endpointProvider(s,n)},y=async(e,t,r)=>{let n={},s=t?.getEndpointParameterInstructions?.()||{};for(let[t,i]of Object.entries(s))switch(i.type){case"staticContextParams":n[t]=i.value;break;case"contextParams":n[t]=e[i.name];break;case"clientContextParams":case"builtInParams":n[t]=await d(i.name,t,r)();break;case"operationContextParams":n[t]=i.get(e);break;default:throw Error("Unrecognized endpoint parameter instruction: "+JSON.stringify(i))}return 0===Object.keys(s).length&&Object.assign(n,r),"s3"===String(r.serviceId).toLowerCase()&&await i(n),n},g=({config:e,instructions:t})=>(r,i)=>async o=>{e.endpoint&&(0,n.J7)(i,"ENDPOINT_OVERRIDE","N");let a=await m(o.input,{getEndpointParameterInstructions:()=>t},{...e},i);i.endpointV2=a,i.authSchemes=a.properties?.authSchemes;let c=i.authSchemes?.[0];if(c){i.signing_region=c.signingRegion,i.signing_service=c.signingName;let e=(0,s.u)(i),t=e?.selectedHttpAuthScheme?.httpAuthOption;t&&(t.signingProperties=Object.assign(t.signingProperties||{},{signing_region:c.signingRegion,signingRegion:c.signingRegion,signing_service:c.signingName,signingName:c.signingName,signingRegionSet:c.signingRegionSet},c.properties))}return r({...o})},x={step:"serialize",tags:["ENDPOINT_PARAMETERS","ENDPOINT_V2","ENDPOINT"],name:"endpointV2Middleware",override:!0,relation:"before",toMiddleware:r(228).Ou.name},b=(e,t)=>({applyToStack:r=>{r.addRelativeTo(g({config:e,instructions:t}),x)}}),w=e=>{let t;let r=e.tls??!0,{endpoint:n}=e,i=null!=n?async()=>h(await (0,s.t)(n)()):void 0,o={...e,endpoint:i,tls:r,isCustomEndpoint:!!n,useDualstackEndpoint:(0,s.t)(e.useDualstackEndpoint??!1),useFipsEndpoint:(0,s.t)(e.useFipsEndpoint??!1)};return o.serviceConfiguredEndpoint=async()=>(e.serviceId&&!t&&(t=p(e.serviceId)),t),o}},6320:(e,t,r)=>{"use strict";let n;r.d(t,{ey:()=>S,$z:()=>m});var s=r(2073),i=r(7609),o=r(1924);let a={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)},c=new Uint8Array(16),u=[];for(let e=0;e<256;++e)u.push((e+256).toString(16).slice(1));let l=function(e,t,r){if(a.randomUUID&&!t&&!e)return a.randomUUID();let s=(e=e||{}).random||(e.rng||function(){if(!n&&!(n="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)))throw Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return n(c)})();if(s[6]=15&s[6]|64,s[8]=63&s[8]|128,t){r=r||0;for(let e=0;e<16;++e)t[r+e]=s[e];return t}return function(e,t=0){return u[e[t+0]]+u[e[t+1]]+u[e[t+2]]+u[e[t+3]]+"-"+u[e[t+4]]+u[e[t+5]]+"-"+u[e[t+6]]+u[e[t+7]]+"-"+u[e[t+8]]+u[e[t+9]]+"-"+u[e[t+10]]+u[e[t+11]]+u[e[t+12]]+u[e[t+13]]+u[e[t+14]]+u[e[t+15]]}(s)},d=e=>e instanceof Error?e:e instanceof Object?Object.assign(Error(),e):"string"==typeof e?Error(e):Error(`AWS SDK error wrapper for ${e}`);var p=r(9848);let f="AWS_MAX_ATTEMPTS",h="max_attempts";e=>{let t=e[f];if(!t)return;let r=parseInt(t);if(Number.isNaN(r))throw Error(`Environment variable ${f} mast be a number, got "${t}"`);return r},s.Gz;let m=e=>{let{retryStrategy:t}=e,r=(0,p.t)(e.maxAttempts??s.Gz);return{...e,maxAttempts:r,retryStrategy:async()=>t||(await (0,p.t)(e.retryMode)()===s.cm.ADAPTIVE?new s.Y(r):new s.ru(r))}};e=>e.AWS_RETRY_MODE,s.L0;var y=r(5268);let g=e=>e?.body instanceof ReadableStream,x=e=>(t,r)=>async n=>{let o=await e.retryStrategy(),a=await e.maxAttempts();if(!b(o))return o?.mode&&(r.userAgent=[...r.userAgent||[],["cfg/retry-mode",o.mode]]),o.retry(t,n);{let e=await o.acquireInitialRetryToken(r.partition_id),c=Error(),u=0,p=0,{request:f}=n,h=i.Kd.isInstance(f);for(h&&(f.headers[s.l5]=l());;)try{h&&(f.headers[s.ok]=`attempt=${u+1}; max=${a}`);let{response:r,output:i}=await t(n);return o.recordSuccess(e),i.$metadata.attempts=u+1,i.$metadata.totalRetryDelay=p,{response:r,output:i}}catch(s){let t=w(s);if(c=d(s),h&&g(f))throw(r.logger instanceof y.N4?console:r.logger)?.warn("An error was encountered in a non-retryable streaming request."),c;try{e=await o.refreshRetryTokenForRetry(e,t)}catch(e){throw c.$metadata||(c.$metadata={}),c.$metadata.attempts=u+1,c.$metadata.totalRetryDelay=p,c}u=e.getRetryCount();let n=e.getRetryDelay();p+=n,await new Promise(e=>setTimeout(e,n))}}},b=e=>void 0!==e.acquireInitialRetryToken&&void 0!==e.refreshRetryTokenForRetry&&void 0!==e.recordSuccess,w=e=>{let t={error:e,errorType:v(e)},r=P(e.$response);return r&&(t.retryAfterHint=r),t},v=e=>(0,o.Qb)(e)?"THROTTLING":(0,o.bV)(e)?"TRANSIENT":(0,o.GQ)(e)?"SERVER_ERROR":"CLIENT_ERROR",E={name:"retryMiddleware",tags:["RETRY"],step:"finalizeRequest",priority:"high",override:!0},S=e=>({applyToStack:t=>{t.add(x(e),E)}}),P=e=>{if(!i.cS.isInstance(e))return;let t=Object.keys(e.headers).find(e=>"retry-after"===e.toLowerCase());if(!t)return;let r=e.headers[t],n=Number(r);return new Date(Number.isNaN(n)?r:1e3*n)}},228:(e,t,r)=>{"use strict";r.d(t,{TM:()=>a,Ou:()=>o});let n=(e,t)=>(r,n)=>async s=>{let{response:i}=await r(s);try{let r=await t(i,e);return{response:i,output:r}}catch(e){if(Object.defineProperty(e,"$response",{value:i}),!("$metadata"in e)){let t="Deserialization error: to see the raw response, inspect the hidden field {error}.$response on this object.";try{e.message+="\n  "+t}catch(e){n.logger&&n.logger?.constructor?.name!=="NoOpLogger"?n.logger?.warn?.(t):console.warn(t)}void 0!==e.$responseBodyText&&e.$response&&(e.$response.body=e.$responseBodyText)}throw e}},s=(e,t)=>(r,n)=>async s=>{let i=n.endpointV2?.url&&e.urlParser?async()=>e.urlParser(n.endpointV2.url):e.endpoint;if(!i)throw Error("No valid endpoint provider available.");let o=await t(s.input,{...e,endpoint:i});return r({...s,request:o})},i={name:"deserializerMiddleware",step:"deserialize",tags:["DESERIALIZER"],override:!0},o={name:"serializerMiddleware",step:"serialize",tags:["SERIALIZER"],override:!0};function a(e,t,r){return{applyToStack:a=>{a.add(n(e,r),i),a.add(s(e,t),o)}}}},7609:(e,t,r)=>{"use strict";r.d(t,{Kd:()=>i,cS:()=>o,eS:()=>n,jt:()=>s});let n=e=>{let t=e.httpHandler;return{setHttpHandler(e){t=e},httpHandler:()=>t,updateHttpClientConfig(e,r){t.updateHttpClientConfig(e,r)},httpHandlerConfigs:()=>t.httpHandlerConfigs()}},s=e=>({httpHandler:e.httpHandler()});r(6100);class i{constructor(e){this.method=e.method||"GET",this.hostname=e.hostname||"localhost",this.port=e.port,this.query=e.query||{},this.headers=e.headers||{},this.body=e.body,this.protocol=e.protocol?":"!==e.protocol.slice(-1)?`${e.protocol}:`:e.protocol:"https:",this.path=e.path?"/"!==e.path.charAt(0)?`/${e.path}`:e.path:"/",this.username=e.username,this.password=e.password,this.fragment=e.fragment}static clone(e){var t;let r=new i({...e,headers:{...e.headers}});return r.query&&(r.query=Object.keys(t=r.query).reduce((e,r)=>{let n=t[r];return{...e,[r]:Array.isArray(n)?[...n]:n}},{})),r}static isInstance(e){return!!e&&"method"in e&&"protocol"in e&&"hostname"in e&&"path"in e&&"object"==typeof e.query&&"object"==typeof e.headers}clone(){return i.clone(this)}}class o{constructor(e){this.statusCode=e.statusCode,this.reason=e.reason,this.headers=e.headers||{},this.body=e.body}static isInstance(e){return!!e&&"number"==typeof e.statusCode&&"object"==typeof e.headers}}},1924:(e,t,r)=>{"use strict";r.d(t,{h5:()=>u,S0:()=>c,GQ:()=>f,Qb:()=>d,bV:()=>p});let n=["AuthFailure","InvalidSignatureException","RequestExpired","RequestInTheFuture","RequestTimeTooSkewed","SignatureDoesNotMatch"],s=["BandwidthLimitExceeded","EC2ThrottledException","LimitExceededException","PriorRequestNotComplete","ProvisionedThroughputExceededException","RequestLimitExceeded","RequestThrottled","RequestThrottledException","SlowDown","ThrottledException","Throttling","ThrottlingException","TooManyRequestsException","TransactionInProgressException"],i=["TimeoutError","RequestTimeout","RequestTimeoutException"],o=[500,502,503,504],a=["ECONNRESET","ECONNREFUSED","EPIPE","ETIMEDOUT"],c=e=>void 0!==e.$retryable,u=e=>n.includes(e.name),l=e=>e.$metadata?.clockSkewCorrected,d=e=>e.$metadata?.httpStatusCode===429||s.includes(e.name)||e.$retryable?.throttling==!0,p=(e,t=0)=>l(e)||i.includes(e.name)||a.includes(e?.code||"")||o.includes(e.$metadata?.httpStatusCode||0)||void 0!==e.cause&&t<=10&&p(e.cause,t+1),f=e=>{if(e.$metadata?.httpStatusCode!==void 0){let t=e.$metadata.httpStatusCode;if(500<=t&&t<=599&&!p(e))return!0}return!1}},8599:(e,t,r)=>{"use strict";r.d(t,{BB:()=>B});var n,s=r(8004),i=r(9848),o=r(2531),a=r(2637);let c=e=>"string"==typeof e?(0,a.a)(e):ArrayBuffer.isView(e)?new Uint8Array(e.buffer,e.byteOffset,e.byteLength/Uint8Array.BYTES_PER_ELEMENT):new Uint8Array(e),u="X-Amz-Date",l="X-Amz-Signature",d="X-Amz-Security-Token",p="authorization",f=u.toLowerCase(),h=[p,f,"date"],m=l.toLowerCase(),y="x-amz-content-sha256",g=d.toLowerCase(),x={authorization:!0,"cache-control":!0,connection:!0,expect:!0,from:!0,"keep-alive":!0,"max-forwards":!0,pragma:!0,referer:!0,te:!0,trailer:!0,"transfer-encoding":!0,upgrade:!0,"user-agent":!0,"x-amzn-trace-id":!0},b=/^proxy-/,w=/^sec-/,v="AWS4-HMAC-SHA256",E="aws4_request",S={},P=[],O=(e,t,r)=>`${e}/${t}/${r}/${E}`,$=async(e,t,r,n,i)=>{let o=await A(e,t.secretAccessKey,t.accessKeyId),a=`${r}:${n}:${i}:${(0,s.n)(o)}:${t.sessionToken}`;if(a in S)return S[a];for(P.push(a);P.length>50;)delete S[P.shift()];let c=`AWS4${t.secretAccessKey}`;for(let t of[r,n,i,E])c=await A(e,c,t);return S[a]=c},A=(e,t,r)=>{let n=new e(t);return n.update(c(r)),n.digest()},M=({headers:e},t,r)=>{let n={};for(let s of Object.keys(e).sort()){if(void 0==e[s])continue;let i=s.toLowerCase();(!(i in x||t?.has(i)||b.test(i)||w.test(i))||r&&(!r||r.has(i)))&&(n[i]=e[s].trim().replace(/\s+/g," "))}return n},I=({query:e={}})=>{let t=[],r={};for(let n of Object.keys(e)){if(n.toLowerCase()===m)continue;let s=(0,o.o)(n);t.push(s);let i=e[n];"string"==typeof i?r[s]=`${s}=${(0,o.o)(i)}`:Array.isArray(i)&&(r[s]=i.slice(0).reduce((e,t)=>e.concat([`${s}=${(0,o.o)(t)}`]),[]).sort().join("&"))}return t.sort().map(e=>r[e]).filter(e=>e).join("&")},C=e=>"function"==typeof ArrayBuffer&&e instanceof ArrayBuffer||"[object ArrayBuffer]"===Object.prototype.toString.call(e),k=async({headers:e,body:t},r)=>{for(let t of Object.keys(e))if(t.toLowerCase()===y)return e[t];if(void 0==t)return"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855";if("string"==typeof t||ArrayBuffer.isView(t)||C(t)){let e=new r;return e.update(c(t)),(0,s.n)(await e.digest())}return"UNSIGNED-PAYLOAD"};class T{format(e){let t=[];for(let r of Object.keys(e)){let n=(0,a.a)(r);t.push(Uint8Array.from([n.byteLength]),n,this.formatHeaderValue(e[r]))}let r=new Uint8Array(t.reduce((e,t)=>e+t.byteLength,0)),n=0;for(let e of t)r.set(e,n),n+=e.byteLength;return r}formatHeaderValue(e){switch(e.type){case"boolean":return Uint8Array.from([e.value?0:1]);case"byte":return Uint8Array.from([2,e.value]);case"short":let t=new DataView(new ArrayBuffer(3));return t.setUint8(0,3),t.setInt16(1,e.value,!1),new Uint8Array(t.buffer);case"integer":let r=new DataView(new ArrayBuffer(5));return r.setUint8(0,4),r.setInt32(1,e.value,!1),new Uint8Array(r.buffer);case"long":let n=new Uint8Array(9);return n[0]=5,n.set(e.value.bytes,1),n;case"binary":let i=new DataView(new ArrayBuffer(3+e.value.byteLength));i.setUint8(0,6),i.setUint16(1,e.value.byteLength,!1);let o=new Uint8Array(i.buffer);return o.set(e.value,3),o;case"string":let c=(0,a.a)(e.value),u=new DataView(new ArrayBuffer(3+c.byteLength));u.setUint8(0,7),u.setUint16(1,c.byteLength,!1);let l=new Uint8Array(u.buffer);return l.set(c,3),l;case"timestamp":let d=new Uint8Array(9);return d[0]=8,d.set(N.fromNumber(e.value.valueOf()).bytes,1),d;case"uuid":if(!R.test(e.value))throw Error(`Invalid UUID received: ${e.value}`);let p=new Uint8Array(17);return p[0]=9,p.set((0,s.a)(e.value.replace(/\-/g,"")),1),p}}}!function(e){e[e.boolTrue=0]="boolTrue",e[e.boolFalse=1]="boolFalse",e[e.byte=2]="byte",e[e.short=3]="short",e[e.integer=4]="integer",e[e.long=5]="long",e[e.byteArray=6]="byteArray",e[e.string=7]="string",e[e.timestamp=8]="timestamp",e[e.uuid=9]="uuid"}(n||(n={}));let R=/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/;class N{constructor(e){if(this.bytes=e,8!==e.byteLength)throw Error("Int64 buffers must be exactly 8 bytes")}static fromNumber(e){if(e>0x8000000000000000||e<-0x8000000000000000)throw Error(`${e} is too large (or, if negative, too small) to represent as an Int64`);let t=new Uint8Array(8);for(let r=7,n=Math.abs(Math.round(e));r>-1&&n>0;r--,n/=256)t[r]=n;return e<0&&D(t),new N(t)}valueOf(){let e=this.bytes.slice(0),t=128&e[0];return t&&D(e),parseInt((0,s.n)(e),16)*(t?-1:1)}toString(){return String(this.valueOf())}}function D(e){for(let t=0;t<8;t++)e[t]^=255;for(let t=7;t>-1&&(e[t]++,0===e[t]);t--);}let U=(e,t)=>{for(let r of(e=e.toLowerCase(),Object.keys(t)))if(e===r.toLowerCase())return!0;return!1};var j=r(7609);let F=(e,t={})=>{let{headers:r,query:n={}}=j.Kd.clone(e);for(let e of Object.keys(r)){let s=e.toLowerCase();("x-amz-"===s.slice(0,6)&&!t.unhoistableHeaders?.has(s)||t.hoistableHeaders?.has(s))&&(n[e]=r[e],delete r[e])}return{...e,headers:r,query:n}},_=e=>{for(let t of Object.keys((e=j.Kd.clone(e)).headers))h.indexOf(t.toLowerCase())>-1&&delete e.headers[t];return e},L=e=>z(e).toISOString().replace(/\.\d{3}Z$/,"Z"),z=e=>"number"==typeof e?new Date(1e3*e):"string"==typeof e?new Date(Number(e)?1e3*Number(e):e):e;class B{constructor({applyChecksum:e,credentials:t,region:r,service:n,sha256:s,uriEscapePath:o=!0}){this.headerFormatter=new T,this.service=n,this.sha256=s,this.uriEscapePath=o,this.applyChecksum="boolean"!=typeof e||e,this.regionProvider=(0,i.t)(r),this.credentialProvider=(0,i.t)(t)}async presign(e,t={}){let{signingDate:r=new Date,expiresIn:n=3600,unsignableHeaders:s,unhoistableHeaders:i,signableHeaders:o,hoistableHeaders:a,signingRegion:c,signingService:p}=t,f=await this.credentialProvider();this.validateResolvedCredentials(f);let h=c??await this.regionProvider(),{longDate:m,shortDate:y}=K(r);if(n>604800)return Promise.reject("Signature version 4 presigned URLs must have an expiration date less than one week in the future");let g=O(y,h,p??this.service),x=F(_(e),{unhoistableHeaders:i,hoistableHeaders:a});f.sessionToken&&(x.query[d]=f.sessionToken),x.query["X-Amz-Algorithm"]=v,x.query["X-Amz-Credential"]=`${f.accessKeyId}/${g}`,x.query[u]=m,x.query["X-Amz-Expires"]=n.toString(10);let b=M(x,s,o);return x.query["X-Amz-SignedHeaders"]=V(b),x.query[l]=await this.getSignature(m,g,this.getSigningKey(f,h,y,p),this.createCanonicalRequest(x,b,await k(e,this.sha256))),x}async sign(e,t){return"string"==typeof e?this.signString(e,t):e.headers&&e.payload?this.signEvent(e,t):e.message?this.signMessage(e,t):this.signRequest(e,t)}async signEvent({headers:e,payload:t},{signingDate:r=new Date,priorSignature:n,signingRegion:i,signingService:o}){let a=i??await this.regionProvider(),{shortDate:c,longDate:u}=K(r),l=O(c,a,o??this.service),d=await k({headers:{},body:t},this.sha256),p=new this.sha256;p.update(e);let f=["AWS4-HMAC-SHA256-PAYLOAD",u,l,n,(0,s.n)(await p.digest()),d].join("\n");return this.signString(f,{signingDate:r,signingRegion:a,signingService:o})}async signMessage(e,{signingDate:t=new Date,signingRegion:r,signingService:n}){return this.signEvent({headers:this.headerFormatter.format(e.message.headers),payload:e.message.body},{signingDate:t,signingRegion:r,signingService:n,priorSignature:e.priorSignature}).then(t=>({message:e.message,signature:t}))}async signString(e,{signingDate:t=new Date,signingRegion:r,signingService:n}={}){let i=await this.credentialProvider();this.validateResolvedCredentials(i);let o=r??await this.regionProvider(),{shortDate:a}=K(t),u=new this.sha256(await this.getSigningKey(i,o,a,n));return u.update(c(e)),(0,s.n)(await u.digest())}async signRequest(e,{signingDate:t=new Date,signableHeaders:r,unsignableHeaders:n,signingRegion:s,signingService:i}={}){let o=await this.credentialProvider();this.validateResolvedCredentials(o);let a=s??await this.regionProvider(),c=_(e),{longDate:u,shortDate:l}=K(t),d=O(l,a,i??this.service);c.headers[f]=u,o.sessionToken&&(c.headers[g]=o.sessionToken);let h=await k(c,this.sha256);!U(y,c.headers)&&this.applyChecksum&&(c.headers[y]=h);let m=M(c,n,r),x=await this.getSignature(u,d,this.getSigningKey(o,a,l,i),this.createCanonicalRequest(c,m,h));return c.headers[p]=`${v} Credential=${o.accessKeyId}/${d}, SignedHeaders=${V(m)}, Signature=${x}`,c}createCanonicalRequest(e,t,r){let n=Object.keys(t).sort();return`${e.method}
${this.getCanonicalPath(e)}
${I(e)}
${n.map(e=>`${e}:${t[e]}`).join("\n")}

${n.join(";")}
${r}`}async createStringToSign(e,t,r){let n=new this.sha256;n.update(c(r));let i=await n.digest();return`${v}
${e}
${t}
${(0,s.n)(i)}`}getCanonicalPath({path:e}){if(this.uriEscapePath){let t=[];for(let r of e.split("/"))r?.length!==0&&"."!==r&&(".."===r?t.pop():t.push(r));let r=`${e?.startsWith("/")?"/":""}${t.join("/")}${t.length>0&&e?.endsWith("/")?"/":""}`;return(0,o.o)(r).replace(/%2F/g,"/")}return e}async getSignature(e,t,r,n){let i=await this.createStringToSign(e,t,n),o=new this.sha256(await r);return o.update(c(i)),(0,s.n)(await o.digest())}getSigningKey(e,t,r,n){return $(this.sha256,e,r,t,n||this.service)}validateResolvedCredentials(e){if("object"!=typeof e||"string"!=typeof e.accessKeyId||"string"!=typeof e.secretAccessKey)throw Error("Resolved credential object is not valid")}}let K=e=>{let t=L(e).replace(/[\-:]/g,"");return{longDate:t,shortDate:t.slice(0,8)}},V=e=>Object.keys(e).sort().join(";")},5268:(e,t,r)=>{"use strict";r.d(t,{Kj:()=>c,uB:()=>d,N4:()=>ef,$H:()=>f,TJ:()=>Z,Ss:()=>ew,Px:()=>u.Px,Mw:()=>Q,ak:()=>m,ET:()=>w,Y0:()=>O,r$:()=>y,Xk:()=>$,lK:()=>A,$6:()=>u.$6,Yd:()=>el,xA:()=>ec,rm:()=>ed,JW:()=>T,lT:()=>en,Tj:()=>eh,yG:()=>h,l3:()=>K,t_:()=>B,uv:()=>eu,xW:()=>D,V0:()=>N,s:()=>em,jr:()=>et});let n=(e,t)=>{let r=[];if(e&&r.push(e),t)for(let e of t)r.push(e);return r},s=(e,t)=>`${e||"anonymous"}${t&&t.length>0?` (a.k.a. ${t.join(",")})`:""}`,i=()=>{let e=[],t=[],r=!1,c=new Set,u=e=>e.sort((e,t)=>o[t.step]-o[e.step]||a[t.priority||"normal"]-a[e.priority||"normal"]),l=r=>{let s=!1,i=e=>{let t=n(e.name,e.aliases);if(t.includes(r)){for(let e of(s=!0,t))c.delete(e);return!1}return!0};return e=e.filter(i),t=t.filter(i),s},d=r=>{let s=!1,i=e=>{if(e.middleware===r){for(let t of(s=!0,n(e.name,e.aliases)))c.delete(t);return!1}return!0};return e=e.filter(i),t=t.filter(i),s},p=r=>(e.forEach(e=>{r.add(e.middleware,{...e})}),t.forEach(e=>{r.addRelativeTo(e.middleware,{...e})}),r.identifyOnResolve?.(m.identifyOnResolve()),r),f=e=>{let t=[];return e.before.forEach(e=>{0===e.before.length&&0===e.after.length?t.push(e):t.push(...f(e))}),t.push(e),e.after.reverse().forEach(e=>{0===e.before.length&&0===e.after.length?t.push(e):t.push(...f(e))}),t},h=(r=!1)=>{let i=[],o=[],a={};return e.forEach(e=>{let t={...e,before:[],after:[]};for(let e of n(t.name,t.aliases))a[e]=t;i.push(t)}),t.forEach(e=>{let t={...e,before:[],after:[]};for(let e of n(t.name,t.aliases))a[e]=t;o.push(t)}),o.forEach(e=>{if(e.toMiddleware){let t=a[e.toMiddleware];if(void 0===t){if(r)return;throw Error(`${e.toMiddleware} is not found when adding ${s(e.name,e.aliases)} middleware ${e.relation} ${e.toMiddleware}`)}"after"===e.relation&&t.after.push(e),"before"===e.relation&&t.before.push(e)}}),u(i).map(f).reduce((e,t)=>(e.push(...t),e),[])},m={add:(t,r={})=>{let{name:i,override:o,aliases:a}=r,u={step:"initialize",priority:"normal",middleware:t,...r},l=n(i,a);if(l.length>0){if(l.some(e=>c.has(e))){if(!o)throw Error(`Duplicate middleware name '${s(i,a)}'`);for(let t of l){let r=e.findIndex(e=>e.name===t||e.aliases?.some(e=>e===t));if(-1===r)continue;let n=e[r];if(n.step!==u.step||u.priority!==n.priority)throw Error(`"${s(n.name,n.aliases)}" middleware with ${n.priority} priority in ${n.step} step cannot be overridden by "${s(i,a)}" middleware with ${u.priority} priority in ${u.step} step.`);e.splice(r,1)}}for(let e of l)c.add(e)}e.push(u)},addRelativeTo:(e,r)=>{let{name:i,override:o,aliases:a}=r,u={middleware:e,...r},l=n(i,a);if(l.length>0){if(l.some(e=>c.has(e))){if(!o)throw Error(`Duplicate middleware name '${s(i,a)}'`);for(let e of l){let r=t.findIndex(t=>t.name===e||t.aliases?.some(t=>t===e));if(-1===r)continue;let n=t[r];if(n.toMiddleware!==u.toMiddleware||n.relation!==u.relation)throw Error(`"${s(n.name,n.aliases)}" middleware ${n.relation} "${n.toMiddleware}" middleware cannot be overridden by "${s(i,a)}" middleware ${u.relation} "${u.toMiddleware}" middleware.`);t.splice(r,1)}}for(let e of l)c.add(e)}t.push(u)},clone:()=>p(i()),use:e=>{e.applyToStack(m)},remove:e=>"string"==typeof e?l(e):d(e),removeByTag:r=>{let s=!1,i=e=>{let{tags:t,name:i,aliases:o}=e;if(t&&t.includes(r)){for(let e of n(i,o))c.delete(e);return s=!0,!1}return!0};return e=e.filter(i),t=t.filter(i),s},concat:e=>{let t=p(i());return t.use(e),t.identifyOnResolve(r||t.identifyOnResolve()||(e.identifyOnResolve?.()??!1)),t},applyToStack:p,identify:()=>h(!0).map(e=>{let t=e.step??e.relation+" "+e.toMiddleware;return s(e.name,e.aliases)+" - "+t}),identifyOnResolve:e=>("boolean"==typeof e&&(r=e),r),resolve:(e,t)=>{for(let r of h().map(e=>e.middleware).reverse())e=r(e,t);return r&&console.log(m.identify()),e}};return m},o={initialize:5,serialize:4,build:3,finalizeRequest:2,deserialize:1},a={high:3,normal:2,low:1};class c{constructor(e){this.config=e,this.middlewareStack=i()}send(e,t,r){let n;let s="function"!=typeof t?t:void 0,i="function"==typeof t?t:r;if(void 0===s&&!0===this.config.cacheMiddleware){this.handlers||(this.handlers=new WeakMap);let t=this.handlers;t.has(e.constructor)?n=t.get(e.constructor):(n=e.resolveMiddleware(this.middlewareStack,this.config,s),t.set(e.constructor,n))}else delete this.handlers,n=e.resolveMiddleware(this.middlewareStack,this.config,s);if(!i)return n(e).then(e=>e.output);n(e).then(e=>i(null,e.output),e=>i(e)).catch(()=>{})}destroy(){this.config?.requestHandler?.destroy?.(),delete this.handlers}}var u=r(2e3),l=r(6100);class d{constructor(){this.middlewareStack=i()}static classBuilder(){return new p}resolveMiddlewareWithContext(e,t,r,{middlewareFn:n,clientName:s,commandName:i,inputFilterSensitiveLog:o,outputFilterSensitiveLog:a,smithyContext:c,additionalContext:u,CommandCtor:d}){for(let s of n.bind(this)(d,e,t,r))this.middlewareStack.use(s);let p=e.concat(this.middlewareStack),{logger:f}=t,h={logger:f,clientName:s,commandName:i,inputFilterSensitiveLog:o,outputFilterSensitiveLog:a,[l.Vf]:{commandInstance:this,...c},...u},{requestHandler:m}=t;return p.resolve(e=>m.handle(e.request,r||{}),h)}}class p{constructor(){this._init=()=>{},this._ep={},this._middlewareFn=()=>[],this._commandName="",this._clientName="",this._additionalContext={},this._smithyContext={},this._inputFilterSensitiveLog=e=>e,this._outputFilterSensitiveLog=e=>e,this._serializer=null,this._deserializer=null}init(e){this._init=e}ep(e){return this._ep=e,this}m(e){return this._middlewareFn=e,this}s(e,t,r={}){return this._smithyContext={service:e,operation:t,...r},this}c(e={}){return this._additionalContext=e,this}n(e,t){return this._clientName=e,this._commandName=t,this}f(e=e=>e,t=e=>e){return this._inputFilterSensitiveLog=e,this._outputFilterSensitiveLog=t,this}ser(e){return this._serializer=e,this}de(e){return this._deserializer=e,this}build(){let e;let t=this;return e=class extends d{static getEndpointParameterInstructions(){return t._ep}constructor(...[e]){super(),this.serialize=t._serializer,this.deserialize=t._deserializer,this.input=e??{},t._init(this)}resolveMiddleware(r,n,s){return this.resolveMiddlewareWithContext(r,n,s,{CommandCtor:e,middlewareFn:t._middlewareFn,clientName:t._clientName,commandName:t._commandName,inputFilterSensitiveLog:t._inputFilterSensitiveLog,outputFilterSensitiveLog:t._outputFilterSensitiveLog,smithyContext:t._smithyContext,additionalContext:t._additionalContext})}}}}let f="***SensitiveInformation***",h=e=>{switch(e){case"true":return!0;case"false":return!1;default:throw Error(`Unable to parse boolean value "${e}"`)}},m=e=>{if(null!=e){if("number"==typeof e){if((0===e||1===e)&&_.warn(F(`Expected boolean, got ${typeof e}: ${e}`)),0===e)return!1;if(1===e)return!0}if("string"==typeof e){let t=e.toLowerCase();if(("false"===t||"true"===t)&&_.warn(F(`Expected boolean, got ${typeof e}: ${e}`)),"false"===t)return!1;if("true"===t)return!0}if("boolean"==typeof e)return e;throw TypeError(`Expected boolean, got ${typeof e}: ${e}`)}},y=e=>{if(null!=e){if("string"==typeof e){let t=parseFloat(e);if(!Number.isNaN(t))return String(t)!==String(e)&&_.warn(F(`Expected number but observed string: ${e}`)),t}if("number"==typeof e)return e;throw TypeError(`Expected number, got ${typeof e}: ${e}`)}},g=Math.ceil(17014118346046923e22*(2-11920928955078125e-23)),x=e=>{let t=y(e);if(void 0!==t&&!Number.isNaN(t)&&t!==1/0&&t!==-1/0&&Math.abs(t)>g)throw TypeError(`Expected 32-bit float, got ${e}`);return t},b=e=>{if(null!=e){if(Number.isInteger(e)&&!Number.isNaN(e))return e;throw TypeError(`Expected integer, got ${typeof e}: ${e}`)}},w=e=>S(e,32),v=e=>S(e,16),E=e=>S(e,8),S=(e,t)=>{let r=b(e);if(void 0!==r&&P(r,t)!==r)throw TypeError(`Expected ${t}-bit integer, got ${e}`);return r},P=(e,t)=>{switch(t){case 32:return Int32Array.of(e)[0];case 16:return Int16Array.of(e)[0];case 8:return Int8Array.of(e)[0]}},O=(e,t)=>{if(null==e){if(t)throw TypeError(`Expected a non-null value for ${t}`);throw TypeError("Expected a non-null value")}return e},$=e=>{if(null==e)return;if("object"==typeof e&&!Array.isArray(e))return e;let t=Array.isArray(e)?"array":typeof e;throw TypeError(`Expected object, got ${t}: ${e}`)},A=e=>{if(null!=e){if("string"==typeof e)return e;if(["boolean","number","bigint"].includes(typeof e))return _.warn(F(`Expected string, got ${typeof e}: ${e}`)),String(e);throw TypeError(`Expected string, got ${typeof e}: ${e}`)}},M=e=>"string"==typeof e?y(k(e)):y(e),I=e=>"string"==typeof e?x(k(e)):x(e),C=/(-?(?:0|[1-9]\d*)(?:\.\d+)?(?:[eE][+-]?\d+)?)|(-?Infinity)|(NaN)/g,k=e=>{let t=e.match(C);if(null===t||t[0].length!==e.length)throw TypeError("Expected real number, got implicit NaN");return parseFloat(e)},T=e=>"string"==typeof e?R(e):y(e),R=e=>{switch(e){case"NaN":return NaN;case"Infinity":return 1/0;case"-Infinity":return-1/0;default:throw Error(`Unable to parse float value: ${e}`)}},N=e=>"string"==typeof e?b(k(e)):b(e),D=e=>"string"==typeof e?w(k(e)):w(e),U=e=>"string"==typeof e?v(k(e)):v(e),j=e=>"string"==typeof e?E(k(e)):E(e),F=e=>String(TypeError(e).stack||e).split("\n").slice(0,5).filter(e=>!e.includes("stackTraceWarning")).join("\n"),_={warn:console.warn},L=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],z=new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?(([-+]\d{2}\:\d{2})|[zZ])$/),B=e=>{if(null==e)return;if("string"!=typeof e)throw TypeError("RFC-3339 date-times must be expressed as strings");let t=z.exec(e);if(!t)throw TypeError("Invalid RFC-3339 date-time value");let[r,n,s,i,o,a,c,u,l]=t,d=V(U(J(n)),W(s,"month",1,12),W(i,"day",1,31),{hours:o,minutes:a,seconds:c,fractionalMilliseconds:u});return"Z"!=l.toUpperCase()&&d.setTime(d.getTime()-Y(l)),d},K=e=>{let t;if(null!=e){if("number"==typeof e)t=e;else if("string"==typeof e)t=M(e);else if("object"==typeof e&&1===e.tag)t=e.value;else throw TypeError("Epoch timestamps must be expressed as floating point numbers or their string representation");if(Number.isNaN(t)||t===1/0||t===-1/0)throw TypeError("Epoch timestamps must be valid, non-Infinite, non-NaN numerics");return new Date(Math.round(1e3*t))}},V=(e,t,r,n)=>{let s=t-1;return q(e,s,r),new Date(Date.UTC(e,s,r,W(n.hours,"hour",0,23),W(n.minutes,"minute",0,59),W(n.seconds,"seconds",0,60),X(n.fractionalMilliseconds)))},H=[31,28,31,30,31,30,31,31,30,31,30,31],q=(e,t,r)=>{let n=H[t];if(1===t&&G(e)&&(n=29),r>n)throw TypeError(`Invalid day for ${L[t]} in ${e}: ${r}`)},G=e=>e%4==0&&(e%100!=0||e%400==0),W=(e,t,r,n)=>{let s=j(J(e));if(s<r||s>n)throw TypeError(`${t} must be between ${r} and ${n}, inclusive`);return s},X=e=>null==e?0:1e3*I("0."+e),Y=e=>{let t=e[0],r=1;if("+"==t)r=1;else if("-"==t)r=-1;else throw TypeError(`Offset direction, ${t}, must be "+" or "-"`);return r*(60*Number(e.substring(1,3))+Number(e.substring(4,6)))*6e4},J=e=>{let t=0;for(;t<e.length-1&&"0"===e.charAt(t);)t++;return 0===t?e:e.slice(t)};class Z extends Error{constructor(e){super(e.message),Object.setPrototypeOf(this,Object.getPrototypeOf(this).constructor.prototype),this.name=e.name,this.$fault=e.$fault,this.$metadata=e.$metadata}static isInstance(e){return!!e&&(Z.prototype.isPrototypeOf(e)||!!e.$fault&&!!e.$metadata&&("client"===e.$fault||"server"===e.$fault))}static[Symbol.hasInstance](e){return!!e&&(this===Z?Z.isInstance(e):!!Z.isInstance(e)&&(e.name&&this.name?this.prototype.isPrototypeOf(e)||e.name===this.name:this.prototype.isPrototypeOf(e)))}}let Q=(e,t={})=>{Object.entries(t).filter(([,e])=>void 0!==e).forEach(([t,r])=>{(void 0==e[t]||""===e[t])&&(e[t]=r)});let r=e.message||e.Message||"UnknownError";return e.message=r,delete e.Message,e},ee=({output:e,parsedBody:t,exceptionCtor:r,errorCode:n})=>{let s=er(e),i=s.httpStatusCode?s.httpStatusCode+"":void 0;throw Q(new r({name:t?.code||t?.Code||n||i||"UnknownError",$fault:"client",$metadata:s}),t)},et=e=>({output:t,parsedBody:r,errorCode:n})=>{ee({output:t,parsedBody:r,exceptionCtor:e,errorCode:n})},er=e=>({httpStatusCode:e.statusCode,requestId:e.headers["x-amzn-requestid"]??e.headers["x-amzn-request-id"]??e.headers["x-amz-request-id"],extendedRequestId:e.headers["x-amz-id-2"],cfId:e.headers["x-amz-cf-id"]}),en=e=>{switch(e){case"standard":case"cross-region":return{retryMode:"standard",connectionTimeout:3100};case"in-region":return{retryMode:"standard",connectionTimeout:1100};case"mobile":return{retryMode:"standard",connectionTimeout:3e4};default:return{}}},es=e=>{let t=[];for(let r in l.dB){let n=l.dB[r];void 0!==e[n]&&t.push({algorithmId:()=>n,checksumConstructor:()=>e[n]})}return{_checksumAlgorithms:t,addChecksumAlgorithm(e){this._checksumAlgorithms.push(e)},checksumAlgorithms(){return this._checksumAlgorithms}}},ei=e=>{let t={};return e.checksumAlgorithms().forEach(e=>{t[e.algorithmId()]=e.checksumConstructor()}),t},eo=e=>{let t=e.retryStrategy;return{setRetryStrategy(e){t=e},retryStrategy:()=>t}},ea=e=>{let t={};return t.retryStrategy=e.retryStrategy(),t},ec=e=>({...es(e),...eo(e)}),eu=e=>({...ei(e),...ea(e)}),el=e=>Array.isArray(e)?e:[e],ed=e=>{let t="#text";for(let r in e)e.hasOwnProperty(r)&&void 0!==e[r][t]?e[r]=e[r][t]:"object"==typeof e[r]&&null!==e[r]&&(e[r]=ed(e[r]));return e},ep=function(e){return Object.assign(new String(e),{deserializeJSON:()=>JSON.parse(String(e)),toString:()=>String(e),toJSON:()=>String(e)})};ep.from=e=>e&&"object"==typeof e&&(e instanceof ep||"deserializeJSON"in e)?e:"string"==typeof e||Object.getPrototypeOf(e)===String.prototype?ep(String(e)):ep(JSON.stringify(e)),ep.fromObject=ep.from;class ef{trace(){}debug(){}info(){}warn(){}error(){}}function eh(e,t,r){let n,s;if(void 0===t&&void 0===r)n={},s=e;else{if(n=e,"function"==typeof t)return ey(n,t,s=r);s=t}for(let e of Object.keys(s)){if(!Array.isArray(s[e])){n[e]=s[e];continue}eg(n,null,s,e)}return n}let em=(e,t)=>{let r={};for(let n in t)eg(r,e,t,n);return r},ey=(e,t,r)=>eh(e,Object.entries(r).reduce((e,[r,n])=>(Array.isArray(n)?e[r]=n:"function"==typeof n?e[r]=[t,n()]:e[r]=[t,n],e),{})),eg=(e,t,r,n)=>{if(null!==t){let s=r[n];"function"==typeof s&&(s=[,s]);let[i=ex,o=eb,a=n]=s;("function"==typeof i&&i(t[a])||"function"!=typeof i&&i)&&(e[n]=o(t[a]));return}let[s,i]=r[n];if("function"==typeof i){let t;let r=void 0===s&&null!=(t=i()),o="function"==typeof s&&!!s(void 0)||"function"!=typeof s&&!!s;r?e[n]=t:o&&(e[n]=i())}else{let t=void 0===s&&null!=i,r="function"==typeof s&&!!s(i)||"function"!=typeof s&&!!s;(t||r)&&(e[n]=i)}},ex=e=>null!=e,eb=e=>e,ew=e=>{if(null==e)return{};if(Array.isArray(e))return e.filter(e=>null!=e).map(ew);if("object"==typeof e){let t={};for(let r of Object.keys(e))null!=e[r]&&(t[r]=ew(e[r]));return t}return e}},6100:(e,t,r)=>{"use strict";var n,s,i,o,a,c,u;r.d(t,{dB:()=>o,Ue:()=>i,Vf:()=>l}),function(e){e.HEADER="header",e.QUERY="query"}(n||(n={})),function(e){e.HEADER="header",e.QUERY="query"}(s||(s={})),function(e){e.HTTP="http",e.HTTPS="https"}(i||(i={})),function(e){e.MD5="md5",e.CRC32="crc32",e.CRC32C="crc32c",e.SHA1="sha1",e.SHA256="sha256"}(o||(o={})),function(e){e[e.HEADER=0]="HEADER",e[e.TRAILER=1]="TRAILER"}(a||(a={}));let l="__smithy_context";!function(e){e.PROFILE="profile",e.SSO_SESSION="sso-session",e.SERVICES="services"}(c||(c={})),function(e){e.HTTP_0_9="http/0.9",e.HTTP_1_0="http/1.0",e.TDS_8_0="tds/8.0"}(u||(u={}))},4262:(e,t,r)=>{"use strict";r.d(t,{D:()=>n});let n=e=>{let t;if("string"==typeof e)return n(new URL(e));let{hostname:r,pathname:s,port:i,protocol:o,search:a}=e;return a&&(t=function(e){let t={};if(e=e.replace(/^\?/,""))for(let r of e.split("&")){let[e,n=null]=r.split("=");e=decodeURIComponent(e),n&&(n=decodeURIComponent(n)),e in t?Array.isArray(t[e])?t[e].push(n):t[e]=[t[e],n]:t[e]=n}return t}(a)),{hostname:r,port:i?parseInt(i):void 0,protocol:o,path:s,query:t}}},9764:(e,t,r)=>{"use strict";r.d(t,{E:()=>i,n:()=>a});let n={},s=Array(64);for(let e=0;e+65<=90;e++){let t=String.fromCharCode(e+65);n[t]=e,s[e]=t}for(let e=0;e+97<=122;e++){let t=String.fromCharCode(e+97),r=e+26;n[t]=r,s[r]=t}for(let e=0;e<10;e++){n[e.toString(10)]=e+52;let t=e.toString(10),r=e+52;n[t]=r,s[r]=t}n["+"]=62,s[62]="+",n["/"]=63,s[63]="/";let i=e=>{let t=e.length/4*3;"=="===e.slice(-2)?t-=2:"="===e.slice(-1)&&t--;let r=new ArrayBuffer(t),s=new DataView(r);for(let t=0;t<e.length;t+=4){let r=0,i=0;for(let s=t,o=t+3;s<=o;s++)if("="!==e[s]){if(!(e[s]in n))throw TypeError(`Invalid character ${e[s]} in base64 string.`);r|=n[e[s]]<<(o-s)*6,i+=6}else r>>=6;let o=t/4*3;r>>=i%8;let a=Math.floor(i/8);for(let e=0;e<a;e++){let t=(a-e-1)*8;s.setUint8(o+e,(r&255<<t)>>t)}}return new Uint8Array(r)};var o=r(2637);function a(e){let t;let r="object"==typeof(t="string"==typeof e?(0,o.a)(e):e)&&"number"==typeof t.length,n="object"==typeof t&&"number"==typeof t.byteOffset&&"number"==typeof t.byteLength;if(!r&&!n)throw Error("@smithy/util-base64: toBase64 encoder function only accepts string | Uint8Array.");let i="";for(let e=0;e<t.length;e+=3){let r=0,n=0;for(let s=e,i=Math.min(e+3,t.length);s<i;s++)r|=t[s]<<(i-s-1)*8,n+=8;let o=Math.ceil(n/6);r<<=6*o-n;for(let e=1;e<=o;e++){let t=(o-e)*6;i+=s[(r&63<<t)>>t]}i+="==".slice(0,4-o)}return i}},6850:(e,t,r)=>{"use strict";r.d(t,{n:()=>s});let n="function"==typeof TextEncoder?new TextEncoder:null,s=e=>{if("string"==typeof e){if(n)return n.encode(e).byteLength;let t=e.length;for(let r=t-1;r>=0;r--){let n=e.charCodeAt(r);n>127&&n<=2047?t++:n>2047&&n<=65535&&(t+=2),n>=56320&&n<=57343&&r--}return t}if("number"==typeof e.byteLength)return e.byteLength;if("number"==typeof e.size)return e.size;throw Error(`Body Length computation failed for ${e}`)}},1996:(e,t,r)=>{"use strict";r.d(t,{I:()=>a});let n=(e,t,r)=>{let n,s,i;let o=!1,a=async()=>{s||(s=e());try{n=await s,i=!0,o=!1}finally{s=void 0}return n};return void 0===t?async e=>((!i||e?.forceRefresh)&&(n=await a()),n):async e=>((!i||e?.forceRefresh)&&(n=await a()),o||(r&&!r(n)?o=!0:t(n)&&await a()),n)};var s=r(6880),i=r.n(s);let o=["in-region","cross-region","mobile","standard","legacy"],a=({defaultsMode:e}={})=>n(async()=>{let t="function"==typeof e?await e():e;switch(t?.toLowerCase()){case"auto":return Promise.resolve(c()?"mobile":"standard");case"mobile":case"in-region":case"cross-region":case"standard":case"legacy":return Promise.resolve(t?.toLocaleLowerCase());case void 0:return Promise.resolve("legacy");default:throw Error(`Invalid parameter for "defaultsMode", expect ${o.join(", ")}, got ${t}`)}}),c=()=>{let e="undefined"!=typeof window&&window?.navigator?.userAgent?i().parse(window.navigator.userAgent):void 0,t=e?.platform?.type;return"tablet"===t||"mobile"===t}},8636:(e,t,r)=>{"use strict";r.d(t,{kS:()=>n,mw:()=>c,oX:()=>i,X8:()=>a,sO:()=>k});class n{constructor({size:e,params:t}){this.data=new Map,this.parameters=[],this.capacity=e??50,t&&(this.parameters=t)}get(e,t){let r=this.hash(e);if(!1===r)return t();if(!this.data.has(r)){if(this.data.size>this.capacity+10){let e=this.data.keys(),t=0;for(;;){let{value:r,done:n}=e.next();if(this.data.delete(r),n||++t>10)break}}this.data.set(r,t())}return this.data.get(r)}size(){return this.data.size}hash(e){let t="",{parameters:r}=this;if(0===r.length)return!1;for(let n of r){let r=String(e[n]??"");if(r.includes("|;"))return!1;t+=r+"|;"}return t}}let s=RegExp("^(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}$"),i=e=>s.test(e)||e.startsWith("[")&&e.endsWith("]"),o=RegExp("^(?!.*-$)(?!-)[a-zA-Z0-9-]{1,63}$"),a=(e,t=!1)=>{if(!t)return o.test(e);for(let t of e.split("."))if(!a(t))return!1;return!0},c={},u="endpoints";function l(e){return"object"!=typeof e||null==e?e:"ref"in e?`$${l(e.ref)}`:"fn"in e?`${e.fn}(${(e.argv||[]).map(l).join(", ")})`:JSON.stringify(e,null,2)}class d extends Error{constructor(e){super(e),this.name="EndpointError"}}let p=e=>{let t=e.split("."),r=[];for(let n of t){let t=n.indexOf("[");if(-1!==t){if(n.indexOf("]")!==n.length-1)throw new d(`Path: '${e}' does not end with ']'`);let s=n.slice(t+1,-1);if(Number.isNaN(parseInt(s)))throw new d(`Invalid array index: '${s}' in path: '${e}'`);0!==t&&r.push(n.slice(0,t)),r.push(s)}else r.push(n)}return r},f=(e,t)=>p(t).reduce((r,n)=>{if("object"!=typeof r)throw new d(`Index '${n}' in '${t}' not found in '${JSON.stringify(e)}'`);return Array.isArray(r)?r[parseInt(n)]:r[n]},e);var h=r(6100);let m={[h.Ue.HTTP]:80,[h.Ue.HTTPS]:443},y={booleanEquals:(e,t)=>e===t,getAttr:f,isSet:e=>null!=e,isValidHostLabel:a,not:e=>!e,parseURL:e=>{let t=(()=>{try{if(e instanceof URL)return e;if("object"==typeof e&&"hostname"in e){let{hostname:t,port:r,protocol:n="",path:s="",query:i={}}=e,o=new URL(`${n}//${t}${r?`:${r}`:""}${s}`);return o.search=Object.entries(i).map(([e,t])=>`${e}=${t}`).join("&"),o}return new URL(e)}catch(e){return null}})();if(!t)return console.error(`Unable to parse ${JSON.stringify(e)} as a whatwg URL.`),null;let r=t.href,{host:n,hostname:s,pathname:o,protocol:a,search:c}=t;if(c)return null;let u=a.slice(0,-1);if(!Object.values(h.Ue).includes(u))return null;let l=i(s),d=r.includes(`${n}:${m[u]}`)||"string"==typeof e&&e.includes(`${n}:${m[u]}`),p=`${n}${d?`:${m[u]}`:""}`;return{scheme:u,authority:p,path:o,normalizedPath:o.endsWith("/")?o:`${o}/`,isIp:l}},stringEquals:(e,t)=>e===t,substring:(e,t,r,n)=>t>=r||e.length<r?null:n?e.substring(e.length-r,e.length-t):e.substring(t,r),uriEncode:e=>encodeURIComponent(e).replace(/[!*'()]/g,e=>`%${e.charCodeAt(0).toString(16).toUpperCase()}`)},g=(e,t)=>{let r=[],n={...t.endpointParams,...t.referenceRecord},s=0;for(;s<e.length;){let t=e.indexOf("{",s);if(-1===t){r.push(e.slice(s));break}r.push(e.slice(s,t));let i=e.indexOf("}",t);if(-1===i){r.push(e.slice(t));break}"{"===e[t+1]&&"}"===e[i+1]&&(r.push(e.slice(t+1,i)),s=i+2);let o=e.substring(t+1,i);if(o.includes("#")){let[e,t]=o.split("#");r.push(f(n[e],t))}else r.push(n[o]);s=i+1}return r.join("")},x=({ref:e},t)=>({...t.endpointParams,...t.referenceRecord})[e],b=(e,t,r)=>{if("string"==typeof e)return g(e,r);if(e.fn)return w(e,r);if(e.ref)return x(e,r);throw new d(`'${t}': ${String(e)} is not a string, function or reference.`)},w=({fn:e,argv:t},r)=>{let n=t.map(e=>["boolean","number"].includes(typeof e)?e:b(e,"arg",r)),s=e.split(".");return s[0]in c&&null!=s[1]?c[s[0]][s[1]](...n):y[e](...n)},v=({assign:e,...t},r)=>{if(e&&e in r.referenceRecord)throw new d(`'${e}' is already defined in Reference Record.`);let n=w(t,r);return r.logger?.debug?.(`${u} evaluateCondition: ${l(t)} = ${l(n)}`),{result:""===n||!!n,...null!=e&&{toAssign:{name:e,value:n}}}},E=(e=[],t)=>{let r={};for(let n of e){let{result:e,toAssign:s}=v(n,{...t,referenceRecord:{...t.referenceRecord,...r}});if(!e)return{result:e};s&&(r[s.name]=s.value,t.logger?.debug?.(`${u} assign: ${s.name} := ${l(s.value)}`))}return{result:!0,referenceRecord:r}},S=(e,t)=>Object.entries(e).reduce((e,[r,n])=>({...e,[r]:n.map(e=>{let n=b(e,"Header value entry",t);if("string"!=typeof n)throw new d(`Header '${r}' value '${n}' is not a string`);return n})}),{}),P=(e,t)=>{if(Array.isArray(e))return e.map(e=>P(e,t));switch(typeof e){case"string":return g(e,t);case"object":if(null===e)throw new d(`Unexpected endpoint property: ${e}`);return O(e,t);case"boolean":return e;default:throw new d(`Unexpected endpoint property type: ${typeof e}`)}},O=(e,t)=>Object.entries(e).reduce((e,[r,n])=>({...e,[r]:P(n,t)}),{}),$=(e,t)=>{let r=b(e,"Endpoint URL",t);if("string"==typeof r)try{return new URL(r)}catch(e){throw console.error(`Failed to construct URL with ${r}`,e),e}throw new d(`Endpoint URL must be a string, got ${typeof r}`)},A=(e,t)=>{let{conditions:r,endpoint:n}=e,{result:s,referenceRecord:i}=E(r,t);if(!s)return;let o={...t,referenceRecord:{...t.referenceRecord,...i}},{url:a,properties:c,headers:d}=n;return t.logger?.debug?.(`${u} Resolving endpoint from template: ${l(n)}`),{...void 0!=d&&{headers:S(d,o)},...void 0!=c&&{properties:O(c,o)},url:$(a,o)}},M=(e,t)=>{let{conditions:r,error:n}=e,{result:s,referenceRecord:i}=E(r,t);if(s)throw new d(b(n,"Error",{...t,referenceRecord:{...t.referenceRecord,...i}}))},I=(e,t)=>{let{conditions:r,rules:n}=e,{result:s,referenceRecord:i}=E(r,t);if(s)return C(n,{...t,referenceRecord:{...t.referenceRecord,...i}})},C=(e,t)=>{for(let r of e)if("endpoint"===r.type){let e=A(r,t);if(e)return e}else if("error"===r.type)M(r,t);else if("tree"===r.type){let e=I(r,t);if(e)return e}else throw new d(`Unknown endpoint rule: ${r}`);throw new d("Rules evaluation failed")},k=(e,t)=>{let{endpointParams:r,logger:n}=t,{parameters:s,rules:i}=e;t.logger?.debug?.(`${u} Initial EndpointParams: ${l(r)}`);let o=Object.entries(s).filter(([,e])=>null!=e.default).map(([e,t])=>[e,t.default]);if(o.length>0)for(let[e,t]of o)r[e]=r[e]??t;for(let e of Object.entries(s).filter(([,e])=>e.required).map(([e])=>e))if(null==r[e])throw new d(`Missing required parameter: '${e}'`);let a=C(i,{endpointParams:r,logger:n,referenceRecord:{}});return t.logger?.debug?.(`${u} Resolved endpoint: ${l(a)}`),a}},8004:(e,t,r)=>{"use strict";r.d(t,{a:()=>i,n:()=>o});let n={},s={};for(let e=0;e<256;e++){let t=e.toString(16).toLowerCase();1===t.length&&(t=`0${t}`),n[e]=t,s[t]=e}function i(e){if(e.length%2!=0)throw Error("Hex encoded strings must have an even number length");let t=new Uint8Array(e.length/2);for(let r=0;r<e.length;r+=2){let n=e.slice(r,r+2).toLowerCase();if(n in s)t[r/2]=s[n];else throw Error(`Cannot decode unrecognized sequence ${n} as hexadecimal`)}return t}function o(e){let t="";for(let r=0;r<e.byteLength;r++)t+=n[e[r]];return t}},9848:(e,t,r)=>{"use strict";r.d(t,{u:()=>s,t:()=>i});var n=r(6100);let s=e=>e[n.Vf]||(e[n.Vf]={}),i=e=>{if("function"==typeof e)return e;let t=Promise.resolve(e);return()=>t}},2073:(e,t,r)=>{"use strict";r.d(t,{Y:()=>w,Gz:()=>n,bp:()=>c,L0:()=>s,QJ:()=>a,Df:()=>d,l5:()=>m,G8:()=>u,XP:()=>h,ok:()=>y,XS:()=>p,cm:()=>i,ru:()=>b,jh:()=>l,Rn:()=>f}),function(e){e.STANDARD="standard",e.ADAPTIVE="adaptive"}(i||(i={}));let n=3,s=i.STANDARD;var i,o=r(1924);class a{constructor(e){this.currentCapacity=0,this.enabled=!1,this.lastMaxRate=0,this.measuredTxRate=0,this.requestCount=0,this.lastTimestamp=0,this.timeWindow=0,this.beta=e?.beta??.7,this.minCapacity=e?.minCapacity??1,this.minFillRate=e?.minFillRate??.5,this.scaleConstant=e?.scaleConstant??.4,this.smooth=e?.smooth??.8;let t=this.getCurrentTimeInSeconds();this.lastThrottleTime=t,this.lastTxRateBucket=Math.floor(this.getCurrentTimeInSeconds()),this.fillRate=this.minFillRate,this.maxCapacity=this.minCapacity}getCurrentTimeInSeconds(){return Date.now()/1e3}async getSendToken(){return this.acquireTokenBucket(1)}async acquireTokenBucket(e){if(this.enabled){if(this.refillTokenBucket(),e>this.currentCapacity){let t=(e-this.currentCapacity)/this.fillRate*1e3;await new Promise(e=>a.setTimeoutFn(e,t))}this.currentCapacity=this.currentCapacity-e}}refillTokenBucket(){let e=this.getCurrentTimeInSeconds();if(!this.lastTimestamp){this.lastTimestamp=e;return}let t=(e-this.lastTimestamp)*this.fillRate;this.currentCapacity=Math.min(this.maxCapacity,this.currentCapacity+t),this.lastTimestamp=e}updateClientSendingRate(e){let t;if(this.updateMeasuredRate(),(0,o.Qb)(e)){let e=this.enabled?Math.min(this.measuredTxRate,this.fillRate):this.measuredTxRate;this.lastMaxRate=e,this.calculateTimeWindow(),this.lastThrottleTime=this.getCurrentTimeInSeconds(),t=this.cubicThrottle(e),this.enableTokenBucket()}else this.calculateTimeWindow(),t=this.cubicSuccess(this.getCurrentTimeInSeconds());let r=Math.min(t,2*this.measuredTxRate);this.updateTokenBucketRate(r)}calculateTimeWindow(){this.timeWindow=this.getPrecise(Math.pow(this.lastMaxRate*(1-this.beta)/this.scaleConstant,1/3))}cubicThrottle(e){return this.getPrecise(e*this.beta)}cubicSuccess(e){return this.getPrecise(this.scaleConstant*Math.pow(e-this.lastThrottleTime-this.timeWindow,3)+this.lastMaxRate)}enableTokenBucket(){this.enabled=!0}updateTokenBucketRate(e){this.refillTokenBucket(),this.fillRate=Math.max(e,this.minFillRate),this.maxCapacity=Math.max(e,this.minCapacity),this.currentCapacity=Math.min(this.currentCapacity,this.maxCapacity)}updateMeasuredRate(){let e=Math.floor(2*this.getCurrentTimeInSeconds())/2;if(this.requestCount++,e>this.lastTxRateBucket){let t=this.requestCount/(e-this.lastTxRateBucket);this.measuredTxRate=this.getPrecise(t*this.smooth+this.measuredTxRate*(1-this.smooth)),this.requestCount=0,this.lastTxRateBucket=e}}getPrecise(e){return parseFloat(e.toFixed(8))}}a.setTimeoutFn=setTimeout;let c=100,u=2e4,l=500,d=500,p=5,f=10,h=1,m="amz-sdk-invocation-id",y="amz-sdk-request",g=()=>{let e=c;return{computeNextBackoffDelay:t=>Math.floor(Math.min(u,Math.random()*2**t*e)),setDelayBase:t=>{e=t}}},x=({retryDelay:e,retryCount:t,retryCost:r})=>({getRetryCount:()=>t,getRetryDelay:()=>Math.min(u,e),getRetryCost:()=>r});class b{constructor(e){this.maxAttempts=e,this.mode=i.STANDARD,this.capacity=d,this.retryBackoffStrategy=g(),this.maxAttemptsProvider="function"==typeof e?e:async()=>e}async acquireInitialRetryToken(e){return x({retryDelay:c,retryCount:0})}async refreshRetryTokenForRetry(e,t){let r=await this.getMaxAttempts();if(this.shouldRetry(e,t,r)){let r=t.errorType;this.retryBackoffStrategy.setDelayBase("THROTTLING"===r?l:c);let n=this.retryBackoffStrategy.computeNextBackoffDelay(e.getRetryCount()),s=t.retryAfterHint?Math.max(t.retryAfterHint.getTime()-Date.now()||0,n):n,i=this.getCapacityCost(r);return this.capacity-=i,x({retryDelay:s,retryCount:e.getRetryCount()+1,retryCost:i})}throw Error("No retry token available")}recordSuccess(e){this.capacity=Math.max(d,this.capacity+(e.getRetryCost()??h))}getCapacity(){return this.capacity}async getMaxAttempts(){try{return await this.maxAttemptsProvider()}catch(e){return console.warn(`Max attempts provider could not resolve. Using default of ${n}`),n}}shouldRetry(e,t,r){return e.getRetryCount()+1<r&&this.capacity>=this.getCapacityCost(t.errorType)&&this.isRetryableError(t.errorType)}getCapacityCost(e){return"TRANSIENT"===e?f:p}isRetryableError(e){return"THROTTLING"===e||"TRANSIENT"===e}}class w{constructor(e,t){this.maxAttemptsProvider=e,this.mode=i.ADAPTIVE;let{rateLimiter:r}=t??{};this.rateLimiter=r??new a,this.standardRetryStrategy=new b(e)}async acquireInitialRetryToken(e){return await this.rateLimiter.getSendToken(),this.standardRetryStrategy.acquireInitialRetryToken(e)}async refreshRetryTokenForRetry(e,t){return this.rateLimiter.updateClientSendingRate(t),this.standardRetryStrategy.refreshRetryTokenForRetry(e,t)}recordSuccess(e){this.rateLimiter.updateClientSendingRate({}),this.standardRetryStrategy.recordSuccess(e)}}},2531:(e,t,r)=>{"use strict";r.d(t,{o:()=>n});let n=e=>encodeURIComponent(e).replace(/[!'()*]/g,s),s=e=>`%${e.charCodeAt(0).toString(16).toUpperCase()}`},2637:(e,t,r)=>{"use strict";r.d(t,{a:()=>n});let n=e=>new TextEncoder().encode(e)},3411:(e,t,r)=>{"use strict";r.d(t,{P:()=>n});let n=e=>{if("string"==typeof e)return e;if("object"!=typeof e||"number"!=typeof e.byteOffset||"number"!=typeof e.byteLength)throw Error("@smithy/util-utf8: toUtf8 encoder function only accepts string | Uint8Array.");return new TextDecoder("utf-8").decode(e)}},6880:function(e){var t;t=function(){return function(e){var t={};function r(n){if(t[n])return t[n].exports;var s=t[n]={i:n,l:!1,exports:{}};return e[n].call(s.exports,s,s.exports,r),s.l=!0,s.exports}return r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t||4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var s in e)r.d(n,s,(function(t){return e[t]}).bind(null,s));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=90)}({17:function(e,t,r){"use strict";t.__esModule=!0,t.default=void 0;var n=r(18),s=function(){function e(){}return e.getFirstMatch=function(e,t){var r=t.match(e);return r&&r.length>0&&r[1]||""},e.getSecondMatch=function(e,t){var r=t.match(e);return r&&r.length>1&&r[2]||""},e.matchAndReturnConst=function(e,t,r){if(e.test(t))return r},e.getWindowsVersionName=function(e){switch(e){case"NT":return"NT";case"XP":case"NT 5.1":return"XP";case"NT 5.0":return"2000";case"NT 5.2":return"2003";case"NT 6.0":return"Vista";case"NT 6.1":return"7";case"NT 6.2":return"8";case"NT 6.3":return"8.1";case"NT 10.0":return"10";default:return}},e.getMacOSVersionName=function(e){var t=e.split(".").splice(0,2).map(function(e){return parseInt(e,10)||0});if(t.push(0),10===t[0])switch(t[1]){case 5:return"Leopard";case 6:return"Snow Leopard";case 7:return"Lion";case 8:return"Mountain Lion";case 9:return"Mavericks";case 10:return"Yosemite";case 11:return"El Capitan";case 12:return"Sierra";case 13:return"High Sierra";case 14:return"Mojave";case 15:return"Catalina";default:return}},e.getAndroidVersionName=function(e){var t=e.split(".").splice(0,2).map(function(e){return parseInt(e,10)||0});if(t.push(0),!(1===t[0]&&t[1]<5))return 1===t[0]&&t[1]<6?"Cupcake":1===t[0]&&t[1]>=6?"Donut":2===t[0]&&t[1]<2?"Eclair":2===t[0]&&2===t[1]?"Froyo":2===t[0]&&t[1]>2?"Gingerbread":3===t[0]?"Honeycomb":4===t[0]&&t[1]<1?"Ice Cream Sandwich":4===t[0]&&t[1]<4?"Jelly Bean":4===t[0]&&t[1]>=4?"KitKat":5===t[0]?"Lollipop":6===t[0]?"Marshmallow":7===t[0]?"Nougat":8===t[0]?"Oreo":9===t[0]?"Pie":void 0},e.getVersionPrecision=function(e){return e.split(".").length},e.compareVersions=function(t,r,n){void 0===n&&(n=!1);var s=e.getVersionPrecision(t),i=e.getVersionPrecision(r),o=Math.max(s,i),a=0,c=e.map([t,r],function(t){var r=o-e.getVersionPrecision(t),n=t+Array(r+1).join(".0");return e.map(n.split("."),function(e){return Array(20-e.length).join("0")+e}).reverse()});for(n&&(a=o-Math.min(s,i)),o-=1;o>=a;){if(c[0][o]>c[1][o])return 1;if(c[0][o]===c[1][o]){if(o===a)return 0;o-=1}else if(c[0][o]<c[1][o])return -1}},e.map=function(e,t){var r,n=[];if(Array.prototype.map)return Array.prototype.map.call(e,t);for(r=0;r<e.length;r+=1)n.push(t(e[r]));return n},e.find=function(e,t){var r,n;if(Array.prototype.find)return Array.prototype.find.call(e,t);for(r=0,n=e.length;r<n;r+=1){var s=e[r];if(t(s,r))return s}},e.assign=function(e){for(var t,r,n=arguments.length,s=Array(n>1?n-1:0),i=1;i<n;i++)s[i-1]=arguments[i];if(Object.assign)return Object.assign.apply(Object,[e].concat(s));for(t=0,r=s.length;t<r;t+=1)(function(){var r=s[t];"object"==typeof r&&null!==r&&Object.keys(r).forEach(function(t){e[t]=r[t]})})();return e},e.getBrowserAlias=function(e){return n.BROWSER_ALIASES_MAP[e]},e.getBrowserTypeByAlias=function(e){return n.BROWSER_MAP[e]||""},e}();t.default=s,e.exports=t.default},18:function(e,t,r){"use strict";t.__esModule=!0,t.ENGINE_MAP=t.OS_MAP=t.PLATFORMS_MAP=t.BROWSER_MAP=t.BROWSER_ALIASES_MAP=void 0,t.BROWSER_ALIASES_MAP={"Amazon Silk":"amazon_silk","Android Browser":"android",Bada:"bada",BlackBerry:"blackberry",Chrome:"chrome",Chromium:"chromium",Electron:"electron",Epiphany:"epiphany",Firefox:"firefox",Focus:"focus",Generic:"generic","Google Search":"google_search",Googlebot:"googlebot","Internet Explorer":"ie","K-Meleon":"k_meleon",Maxthon:"maxthon","Microsoft Edge":"edge","MZ Browser":"mz","NAVER Whale Browser":"naver",Opera:"opera","Opera Coast":"opera_coast",PhantomJS:"phantomjs",Puffin:"puffin",QupZilla:"qupzilla",QQ:"qq",QQLite:"qqlite",Safari:"safari",Sailfish:"sailfish","Samsung Internet for Android":"samsung_internet",SeaMonkey:"seamonkey",Sleipnir:"sleipnir",Swing:"swing",Tizen:"tizen","UC Browser":"uc",Vivaldi:"vivaldi","WebOS Browser":"webos",WeChat:"wechat","Yandex Browser":"yandex",Roku:"roku"},t.BROWSER_MAP={amazon_silk:"Amazon Silk",android:"Android Browser",bada:"Bada",blackberry:"BlackBerry",chrome:"Chrome",chromium:"Chromium",electron:"Electron",epiphany:"Epiphany",firefox:"Firefox",focus:"Focus",generic:"Generic",googlebot:"Googlebot",google_search:"Google Search",ie:"Internet Explorer",k_meleon:"K-Meleon",maxthon:"Maxthon",edge:"Microsoft Edge",mz:"MZ Browser",naver:"NAVER Whale Browser",opera:"Opera",opera_coast:"Opera Coast",phantomjs:"PhantomJS",puffin:"Puffin",qupzilla:"QupZilla",qq:"QQ Browser",qqlite:"QQ Browser Lite",safari:"Safari",sailfish:"Sailfish",samsung_internet:"Samsung Internet for Android",seamonkey:"SeaMonkey",sleipnir:"Sleipnir",swing:"Swing",tizen:"Tizen",uc:"UC Browser",vivaldi:"Vivaldi",webos:"WebOS Browser",wechat:"WeChat",yandex:"Yandex Browser"},t.PLATFORMS_MAP={tablet:"tablet",mobile:"mobile",desktop:"desktop",tv:"tv"},t.OS_MAP={WindowsPhone:"Windows Phone",Windows:"Windows",MacOS:"macOS",iOS:"iOS",Android:"Android",WebOS:"WebOS",BlackBerry:"BlackBerry",Bada:"Bada",Tizen:"Tizen",Linux:"Linux",ChromeOS:"Chrome OS",PlayStation4:"PlayStation 4",Roku:"Roku"},t.ENGINE_MAP={EdgeHTML:"EdgeHTML",Blink:"Blink",Trident:"Trident",Presto:"Presto",Gecko:"Gecko",WebKit:"WebKit"}},90:function(e,t,r){"use strict";t.__esModule=!0,t.default=void 0;var n,s=(n=r(91))&&n.__esModule?n:{default:n},i=r(18),o=function(){function e(){}return e.getParser=function(e,t){if(void 0===t&&(t=!1),"string"!=typeof e)throw Error("UserAgent should be a string");return new s.default(e,t)},e.parse=function(e){return new s.default(e).getResult()},function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(e,[{key:"BROWSER_MAP",get:function(){return i.BROWSER_MAP}},{key:"ENGINE_MAP",get:function(){return i.ENGINE_MAP}},{key:"OS_MAP",get:function(){return i.OS_MAP}},{key:"PLATFORMS_MAP",get:function(){return i.PLATFORMS_MAP}}]),e}();t.default=o,e.exports=t.default},91:function(e,t,r){"use strict";t.__esModule=!0,t.default=void 0;var n=c(r(92)),s=c(r(93)),i=c(r(94)),o=c(r(95)),a=c(r(17));function c(e){return e&&e.__esModule?e:{default:e}}var u=function(){function e(e,t){if(void 0===t&&(t=!1),null==e||""===e)throw Error("UserAgent parameter can't be empty");this._ua=e,this.parsedResult={},!0!==t&&this.parse()}var t=e.prototype;return t.getUA=function(){return this._ua},t.test=function(e){return e.test(this._ua)},t.parseBrowser=function(){var e=this;this.parsedResult.browser={};var t=a.default.find(n.default,function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some(function(t){return e.test(t)});throw Error("Browser's test function is not valid")});return t&&(this.parsedResult.browser=t.describe(this.getUA())),this.parsedResult.browser},t.getBrowser=function(){return this.parsedResult.browser?this.parsedResult.browser:this.parseBrowser()},t.getBrowserName=function(e){return e?String(this.getBrowser().name).toLowerCase()||"":this.getBrowser().name||""},t.getBrowserVersion=function(){return this.getBrowser().version},t.getOS=function(){return this.parsedResult.os?this.parsedResult.os:this.parseOS()},t.parseOS=function(){var e=this;this.parsedResult.os={};var t=a.default.find(s.default,function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some(function(t){return e.test(t)});throw Error("Browser's test function is not valid")});return t&&(this.parsedResult.os=t.describe(this.getUA())),this.parsedResult.os},t.getOSName=function(e){var t=this.getOS().name;return e?String(t).toLowerCase()||"":t||""},t.getOSVersion=function(){return this.getOS().version},t.getPlatform=function(){return this.parsedResult.platform?this.parsedResult.platform:this.parsePlatform()},t.getPlatformType=function(e){void 0===e&&(e=!1);var t=this.getPlatform().type;return e?String(t).toLowerCase()||"":t||""},t.parsePlatform=function(){var e=this;this.parsedResult.platform={};var t=a.default.find(i.default,function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some(function(t){return e.test(t)});throw Error("Browser's test function is not valid")});return t&&(this.parsedResult.platform=t.describe(this.getUA())),this.parsedResult.platform},t.getEngine=function(){return this.parsedResult.engine?this.parsedResult.engine:this.parseEngine()},t.getEngineName=function(e){return e?String(this.getEngine().name).toLowerCase()||"":this.getEngine().name||""},t.parseEngine=function(){var e=this;this.parsedResult.engine={};var t=a.default.find(o.default,function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some(function(t){return e.test(t)});throw Error("Browser's test function is not valid")});return t&&(this.parsedResult.engine=t.describe(this.getUA())),this.parsedResult.engine},t.parse=function(){return this.parseBrowser(),this.parseOS(),this.parsePlatform(),this.parseEngine(),this},t.getResult=function(){return a.default.assign({},this.parsedResult)},t.satisfies=function(e){var t=this,r={},n=0,s={},i=0;if(Object.keys(e).forEach(function(t){var o=e[t];"string"==typeof o?(s[t]=o,i+=1):"object"==typeof o&&(r[t]=o,n+=1)}),n>0){var o=Object.keys(r),c=a.default.find(o,function(e){return t.isOS(e)});if(c){var u=this.satisfies(r[c]);if(void 0!==u)return u}var l=a.default.find(o,function(e){return t.isPlatform(e)});if(l){var d=this.satisfies(r[l]);if(void 0!==d)return d}}if(i>0){var p=Object.keys(s),f=a.default.find(p,function(e){return t.isBrowser(e,!0)});if(void 0!==f)return this.compareVersion(s[f])}},t.isBrowser=function(e,t){void 0===t&&(t=!1);var r=this.getBrowserName().toLowerCase(),n=e.toLowerCase(),s=a.default.getBrowserTypeByAlias(n);return t&&s&&(n=s.toLowerCase()),n===r},t.compareVersion=function(e){var t=[0],r=e,n=!1,s=this.getBrowserVersion();if("string"==typeof s)return">"===e[0]||"<"===e[0]?(r=e.substr(1),"="===e[1]?(n=!0,r=e.substr(2)):t=[],">"===e[0]?t.push(1):t.push(-1)):"="===e[0]?r=e.substr(1):"~"===e[0]&&(n=!0,r=e.substr(1)),t.indexOf(a.default.compareVersions(s,r,n))>-1},t.isOS=function(e){return this.getOSName(!0)===String(e).toLowerCase()},t.isPlatform=function(e){return this.getPlatformType(!0)===String(e).toLowerCase()},t.isEngine=function(e){return this.getEngineName(!0)===String(e).toLowerCase()},t.is=function(e,t){return void 0===t&&(t=!1),this.isBrowser(e,t)||this.isOS(e)||this.isPlatform(e)},t.some=function(e){var t=this;return void 0===e&&(e=[]),e.some(function(e){return t.is(e)})},e}();t.default=u,e.exports=t.default},92:function(e,t,r){"use strict";t.__esModule=!0,t.default=void 0;var n,s=(n=r(17))&&n.__esModule?n:{default:n},i=/version\/(\d+(\.?_?\d+)+)/i,o=[{test:[/googlebot/i],describe:function(e){var t={name:"Googlebot"},r=s.default.getFirstMatch(/googlebot\/(\d+(\.\d+))/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/opera/i],describe:function(e){var t={name:"Opera"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/(?:opera)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/opr\/|opios/i],describe:function(e){var t={name:"Opera"},r=s.default.getFirstMatch(/(?:opr|opios)[\s/](\S+)/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/SamsungBrowser/i],describe:function(e){var t={name:"Samsung Internet for Android"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/(?:SamsungBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/Whale/i],describe:function(e){var t={name:"NAVER Whale Browser"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/(?:whale)[\s/](\d+(?:\.\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/MZBrowser/i],describe:function(e){var t={name:"MZ Browser"},r=s.default.getFirstMatch(/(?:MZBrowser)[\s/](\d+(?:\.\d+)+)/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/focus/i],describe:function(e){var t={name:"Focus"},r=s.default.getFirstMatch(/(?:focus)[\s/](\d+(?:\.\d+)+)/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/swing/i],describe:function(e){var t={name:"Swing"},r=s.default.getFirstMatch(/(?:swing)[\s/](\d+(?:\.\d+)+)/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/coast/i],describe:function(e){var t={name:"Opera Coast"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/(?:coast)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/opt\/\d+(?:.?_?\d+)+/i],describe:function(e){var t={name:"Opera Touch"},r=s.default.getFirstMatch(/(?:opt)[\s/](\d+(\.?_?\d+)+)/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/yabrowser/i],describe:function(e){var t={name:"Yandex Browser"},r=s.default.getFirstMatch(/(?:yabrowser)[\s/](\d+(\.?_?\d+)+)/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/ucbrowser/i],describe:function(e){var t={name:"UC Browser"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/(?:ucbrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/Maxthon|mxios/i],describe:function(e){var t={name:"Maxthon"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/(?:Maxthon|mxios)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/epiphany/i],describe:function(e){var t={name:"Epiphany"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/(?:epiphany)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/puffin/i],describe:function(e){var t={name:"Puffin"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/(?:puffin)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/sleipnir/i],describe:function(e){var t={name:"Sleipnir"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/(?:sleipnir)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/k-meleon/i],describe:function(e){var t={name:"K-Meleon"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/(?:k-meleon)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/micromessenger/i],describe:function(e){var t={name:"WeChat"},r=s.default.getFirstMatch(/(?:micromessenger)[\s/](\d+(\.?_?\d+)+)/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/qqbrowser/i],describe:function(e){var t={name:/qqbrowserlite/i.test(e)?"QQ Browser Lite":"QQ Browser"},r=s.default.getFirstMatch(/(?:qqbrowserlite|qqbrowser)[/](\d+(\.?_?\d+)+)/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/msie|trident/i],describe:function(e){var t={name:"Internet Explorer"},r=s.default.getFirstMatch(/(?:msie |rv:)(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/\sedg\//i],describe:function(e){var t={name:"Microsoft Edge"},r=s.default.getFirstMatch(/\sedg\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/edg([ea]|ios)/i],describe:function(e){var t={name:"Microsoft Edge"},r=s.default.getSecondMatch(/edg([ea]|ios)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/vivaldi/i],describe:function(e){var t={name:"Vivaldi"},r=s.default.getFirstMatch(/vivaldi\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/seamonkey/i],describe:function(e){var t={name:"SeaMonkey"},r=s.default.getFirstMatch(/seamonkey\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/sailfish/i],describe:function(e){var t={name:"Sailfish"},r=s.default.getFirstMatch(/sailfish\s?browser\/(\d+(\.\d+)?)/i,e);return r&&(t.version=r),t}},{test:[/silk/i],describe:function(e){var t={name:"Amazon Silk"},r=s.default.getFirstMatch(/silk\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/phantom/i],describe:function(e){var t={name:"PhantomJS"},r=s.default.getFirstMatch(/phantomjs\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/slimerjs/i],describe:function(e){var t={name:"SlimerJS"},r=s.default.getFirstMatch(/slimerjs\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe:function(e){var t={name:"BlackBerry"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/blackberry[\d]+\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/(web|hpw)[o0]s/i],describe:function(e){var t={name:"WebOS Browser"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/w(?:eb)?[o0]sbrowser\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/bada/i],describe:function(e){var t={name:"Bada"},r=s.default.getFirstMatch(/dolfin\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/tizen/i],describe:function(e){var t={name:"Tizen"},r=s.default.getFirstMatch(/(?:tizen\s?)?browser\/(\d+(\.?_?\d+)+)/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/qupzilla/i],describe:function(e){var t={name:"QupZilla"},r=s.default.getFirstMatch(/(?:qupzilla)[\s/](\d+(\.?_?\d+)+)/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/firefox|iceweasel|fxios/i],describe:function(e){var t={name:"Firefox"},r=s.default.getFirstMatch(/(?:firefox|iceweasel|fxios)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/electron/i],describe:function(e){var t={name:"Electron"},r=s.default.getFirstMatch(/(?:electron)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/MiuiBrowser/i],describe:function(e){var t={name:"Miui"},r=s.default.getFirstMatch(/(?:MiuiBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/chromium/i],describe:function(e){var t={name:"Chromium"},r=s.default.getFirstMatch(/(?:chromium)[\s/](\d+(\.?_?\d+)+)/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/chrome|crios|crmo/i],describe:function(e){var t={name:"Chrome"},r=s.default.getFirstMatch(/(?:chrome|crios|crmo)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/GSA/i],describe:function(e){var t={name:"Google Search"},r=s.default.getFirstMatch(/(?:GSA)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:function(e){var t=!e.test(/like android/i),r=e.test(/android/i);return t&&r},describe:function(e){var t={name:"Android Browser"},r=s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/playstation 4/i],describe:function(e){var t={name:"PlayStation 4"},r=s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/safari|applewebkit/i],describe:function(e){var t={name:"Safari"},r=s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/.*/i],describe:function(e){var t=-1!==e.search("\\(")?/^(.*)\/(.*)[ \t]\((.*)/:/^(.*)\/(.*) /;return{name:s.default.getFirstMatch(t,e),version:s.default.getSecondMatch(t,e)}}}];t.default=o,e.exports=t.default},93:function(e,t,r){"use strict";t.__esModule=!0,t.default=void 0;var n,s=(n=r(17))&&n.__esModule?n:{default:n},i=r(18),o=[{test:[/Roku\/DVP/],describe:function(e){var t=s.default.getFirstMatch(/Roku\/DVP-(\d+\.\d+)/i,e);return{name:i.OS_MAP.Roku,version:t}}},{test:[/windows phone/i],describe:function(e){var t=s.default.getFirstMatch(/windows phone (?:os)?\s?(\d+(\.\d+)*)/i,e);return{name:i.OS_MAP.WindowsPhone,version:t}}},{test:[/windows /i],describe:function(e){var t=s.default.getFirstMatch(/Windows ((NT|XP)( \d\d?.\d)?)/i,e),r=s.default.getWindowsVersionName(t);return{name:i.OS_MAP.Windows,version:t,versionName:r}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe:function(e){var t={name:i.OS_MAP.iOS},r=s.default.getSecondMatch(/(Version\/)(\d[\d.]+)/,e);return r&&(t.version=r),t}},{test:[/macintosh/i],describe:function(e){var t=s.default.getFirstMatch(/mac os x (\d+(\.?_?\d+)+)/i,e).replace(/[_\s]/g,"."),r=s.default.getMacOSVersionName(t),n={name:i.OS_MAP.MacOS,version:t};return r&&(n.versionName=r),n}},{test:[/(ipod|iphone|ipad)/i],describe:function(e){var t=s.default.getFirstMatch(/os (\d+([_\s]\d+)*) like mac os x/i,e).replace(/[_\s]/g,".");return{name:i.OS_MAP.iOS,version:t}}},{test:function(e){var t=!e.test(/like android/i),r=e.test(/android/i);return t&&r},describe:function(e){var t=s.default.getFirstMatch(/android[\s/-](\d+(\.\d+)*)/i,e),r=s.default.getAndroidVersionName(t),n={name:i.OS_MAP.Android,version:t};return r&&(n.versionName=r),n}},{test:[/(web|hpw)[o0]s/i],describe:function(e){var t=s.default.getFirstMatch(/(?:web|hpw)[o0]s\/(\d+(\.\d+)*)/i,e),r={name:i.OS_MAP.WebOS};return t&&t.length&&(r.version=t),r}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe:function(e){var t=s.default.getFirstMatch(/rim\stablet\sos\s(\d+(\.\d+)*)/i,e)||s.default.getFirstMatch(/blackberry\d+\/(\d+([_\s]\d+)*)/i,e)||s.default.getFirstMatch(/\bbb(\d+)/i,e);return{name:i.OS_MAP.BlackBerry,version:t}}},{test:[/bada/i],describe:function(e){var t=s.default.getFirstMatch(/bada\/(\d+(\.\d+)*)/i,e);return{name:i.OS_MAP.Bada,version:t}}},{test:[/tizen/i],describe:function(e){var t=s.default.getFirstMatch(/tizen[/\s](\d+(\.\d+)*)/i,e);return{name:i.OS_MAP.Tizen,version:t}}},{test:[/linux/i],describe:function(){return{name:i.OS_MAP.Linux}}},{test:[/CrOS/],describe:function(){return{name:i.OS_MAP.ChromeOS}}},{test:[/PlayStation 4/],describe:function(e){var t=s.default.getFirstMatch(/PlayStation 4[/\s](\d+(\.\d+)*)/i,e);return{name:i.OS_MAP.PlayStation4,version:t}}}];t.default=o,e.exports=t.default},94:function(e,t,r){"use strict";t.__esModule=!0,t.default=void 0;var n,s=(n=r(17))&&n.__esModule?n:{default:n},i=r(18),o=[{test:[/googlebot/i],describe:function(){return{type:"bot",vendor:"Google"}}},{test:[/huawei/i],describe:function(e){var t=s.default.getFirstMatch(/(can-l01)/i,e)&&"Nova",r={type:i.PLATFORMS_MAP.mobile,vendor:"Huawei"};return t&&(r.model=t),r}},{test:[/nexus\s*(?:7|8|9|10).*/i],describe:function(){return{type:i.PLATFORMS_MAP.tablet,vendor:"Nexus"}}},{test:[/ipad/i],describe:function(){return{type:i.PLATFORMS_MAP.tablet,vendor:"Apple",model:"iPad"}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe:function(){return{type:i.PLATFORMS_MAP.tablet,vendor:"Apple",model:"iPad"}}},{test:[/kftt build/i],describe:function(){return{type:i.PLATFORMS_MAP.tablet,vendor:"Amazon",model:"Kindle Fire HD 7"}}},{test:[/silk/i],describe:function(){return{type:i.PLATFORMS_MAP.tablet,vendor:"Amazon"}}},{test:[/tablet(?! pc)/i],describe:function(){return{type:i.PLATFORMS_MAP.tablet}}},{test:function(e){var t=e.test(/ipod|iphone/i),r=e.test(/like (ipod|iphone)/i);return t&&!r},describe:function(e){var t=s.default.getFirstMatch(/(ipod|iphone)/i,e);return{type:i.PLATFORMS_MAP.mobile,vendor:"Apple",model:t}}},{test:[/nexus\s*[0-6].*/i,/galaxy nexus/i],describe:function(){return{type:i.PLATFORMS_MAP.mobile,vendor:"Nexus"}}},{test:[/[^-]mobi/i],describe:function(){return{type:i.PLATFORMS_MAP.mobile}}},{test:function(e){return"blackberry"===e.getBrowserName(!0)},describe:function(){return{type:i.PLATFORMS_MAP.mobile,vendor:"BlackBerry"}}},{test:function(e){return"bada"===e.getBrowserName(!0)},describe:function(){return{type:i.PLATFORMS_MAP.mobile}}},{test:function(e){return"windows phone"===e.getBrowserName()},describe:function(){return{type:i.PLATFORMS_MAP.mobile,vendor:"Microsoft"}}},{test:function(e){var t=Number(String(e.getOSVersion()).split(".")[0]);return"android"===e.getOSName(!0)&&t>=3},describe:function(){return{type:i.PLATFORMS_MAP.tablet}}},{test:function(e){return"android"===e.getOSName(!0)},describe:function(){return{type:i.PLATFORMS_MAP.mobile}}},{test:function(e){return"macos"===e.getOSName(!0)},describe:function(){return{type:i.PLATFORMS_MAP.desktop,vendor:"Apple"}}},{test:function(e){return"windows"===e.getOSName(!0)},describe:function(){return{type:i.PLATFORMS_MAP.desktop}}},{test:function(e){return"linux"===e.getOSName(!0)},describe:function(){return{type:i.PLATFORMS_MAP.desktop}}},{test:function(e){return"playstation 4"===e.getOSName(!0)},describe:function(){return{type:i.PLATFORMS_MAP.tv}}},{test:function(e){return"roku"===e.getOSName(!0)},describe:function(){return{type:i.PLATFORMS_MAP.tv}}}];t.default=o,e.exports=t.default},95:function(e,t,r){"use strict";t.__esModule=!0,t.default=void 0;var n,s=(n=r(17))&&n.__esModule?n:{default:n},i=r(18),o=[{test:function(e){return"microsoft edge"===e.getBrowserName(!0)},describe:function(e){if(/\sedg\//i.test(e))return{name:i.ENGINE_MAP.Blink};var t=s.default.getFirstMatch(/edge\/(\d+(\.?_?\d+)+)/i,e);return{name:i.ENGINE_MAP.EdgeHTML,version:t}}},{test:[/trident/i],describe:function(e){var t={name:i.ENGINE_MAP.Trident},r=s.default.getFirstMatch(/trident\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:function(e){return e.test(/presto/i)},describe:function(e){var t={name:i.ENGINE_MAP.Presto},r=s.default.getFirstMatch(/presto\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:function(e){var t=e.test(/gecko/i),r=e.test(/like gecko/i);return t&&!r},describe:function(e){var t={name:i.ENGINE_MAP.Gecko},r=s.default.getFirstMatch(/gecko\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/(apple)?webkit\/537\.36/i],describe:function(){return{name:i.ENGINE_MAP.Blink}}},{test:[/(apple)?webkit/i],describe:function(e){var t={name:i.ENGINE_MAP.WebKit},r=s.default.getFirstMatch(/webkit\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}}];t.default=o,e.exports=t.default}})},e.exports=t()},6454:(e,t,r)=>{"use strict";let n=r(3918),s=r(2923),i=r(8904);e.exports={XMLParser:s,XMLValidator:n,XMLBuilder:i}},5334:(e,t)=>{"use strict";let r=":A-Za-z_\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD",n="["+r+"]["+r+"\\-.\\d\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*",s=RegExp("^"+n+"$");t.isExist=function(e){return void 0!==e},t.isEmptyObject=function(e){return 0===Object.keys(e).length},t.merge=function(e,t,r){if(t){let n=Object.keys(t),s=n.length;for(let i=0;i<s;i++)"strict"===r?e[n[i]]=[t[n[i]]]:e[n[i]]=t[n[i]]}},t.getValue=function(e){return t.isExist(e)?e:""},t.isName=function(e){return null!=s.exec(e)},t.getAllMatches=function(e,t){let r=[],n=t.exec(e);for(;n;){let s=[];s.startIndex=t.lastIndex-n[0].length;let i=n.length;for(let e=0;e<i;e++)s.push(n[e]);r.push(s),n=t.exec(e)}return r},t.nameRegexp=n},3918:(e,t,r)=>{"use strict";let n=r(5334),s={allowBooleanAttributes:!1,unpairedTags:[]};function i(e){return" "===e||"	"===e||"\n"===e||"\r"===e}function o(e,t){let r=t;for(;t<e.length;t++)if("?"==e[t]||" "==e[t]){let n=e.substr(r,t-r);if(t>5&&"xml"===n)return l("InvalidXml","XML declaration allowed only at the start of the document.",d(e,t));if("?"!=e[t]||">"!=e[t+1])continue;t++;break}return t}function a(e,t){if(e.length>t+5&&"-"===e[t+1]&&"-"===e[t+2]){for(t+=3;t<e.length;t++)if("-"===e[t]&&"-"===e[t+1]&&">"===e[t+2]){t+=2;break}}else if(e.length>t+8&&"D"===e[t+1]&&"O"===e[t+2]&&"C"===e[t+3]&&"T"===e[t+4]&&"Y"===e[t+5]&&"P"===e[t+6]&&"E"===e[t+7]){let r=1;for(t+=8;t<e.length;t++)if("<"===e[t])r++;else if(">"===e[t]&&0==--r)break}else if(e.length>t+9&&"["===e[t+1]&&"C"===e[t+2]&&"D"===e[t+3]&&"A"===e[t+4]&&"T"===e[t+5]&&"A"===e[t+6]&&"["===e[t+7]){for(t+=8;t<e.length;t++)if("]"===e[t]&&"]"===e[t+1]&&">"===e[t+2]){t+=2;break}}return t}t.validate=function(e,t){t=Object.assign({},s,t);let r=[],c=!1,p=!1;"\uFEFF"===e[0]&&(e=e.substr(1));for(let s=0;s<e.length;s++)if("<"===e[s]&&"?"===e[s+1]){if(s+=2,(s=o(e,s)).err)return s}else if("<"===e[s]){let h=s;if("!"===e[++s]){s=a(e,s);continue}{var f;let m=!1;"/"===e[s]&&(m=!0,s++);let y="";for(;s<e.length&&">"!==e[s]&&" "!==e[s]&&"	"!==e[s]&&"\n"!==e[s]&&"\r"!==e[s];s++)y+=e[s];if("/"===(y=y.trim())[y.length-1]&&(y=y.substring(0,y.length-1),s--),f=y,!n.isName(f))return l("InvalidTag",0===y.trim().length?"Invalid space after '<'.":"Tag '"+y+"' is an invalid name.",d(e,s));let g=function(e,t){let r="",n="",s=!1;for(;t<e.length;t++){if('"'===e[t]||"'"===e[t])""===n?n=e[t]:n!==e[t]||(n="");else if(">"===e[t]&&""===n){s=!0;break}r+=e[t]}return""===n&&{value:r,index:t,tagClosed:s}}(e,s);if(!1===g)return l("InvalidAttr","Attributes for '"+y+"' have open quote.",d(e,s));let x=g.value;if(s=g.index,"/"===x[x.length-1]){let r=s-x.length,n=u(x=x.substring(0,x.length-1),t);if(!0!==n)return l(n.err.code,n.err.msg,d(e,r+n.err.line));c=!0}else if(m){if(!g.tagClosed)return l("InvalidTag","Closing tag '"+y+"' doesn't have proper closing.",d(e,s));if(x.trim().length>0)return l("InvalidTag","Closing tag '"+y+"' can't have attributes or invalid starting.",d(e,h));{if(0===r.length)return l("InvalidTag","Closing tag '"+y+"' has not been opened.",d(e,h));let t=r.pop();if(y!==t.tagName){let r=d(e,t.tagStartPos);return l("InvalidTag","Expected closing tag '"+t.tagName+"' (opened in line "+r.line+", col "+r.col+") instead of closing tag '"+y+"'.",d(e,h))}0==r.length&&(p=!0)}}else{let n=u(x,t);if(!0!==n)return l(n.err.code,n.err.msg,d(e,s-x.length+n.err.line));if(!0===p)return l("InvalidXml","Multiple possible root nodes found.",d(e,s));-1!==t.unpairedTags.indexOf(y)||r.push({tagName:y,tagStartPos:h}),c=!0}for(s++;s<e.length;s++)if("<"===e[s]){if("!"===e[s+1]){s=a(e,++s);continue}if("?"===e[s+1]){if((s=o(e,++s)).err)return s}else break}else if("&"===e[s]){let t=function(e,t){if(";"===e[++t])return -1;if("#"===e[t])return function(e,t){let r=/\d/;for("x"===e[t]&&(t++,r=/[\da-fA-F]/);t<e.length;t++){if(";"===e[t])return t;if(!e[t].match(r))break}return -1}(e,++t);let r=0;for(;t<e.length;t++,r++)if(!e[t].match(/\w/)||!(r<20)){if(";"===e[t])break;return -1}return t}(e,s);if(-1==t)return l("InvalidChar","char '&' is not expected.",d(e,s));s=t}else if(!0===p&&!i(e[s]))return l("InvalidXml","Extra text at the end",d(e,s));"<"===e[s]&&s--}}else{if(i(e[s]))continue;return l("InvalidChar","char '"+e[s]+"' is not expected.",d(e,s))}return c?1==r.length?l("InvalidTag","Unclosed tag '"+r[0].tagName+"'.",d(e,r[0].tagStartPos)):!(r.length>0)||l("InvalidXml","Invalid '"+JSON.stringify(r.map(e=>e.tagName),null,4).replace(/\r?\n/g,"")+"' found.",{line:1,col:1}):l("InvalidXml","Start tag expected.",1)};let c=RegExp("(\\s*)([^\\s=]+)(\\s*=)?(\\s*(['\"])(([\\s\\S])*?)\\5)?","g");function u(e,t){let r=n.getAllMatches(e,c),s={};for(let e=0;e<r.length;e++){if(0===r[e][1].length)return l("InvalidAttr","Attribute '"+r[e][2]+"' has no space in starting.",p(r[e]));if(void 0!==r[e][3]&&void 0===r[e][4])return l("InvalidAttr","Attribute '"+r[e][2]+"' is without value.",p(r[e]));if(void 0===r[e][3]&&!t.allowBooleanAttributes)return l("InvalidAttr","boolean attribute '"+r[e][2]+"' is not allowed.",p(r[e]));let i=r[e][2];if(!n.isName(i))return l("InvalidAttr","Attribute '"+i+"' is an invalid name.",p(r[e]));if(s.hasOwnProperty(i))return l("InvalidAttr","Attribute '"+i+"' is repeated.",p(r[e]));s[i]=1}return!0}function l(e,t,r){return{err:{code:e,msg:t,line:r.line||r,col:r.col}}}function d(e,t){let r=e.substring(0,t).split(/\r?\n/);return{line:r.length,col:r[r.length-1].length+1}}function p(e){return e.startIndex+e[1].length}},8904:(e,t,r)=>{"use strict";let n=r(2788),s={attributeNamePrefix:"@_",attributesGroupName:!1,textNodeName:"#text",ignoreAttributes:!0,cdataPropName:!1,format:!1,indentBy:"  ",suppressEmptyNode:!1,suppressUnpairedNode:!0,suppressBooleanAttributes:!0,tagValueProcessor:function(e,t){return t},attributeValueProcessor:function(e,t){return t},preserveOrder:!1,commentPropName:!1,unpairedTags:[],entities:[{regex:RegExp("&","g"),val:"&amp;"},{regex:RegExp(">","g"),val:"&gt;"},{regex:RegExp("<","g"),val:"&lt;"},{regex:RegExp("'","g"),val:"&apos;"},{regex:RegExp('"',"g"),val:"&quot;"}],processEntities:!0,stopNodes:[],oneListGroup:!1};function i(e){this.options=Object.assign({},s,e),this.options.ignoreAttributes||this.options.attributesGroupName?this.isAttribute=function(){return!1}:(this.attrPrefixLen=this.options.attributeNamePrefix.length,this.isAttribute=c),this.processTextOrObjNode=o,this.options.format?(this.indentate=a,this.tagEndChar=">\n",this.newLine="\n"):(this.indentate=function(){return""},this.tagEndChar=">",this.newLine="")}function o(e,t,r){let n=this.j2x(e,r+1);return void 0!==e[this.options.textNodeName]&&1===Object.keys(e).length?this.buildTextValNode(e[this.options.textNodeName],t,n.attrStr,r):this.buildObjectNode(n.val,t,n.attrStr,r)}function a(e){return this.options.indentBy.repeat(e)}function c(e){return!!e.startsWith(this.options.attributeNamePrefix)&&e!==this.options.textNodeName&&e.substr(this.attrPrefixLen)}i.prototype.build=function(e){return this.options.preserveOrder?n(e,this.options):(Array.isArray(e)&&this.options.arrayNodeName&&this.options.arrayNodeName.length>1&&(e={[this.options.arrayNodeName]:e}),this.j2x(e,0).val)},i.prototype.j2x=function(e,t){let r="",n="";for(let s in e)if(Object.prototype.hasOwnProperty.call(e,s)){if(void 0===e[s])this.isAttribute(s)&&(n+="");else if(null===e[s])this.isAttribute(s)?n+="":"?"===s[0]?n+=this.indentate(t)+"<"+s+"?"+this.tagEndChar:n+=this.indentate(t)+"<"+s+"/"+this.tagEndChar;else if(e[s]instanceof Date)n+=this.buildTextValNode(e[s],s,"",t);else if("object"!=typeof e[s]){let i=this.isAttribute(s);if(i)r+=this.buildAttrPairStr(i,""+e[s]);else if(s===this.options.textNodeName){let t=this.options.tagValueProcessor(s,""+e[s]);n+=this.replaceEntitiesValue(t)}else n+=this.buildTextValNode(e[s],s,"",t)}else if(Array.isArray(e[s])){let r=e[s].length,i="",o="";for(let a=0;a<r;a++){let r=e[s][a];if(void 0===r);else if(null===r)"?"===s[0]?n+=this.indentate(t)+"<"+s+"?"+this.tagEndChar:n+=this.indentate(t)+"<"+s+"/"+this.tagEndChar;else if("object"==typeof r){if(this.options.oneListGroup){let e=this.j2x(r,t+1);i+=e.val,this.options.attributesGroupName&&r.hasOwnProperty(this.options.attributesGroupName)&&(o+=e.attrStr)}else i+=this.processTextOrObjNode(r,s,t)}else if(this.options.oneListGroup){let e=this.options.tagValueProcessor(s,r);i+=e=this.replaceEntitiesValue(e)}else i+=this.buildTextValNode(r,s,"",t)}this.options.oneListGroup&&(i=this.buildObjectNode(i,s,o,t)),n+=i}else if(this.options.attributesGroupName&&s===this.options.attributesGroupName){let t=Object.keys(e[s]),n=t.length;for(let i=0;i<n;i++)r+=this.buildAttrPairStr(t[i],""+e[s][t[i]])}else n+=this.processTextOrObjNode(e[s],s,t)}return{attrStr:r,val:n}},i.prototype.buildAttrPairStr=function(e,t){return(t=this.options.attributeValueProcessor(e,""+t),t=this.replaceEntitiesValue(t),this.options.suppressBooleanAttributes&&"true"===t)?" "+e:" "+e+'="'+t+'"'},i.prototype.buildObjectNode=function(e,t,r,n){if(""===e)return"?"===t[0]?this.indentate(n)+"<"+t+r+"?"+this.tagEndChar:this.indentate(n)+"<"+t+r+this.closeTag(t)+this.tagEndChar;{let s="</"+t+this.tagEndChar,i="";return("?"===t[0]&&(i="?",s=""),(r||""===r)&&-1===e.indexOf("<"))?this.indentate(n)+"<"+t+r+i+">"+e+s:!1!==this.options.commentPropName&&t===this.options.commentPropName&&0===i.length?this.indentate(n)+`<!--${e}-->`+this.newLine:this.indentate(n)+"<"+t+r+i+this.tagEndChar+e+this.indentate(n)+s}},i.prototype.closeTag=function(e){let t="";return -1!==this.options.unpairedTags.indexOf(e)?this.options.suppressUnpairedNode||(t="/"):t=this.options.suppressEmptyNode?"/":`></${e}`,t},i.prototype.buildTextValNode=function(e,t,r,n){if(!1!==this.options.cdataPropName&&t===this.options.cdataPropName)return this.indentate(n)+`<![CDATA[${e}]]>`+this.newLine;if(!1!==this.options.commentPropName&&t===this.options.commentPropName)return this.indentate(n)+`<!--${e}-->`+this.newLine;if("?"===t[0])return this.indentate(n)+"<"+t+r+"?"+this.tagEndChar;{let s=this.options.tagValueProcessor(t,e);return""===(s=this.replaceEntitiesValue(s))?this.indentate(n)+"<"+t+r+this.closeTag(t)+this.tagEndChar:this.indentate(n)+"<"+t+r+">"+s+"</"+t+this.tagEndChar}},i.prototype.replaceEntitiesValue=function(e){if(e&&e.length>0&&this.options.processEntities)for(let t=0;t<this.options.entities.length;t++){let r=this.options.entities[t];e=e.replace(r.regex,r.val)}return e},e.exports=i},2788:e=>{function t(e,t){let n="";if(e&&!t.ignoreAttributes)for(let s in e){if(!e.hasOwnProperty(s))continue;let i=t.attributeValueProcessor(s,e[s]);!0===(i=r(i,t))&&t.suppressBooleanAttributes?n+=` ${s.substr(t.attributeNamePrefix.length)}`:n+=` ${s.substr(t.attributeNamePrefix.length)}="${i}"`}return n}function r(e,t){if(e&&e.length>0&&t.processEntities)for(let r=0;r<t.entities.length;r++){let n=t.entities[r];e=e.replace(n.regex,n.val)}return e}e.exports=function(e,n){let s="";return n.format&&n.indentBy.length>0&&(s="\n"),function e(n,s,i,o){let a="",c=!1;for(let u=0;u<n.length;u++){let l=n[u],d=function(e){let t=Object.keys(e);for(let r=0;r<t.length;r++){let n=t[r];if(e.hasOwnProperty(n)&&":@"!==n)return n}}(l);if(void 0===d)continue;let p="";if(p=0===i.length?d:`${i}.${d}`,d===s.textNodeName){let e=l[d];!function(e,t){let r=(e=e.substr(0,e.length-t.textNodeName.length-1)).substr(e.lastIndexOf(".")+1);for(let n in t.stopNodes)if(t.stopNodes[n]===e||t.stopNodes[n]==="*."+r)return!0;return!1}(p,s)&&(e=r(e=s.tagValueProcessor(d,e),s)),c&&(a+=o),a+=e,c=!1;continue}if(d===s.cdataPropName){c&&(a+=o),a+=`<![CDATA[${l[d][0][s.textNodeName]}]]>`,c=!1;continue}if(d===s.commentPropName){a+=o+`<!--${l[d][0][s.textNodeName]}-->`,c=!0;continue}if("?"===d[0]){let e=t(l[":@"],s),r="?xml"===d?"":o,n=l[d][0][s.textNodeName];n=0!==n.length?" "+n:"",a+=r+`<${d}${n}${e}?>`,c=!0;continue}let f=o;""!==f&&(f+=s.indentBy);let h=t(l[":@"],s),m=o+`<${d}${h}`,y=e(l[d],s,p,f);-1!==s.unpairedTags.indexOf(d)?s.suppressUnpairedNode?a+=m+">":a+=m+"/>":(!y||0===y.length)&&s.suppressEmptyNode?a+=m+"/>":y&&y.endsWith(">")?a+=m+`>${y}${o}</${d}>`:(a+=m+">",y&&""!==o&&(y.includes("/>")||y.includes("</"))?a+=o+s.indentBy+y+o:a+=y,a+=`</${d}>`),c=!0}return a}(e,n,"",s)}},9400:(e,t,r)=>{let n=r(5334);function s(e,t){return"!"===e[t+1]&&"-"===e[t+2]&&"-"===e[t+3]}e.exports=function(e,t){let r={};if("O"===e[t+3]&&"C"===e[t+4]&&"T"===e[t+5]&&"Y"===e[t+6]&&"P"===e[t+7]&&"E"===e[t+8]){t+=9;let f=1,h=!1,m=!1;for(;t<e.length;t++)if("<"!==e[t]||m){if(">"===e[t]){if(m?"-"===e[t-1]&&"-"===e[t-2]&&(m=!1,f--):f--,0===f)break}else"["===e[t]?h=!0:e[t]}else{var i,o,a,c,u,l,d,p;if(h&&"!"===(i=e)[(o=t)+1]&&"E"===i[o+2]&&"N"===i[o+3]&&"T"===i[o+4]&&"I"===i[o+5]&&"T"===i[o+6]&&"Y"===i[o+7])t+=7,[entityName,val,t]=function(e,t){let r="";for(;t<e.length&&"'"!==e[t]&&'"'!==e[t];t++)r+=e[t];if(-1!==(r=r.trim()).indexOf(" "))throw Error("External entites are not supported");let n=e[t++],s="";for(;t<e.length&&e[t]!==n;t++)s+=e[t];return[r,s,t]}(e,t+1),-1===val.indexOf("&")&&(r[function(e){if(n.isName(e))return e;throw Error(`Invalid entity name ${e}`)}(entityName)]={regx:RegExp(`&${entityName};`,"g"),val:val});else if(h&&"!"===(a=e)[(c=t)+1]&&"E"===a[c+2]&&"L"===a[c+3]&&"E"===a[c+4]&&"M"===a[c+5]&&"E"===a[c+6]&&"N"===a[c+7]&&"T"===a[c+8])t+=8;else if(h&&"!"===(u=e)[(l=t)+1]&&"A"===u[l+2]&&"T"===u[l+3]&&"T"===u[l+4]&&"L"===u[l+5]&&"I"===u[l+6]&&"S"===u[l+7]&&"T"===u[l+8])t+=8;else if(h&&"!"===(d=e)[(p=t)+1]&&"N"===d[p+2]&&"O"===d[p+3]&&"T"===d[p+4]&&"A"===d[p+5]&&"T"===d[p+6]&&"I"===d[p+7]&&"O"===d[p+8]&&"N"===d[p+9])t+=9;else if(s)m=!0;else throw Error("Invalid DOCTYPE");f++}if(0!==f)throw Error("Unclosed DOCTYPE")}else throw Error("Invalid Tag instead of DOCTYPE");return{entities:r,i:t}}},460:(e,t)=>{let r={preserveOrder:!1,attributeNamePrefix:"@_",attributesGroupName:!1,textNodeName:"#text",ignoreAttributes:!0,removeNSPrefix:!1,allowBooleanAttributes:!1,parseTagValue:!0,parseAttributeValue:!1,trimValues:!0,cdataPropName:!1,numberParseOptions:{hex:!0,leadingZeros:!0,eNotation:!0},tagValueProcessor:function(e,t){return t},attributeValueProcessor:function(e,t){return t},stopNodes:[],alwaysCreateTextNode:!1,isArray:()=>!1,commentPropName:!1,unpairedTags:[],processEntities:!0,htmlEntities:!1,ignoreDeclaration:!1,ignorePiTags:!1,transformTagName:!1,transformAttributeName:!1,updateTag:function(e,t,r){return e}};t.buildOptions=function(e){return Object.assign({},r,e)},t.defaultOptions=r},7680:(e,t,r)=>{"use strict";let n=r(5334),s=r(3832),i=r(9400),o=r(7983);class a{constructor(e){this.options=e,this.currentNode=null,this.tagsNodeStack=[],this.docTypeEntities={},this.lastEntities={apos:{regex:/&(apos|#39|#x27);/g,val:"'"},gt:{regex:/&(gt|#62|#x3E);/g,val:">"},lt:{regex:/&(lt|#60|#x3C);/g,val:"<"},quot:{regex:/&(quot|#34|#x22);/g,val:'"'}},this.ampEntity={regex:/&(amp|#38|#x26);/g,val:"&"},this.htmlEntities={space:{regex:/&(nbsp|#160);/g,val:" "},cent:{regex:/&(cent|#162);/g,val:"\xa2"},pound:{regex:/&(pound|#163);/g,val:"\xa3"},yen:{regex:/&(yen|#165);/g,val:"\xa5"},euro:{regex:/&(euro|#8364);/g,val:"€"},copyright:{regex:/&(copy|#169);/g,val:"\xa9"},reg:{regex:/&(reg|#174);/g,val:"\xae"},inr:{regex:/&(inr|#8377);/g,val:"₹"},num_dec:{regex:/&#([0-9]{1,7});/g,val:(e,t)=>String.fromCharCode(Number.parseInt(t,10))},num_hex:{regex:/&#x([0-9a-fA-F]{1,6});/g,val:(e,t)=>String.fromCharCode(Number.parseInt(t,16))}},this.addExternalEntities=c,this.parseXml=f,this.parseTextData=u,this.resolveNameSpace=l,this.buildAttributesMap=p,this.isItStopNode=g,this.replaceEntitiesValue=m,this.readStopNodeData=w,this.saveTextToParentTag=y,this.addChild=h}}function c(e){let t=Object.keys(e);for(let r=0;r<t.length;r++){let n=t[r];this.lastEntities[n]={regex:RegExp("&"+n+";","g"),val:e[n]}}}function u(e,t,r,n,s,i,o){if(void 0!==e&&(this.options.trimValues&&!n&&(e=e.trim()),e.length>0)){o||(e=this.replaceEntitiesValue(e));let n=this.options.tagValueProcessor(t,e,r,s,i);return null==n?e:typeof n!=typeof e||n!==e?n:this.options.trimValues?v(e,this.options.parseTagValue,this.options.numberParseOptions):e.trim()===e?v(e,this.options.parseTagValue,this.options.numberParseOptions):e}}function l(e){if(this.options.removeNSPrefix){let t=e.split(":"),r="/"===e.charAt(0)?"/":"";if("xmlns"===t[0])return"";2===t.length&&(e=r+t[1])}return e}let d=RegExp("([^\\s=]+)\\s*(=\\s*(['\"])([\\s\\S]*?)\\3)?","gm");function p(e,t,r){if(!this.options.ignoreAttributes&&"string"==typeof e){let r=n.getAllMatches(e,d),s=r.length,i={};for(let e=0;e<s;e++){let n=this.resolveNameSpace(r[e][1]),s=r[e][4],o=this.options.attributeNamePrefix+n;if(n.length){if(this.options.transformAttributeName&&(o=this.options.transformAttributeName(o)),"__proto__"===o&&(o="#__proto__"),void 0!==s){this.options.trimValues&&(s=s.trim()),s=this.replaceEntitiesValue(s);let e=this.options.attributeValueProcessor(n,s,t);null==e?i[o]=s:typeof e!=typeof s||e!==s?i[o]=e:i[o]=v(s,this.options.parseAttributeValue,this.options.numberParseOptions)}else this.options.allowBooleanAttributes&&(i[o]=!0)}}if(Object.keys(i).length){if(this.options.attributesGroupName){let e={};return e[this.options.attributesGroupName]=i,e}return i}}}let f=function(e){e=e.replace(/\r\n?/g,"\n");let t=new s("!xml"),r=t,n="",o="";for(let a=0;a<e.length;a++)if("<"===e[a]){if("/"===e[a+1]){let t=x(e,">",a,"Closing Tag is not closed."),s=e.substring(a+2,t).trim();if(this.options.removeNSPrefix){let e=s.indexOf(":");-1!==e&&(s=s.substr(e+1))}this.options.transformTagName&&(s=this.options.transformTagName(s)),r&&(n=this.saveTextToParentTag(n,r,o));let i=o.substring(o.lastIndexOf(".")+1);if(s&&-1!==this.options.unpairedTags.indexOf(s))throw Error(`Unpaired tag can not be used as closing tag: </${s}>`);let c=0;i&&-1!==this.options.unpairedTags.indexOf(i)?(c=o.lastIndexOf(".",o.lastIndexOf(".")-1),this.tagsNodeStack.pop()):c=o.lastIndexOf("."),o=o.substring(0,c),r=this.tagsNodeStack.pop(),n="",a=t}else if("?"===e[a+1]){let t=b(e,a,!1,"?>");if(!t)throw Error("Pi Tag is not closed.");if(n=this.saveTextToParentTag(n,r,o),this.options.ignoreDeclaration&&"?xml"===t.tagName||this.options.ignorePiTags);else{let e=new s(t.tagName);e.add(this.options.textNodeName,""),t.tagName!==t.tagExp&&t.attrExpPresent&&(e[":@"]=this.buildAttributesMap(t.tagExp,o,t.tagName)),this.addChild(r,e,o)}a=t.closeIndex+1}else if("!--"===e.substr(a+1,3)){let t=x(e,"--\x3e",a+4,"Comment is not closed.");if(this.options.commentPropName){let s=e.substring(a+4,t-2);n=this.saveTextToParentTag(n,r,o),r.add(this.options.commentPropName,[{[this.options.textNodeName]:s}])}a=t}else if("!D"===e.substr(a+1,2)){let t=i(e,a);this.docTypeEntities=t.entities,a=t.i}else if("!["===e.substr(a+1,2)){let t=x(e,"]]>",a,"CDATA is not closed.")-2,s=e.substring(a+9,t);n=this.saveTextToParentTag(n,r,o);let i=this.parseTextData(s,r.tagname,o,!0,!1,!0,!0);void 0==i&&(i=""),this.options.cdataPropName?r.add(this.options.cdataPropName,[{[this.options.textNodeName]:s}]):r.add(this.options.textNodeName,i),a=t+2}else{let i=b(e,a,this.options.removeNSPrefix),c=i.tagName,u=i.rawTagName,l=i.tagExp,d=i.attrExpPresent,p=i.closeIndex;this.options.transformTagName&&(c=this.options.transformTagName(c)),r&&n&&"!xml"!==r.tagname&&(n=this.saveTextToParentTag(n,r,o,!1));let f=r;if(f&&-1!==this.options.unpairedTags.indexOf(f.tagname)&&(r=this.tagsNodeStack.pop(),o=o.substring(0,o.lastIndexOf("."))),c!==t.tagname&&(o+=o?"."+c:c),this.isItStopNode(this.options.stopNodes,o,c)){let t="";if(l.length>0&&l.lastIndexOf("/")===l.length-1)"/"===c[c.length-1]?(c=c.substr(0,c.length-1),o=o.substr(0,o.length-1),l=c):l=l.substr(0,l.length-1),a=i.closeIndex;else if(-1!==this.options.unpairedTags.indexOf(c))a=i.closeIndex;else{let r=this.readStopNodeData(e,u,p+1);if(!r)throw Error(`Unexpected end of ${u}`);a=r.i,t=r.tagContent}let n=new s(c);c!==l&&d&&(n[":@"]=this.buildAttributesMap(l,o,c)),t&&(t=this.parseTextData(t,c,o,!0,d,!0,!0)),o=o.substr(0,o.lastIndexOf(".")),n.add(this.options.textNodeName,t),this.addChild(r,n,o)}else{if(l.length>0&&l.lastIndexOf("/")===l.length-1){"/"===c[c.length-1]?(c=c.substr(0,c.length-1),o=o.substr(0,o.length-1),l=c):l=l.substr(0,l.length-1),this.options.transformTagName&&(c=this.options.transformTagName(c));let e=new s(c);c!==l&&d&&(e[":@"]=this.buildAttributesMap(l,o,c)),this.addChild(r,e,o),o=o.substr(0,o.lastIndexOf("."))}else{let e=new s(c);this.tagsNodeStack.push(r),c!==l&&d&&(e[":@"]=this.buildAttributesMap(l,o,c)),this.addChild(r,e,o),r=e}n="",a=p}}}else n+=e[a];return t.child};function h(e,t,r){let n=this.options.updateTag(t.tagname,r,t[":@"]);!1===n||("string"==typeof n&&(t.tagname=n),e.addChild(t))}let m=function(e){if(this.options.processEntities){for(let t in this.docTypeEntities){let r=this.docTypeEntities[t];e=e.replace(r.regx,r.val)}for(let t in this.lastEntities){let r=this.lastEntities[t];e=e.replace(r.regex,r.val)}if(this.options.htmlEntities)for(let t in this.htmlEntities){let r=this.htmlEntities[t];e=e.replace(r.regex,r.val)}e=e.replace(this.ampEntity.regex,this.ampEntity.val)}return e};function y(e,t,r,n){return e&&(void 0===n&&(n=0===Object.keys(t.child).length),void 0!==(e=this.parseTextData(e,t.tagname,r,!1,!!t[":@"]&&0!==Object.keys(t[":@"]).length,n))&&""!==e&&t.add(this.options.textNodeName,e),e=""),e}function g(e,t,r){let n="*."+r;for(let r in e){let s=e[r];if(n===s||t===s)return!0}return!1}function x(e,t,r,n){let s=e.indexOf(t,r);if(-1!==s)return s+t.length-1;throw Error(n)}function b(e,t,r,n=">"){let s=function(e,t,r=">"){let n;let s="";for(let i=t;i<e.length;i++){let t=e[i];if(n)t===n&&(n="");else if('"'===t||"'"===t)n=t;else if(t===r[0]){if(!r[1]||e[i+1]===r[1])return{data:s,index:i}}else"	"===t&&(t=" ");s+=t}}(e,t+1,n);if(!s)return;let i=s.data,o=s.index,a=i.search(/\s/),c=i,u=!0;-1!==a&&(c=i.substring(0,a),i=i.substring(a+1).trimStart());let l=c;if(r){let e=c.indexOf(":");-1!==e&&(u=(c=c.substr(e+1))!==s.data.substr(e+1))}return{tagName:c,tagExp:i,closeIndex:o,attrExpPresent:u,rawTagName:l}}function w(e,t,r){let n=r,s=1;for(;r<e.length;r++)if("<"===e[r]){if("/"===e[r+1]){let i=x(e,">",r,`${t} is not closed`);if(e.substring(r+2,i).trim()===t&&0==--s)return{tagContent:e.substring(n,r),i:i};r=i}else if("?"===e[r+1])r=x(e,"?>",r+1,"StopNode is not closed.");else if("!--"===e.substr(r+1,3))r=x(e,"--\x3e",r+3,"StopNode is not closed.");else if("!["===e.substr(r+1,2))r=x(e,"]]>",r,"StopNode is not closed.")-2;else{let n=b(e,r,">");n&&((n&&n.tagName)===t&&"/"!==n.tagExp[n.tagExp.length-1]&&s++,r=n.closeIndex)}}}function v(e,t,r){if(t&&"string"==typeof e){let t=e.trim();return"true"===t||"false"!==t&&o(e,r)}return n.isExist(e)?e:""}e.exports=a},2923:(e,t,r)=>{let{buildOptions:n}=r(460),s=r(7680),{prettify:i}=r(5629),o=r(3918);class a{constructor(e){this.externalEntities={},this.options=n(e)}parse(e,t){if("string"==typeof e);else if(e.toString)e=e.toString();else throw Error("XML data is accepted in String or Bytes[] form.");if(t){!0===t&&(t={});let r=o.validate(e,t);if(!0!==r)throw Error(`${r.err.msg}:${r.err.line}:${r.err.col}`)}let r=new s(this.options);r.addExternalEntities(this.externalEntities);let n=r.parseXml(e);return this.options.preserveOrder||void 0===n?n:i(n,this.options)}addEntity(e,t){if(-1!==t.indexOf("&"))throw Error("Entity value can't have '&'");if(-1!==e.indexOf("&")||-1!==e.indexOf(";"))throw Error("An entity must be set without '&' and ';'. Eg. use '#xD' for '&#xD;'");if("&"===t)throw Error("An entity with value '&' is not permitted");this.externalEntities[e]=t}}e.exports=a},5629:(e,t)=>{"use strict";t.prettify=function(e,t){return function e(t,r,n){let s;let i={};for(let o=0;o<t.length;o++){let a=t[o],c=function(e){let t=Object.keys(e);for(let e=0;e<t.length;e++){let r=t[e];if(":@"!==r)return r}}(a),u="";if(u=void 0===n?c:n+"."+c,c===r.textNodeName)void 0===s?s=a[c]:s+=""+a[c];else if(void 0===c)continue;else if(a[c]){let t=e(a[c],r,u),n=function(e,t){let{textNodeName:r}=t,n=Object.keys(e).length;return 0===n||1===n&&(!!e[r]||"boolean"==typeof e[r]||0===e[r])}(t,r);a[":@"]?function(e,t,r,n){if(t){let s=Object.keys(t),i=s.length;for(let o=0;o<i;o++){let i=s[o];n.isArray(i,r+"."+i,!0,!0)?e[i]=[t[i]]:e[i]=t[i]}}}(t,a[":@"],u,r):1!==Object.keys(t).length||void 0===t[r.textNodeName]||r.alwaysCreateTextNode?0===Object.keys(t).length&&(r.alwaysCreateTextNode?t[r.textNodeName]="":t=""):t=t[r.textNodeName],void 0!==i[c]&&i.hasOwnProperty(c)?(Array.isArray(i[c])||(i[c]=[i[c]]),i[c].push(t)):r.isArray(c,u,n)?i[c]=[t]:i[c]=t}}return"string"==typeof s?s.length>0&&(i[r.textNodeName]=s):void 0!==s&&(i[r.textNodeName]=s),i}(e,t)}},3832:e=>{"use strict";class t{constructor(e){this.tagname=e,this.child=[],this[":@"]={}}add(e,t){"__proto__"===e&&(e="#__proto__"),this.child.push({[e]:t})}addChild(e){"__proto__"===e.tagname&&(e.tagname="#__proto__"),e[":@"]&&Object.keys(e[":@"]).length>0?this.child.push({[e.tagname]:e.child,":@":e[":@"]}):this.child.push({[e.tagname]:e.child})}}e.exports=t},2928:e=>{!function(){var t={675:function(e,t){"use strict";t.byteLength=function(e){var t=c(e),r=t[0],n=t[1];return(r+n)*3/4-n},t.toByteArray=function(e){var t,r,i=c(e),o=i[0],a=i[1],u=new s((o+a)*3/4-a),l=0,d=a>0?o-4:o;for(r=0;r<d;r+=4)t=n[e.charCodeAt(r)]<<18|n[e.charCodeAt(r+1)]<<12|n[e.charCodeAt(r+2)]<<6|n[e.charCodeAt(r+3)],u[l++]=t>>16&255,u[l++]=t>>8&255,u[l++]=255&t;return 2===a&&(t=n[e.charCodeAt(r)]<<2|n[e.charCodeAt(r+1)]>>4,u[l++]=255&t),1===a&&(t=n[e.charCodeAt(r)]<<10|n[e.charCodeAt(r+1)]<<4|n[e.charCodeAt(r+2)]>>2,u[l++]=t>>8&255,u[l++]=255&t),u},t.fromByteArray=function(e){for(var t,n=e.length,s=n%3,i=[],o=0,a=n-s;o<a;o+=16383)i.push(function(e,t,n){for(var s,i=[],o=t;o<n;o+=3)i.push(r[(s=(e[o]<<16&0xff0000)+(e[o+1]<<8&65280)+(255&e[o+2]))>>18&63]+r[s>>12&63]+r[s>>6&63]+r[63&s]);return i.join("")}(e,o,o+16383>a?a:o+16383));return 1===s?i.push(r[(t=e[n-1])>>2]+r[t<<4&63]+"=="):2===s&&i.push(r[(t=(e[n-2]<<8)+e[n-1])>>10]+r[t>>4&63]+r[t<<2&63]+"="),i.join("")};for(var r=[],n=[],s="undefined"!=typeof Uint8Array?Uint8Array:Array,i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",o=0,a=i.length;o<a;++o)r[o]=i[o],n[i.charCodeAt(o)]=o;function c(e){var t=e.length;if(t%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");-1===r&&(r=t);var n=r===t?0:4-r%4;return[r,n]}n["-".charCodeAt(0)]=62,n["_".charCodeAt(0)]=63},72:function(e,t,r){"use strict";var n=r(675),s=r(783),i="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function o(e){if(e>0x7fffffff)throw RangeError('The value "'+e+'" is invalid for option "size"');var t=new Uint8Array(e);return Object.setPrototypeOf(t,a.prototype),t}function a(e,t,r){if("number"==typeof e){if("string"==typeof t)throw TypeError('The "string" argument must be of type string. Received type number');return l(e)}return c(e,t,r)}function c(e,t,r){if("string"==typeof e)return function(e,t){if(("string"!=typeof t||""===t)&&(t="utf8"),!a.isEncoding(t))throw TypeError("Unknown encoding: "+t);var r=0|f(e,t),n=o(r),s=n.write(e,t);return s!==r&&(n=n.slice(0,s)),n}(e,t);if(ArrayBuffer.isView(e))return d(e);if(null==e)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(I(e,ArrayBuffer)||e&&I(e.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(I(e,SharedArrayBuffer)||e&&I(e.buffer,SharedArrayBuffer)))return function(e,t,r){var n;if(t<0||e.byteLength<t)throw RangeError('"offset" is outside of buffer bounds');if(e.byteLength<t+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(n=void 0===t&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,t):new Uint8Array(e,t,r),a.prototype),n}(e,t,r);if("number"==typeof e)throw TypeError('The "value" argument must not be of type number. Received type number');var n=e.valueOf&&e.valueOf();if(null!=n&&n!==e)return a.from(n,t,r);var s=function(e){if(a.isBuffer(e)){var t,r=0|p(e.length),n=o(r);return 0===n.length||e.copy(n,0,0,r),n}return void 0!==e.length?"number"!=typeof e.length||(t=e.length)!=t?o(0):d(e):"Buffer"===e.type&&Array.isArray(e.data)?d(e.data):void 0}(e);if(s)return s;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return a.from(e[Symbol.toPrimitive]("string"),t,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function u(e){if("number"!=typeof e)throw TypeError('"size" argument must be of type number');if(e<0)throw RangeError('The value "'+e+'" is invalid for option "size"')}function l(e){return u(e),o(e<0?0:0|p(e))}function d(e){for(var t=e.length<0?0:0|p(e.length),r=o(t),n=0;n<t;n+=1)r[n]=255&e[n];return r}function p(e){if(e>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|e}function f(e,t){if(a.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||I(e,ArrayBuffer))return e.byteLength;if("string"!=typeof e)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);var r=e.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;for(var s=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return O(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return A(e).length;default:if(s)return n?-1:O(e).length;t=(""+t).toLowerCase(),s=!0}}function h(e,t,r){var s,i,o=!1;if((void 0===t||t<0)&&(t=0),t>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(t>>>=0)))return"";for(e||(e="utf8");;)switch(e){case"hex":return function(e,t,r){var n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);for(var s="",i=t;i<r;++i)s+=C[e[i]];return s}(this,t,r);case"utf8":case"utf-8":return x(this,t,r);case"ascii":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var s=t;s<r;++s)n+=String.fromCharCode(127&e[s]);return n}(this,t,r);case"latin1":case"binary":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var s=t;s<r;++s)n+=String.fromCharCode(e[s]);return n}(this,t,r);case"base64":return s=t,i=r,0===s&&i===this.length?n.fromByteArray(this):n.fromByteArray(this.slice(s,i));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(e,t,r){for(var n=e.slice(t,r),s="",i=0;i<n.length;i+=2)s+=String.fromCharCode(n[i]+256*n[i+1]);return s}(this,t,r);default:if(o)throw TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),o=!0}}function m(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function y(e,t,r,n,s){var i;if(0===e.length)return -1;if("string"==typeof r?(n=r,r=0):r>0x7fffffff?r=0x7fffffff:r<-0x80000000&&(r=-0x80000000),(i=r=+r)!=i&&(r=s?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(s)return -1;r=e.length-1}else if(r<0){if(!s)return -1;r=0}if("string"==typeof t&&(t=a.from(t,n)),a.isBuffer(t))return 0===t.length?-1:g(e,t,r,n,s);if("number"==typeof t)return(t&=255,"function"==typeof Uint8Array.prototype.indexOf)?s?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):g(e,[t],r,n,s);throw TypeError("val must be string, number or Buffer")}function g(e,t,r,n,s){var i,o=1,a=e.length,c=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return -1;o=2,a/=2,c/=2,r/=2}function u(e,t){return 1===o?e[t]:e.readUInt16BE(t*o)}if(s){var l=-1;for(i=r;i<a;i++)if(u(e,i)===u(t,-1===l?0:i-l)){if(-1===l&&(l=i),i-l+1===c)return l*o}else -1!==l&&(i-=i-l),l=-1}else for(r+c>a&&(r=a-c),i=r;i>=0;i--){for(var d=!0,p=0;p<c;p++)if(u(e,i+p)!==u(t,p)){d=!1;break}if(d)return i}return -1}function x(e,t,r){r=Math.min(e.length,r);for(var n=[],s=t;s<r;){var i,o,a,c,u=e[s],l=null,d=u>239?4:u>223?3:u>191?2:1;if(s+d<=r)switch(d){case 1:u<128&&(l=u);break;case 2:(192&(i=e[s+1]))==128&&(c=(31&u)<<6|63&i)>127&&(l=c);break;case 3:i=e[s+1],o=e[s+2],(192&i)==128&&(192&o)==128&&(c=(15&u)<<12|(63&i)<<6|63&o)>2047&&(c<55296||c>57343)&&(l=c);break;case 4:i=e[s+1],o=e[s+2],a=e[s+3],(192&i)==128&&(192&o)==128&&(192&a)==128&&(c=(15&u)<<18|(63&i)<<12|(63&o)<<6|63&a)>65535&&c<1114112&&(l=c)}null===l?(l=65533,d=1):l>65535&&(l-=65536,n.push(l>>>10&1023|55296),l=56320|1023&l),n.push(l),s+=d}return function(e){var t=e.length;if(t<=4096)return String.fromCharCode.apply(String,e);for(var r="",n=0;n<t;)r+=String.fromCharCode.apply(String,e.slice(n,n+=4096));return r}(n)}function b(e,t,r){if(e%1!=0||e<0)throw RangeError("offset is not uint");if(e+t>r)throw RangeError("Trying to access beyond buffer length")}function w(e,t,r,n,s,i){if(!a.isBuffer(e))throw TypeError('"buffer" argument must be a Buffer instance');if(t>s||t<i)throw RangeError('"value" argument is out of bounds');if(r+n>e.length)throw RangeError("Index out of range")}function v(e,t,r,n,s,i){if(r+n>e.length||r<0)throw RangeError("Index out of range")}function E(e,t,r,n,i){return t=+t,r>>>=0,i||v(e,t,r,4,34028234663852886e22,-34028234663852886e22),s.write(e,t,r,n,23,4),r+4}function S(e,t,r,n,i){return t=+t,r>>>=0,i||v(e,t,r,8,17976931348623157e292,-17976931348623157e292),s.write(e,t,r,n,52,8),r+8}t.Buffer=a,t.SlowBuffer=function(e){return+e!=e&&(e=0),a.alloc(+e)},t.INSPECT_MAX_BYTES=50,t.kMaxLength=0x7fffffff,a.TYPED_ARRAY_SUPPORT=function(){try{var e=new Uint8Array(1),t={foo:function(){return 42}};return Object.setPrototypeOf(t,Uint8Array.prototype),Object.setPrototypeOf(e,t),42===e.foo()}catch(e){return!1}}(),a.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(a.prototype,"parent",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.buffer}}),Object.defineProperty(a.prototype,"offset",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.byteOffset}}),a.poolSize=8192,a.from=function(e,t,r){return c(e,t,r)},Object.setPrototypeOf(a.prototype,Uint8Array.prototype),Object.setPrototypeOf(a,Uint8Array),a.alloc=function(e,t,r){return(u(e),e<=0)?o(e):void 0!==t?"string"==typeof r?o(e).fill(t,r):o(e).fill(t):o(e)},a.allocUnsafe=function(e){return l(e)},a.allocUnsafeSlow=function(e){return l(e)},a.isBuffer=function(e){return null!=e&&!0===e._isBuffer&&e!==a.prototype},a.compare=function(e,t){if(I(e,Uint8Array)&&(e=a.from(e,e.offset,e.byteLength)),I(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),!a.isBuffer(e)||!a.isBuffer(t))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;for(var r=e.length,n=t.length,s=0,i=Math.min(r,n);s<i;++s)if(e[s]!==t[s]){r=e[s],n=t[s];break}return r<n?-1:n<r?1:0},a.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},a.concat=function(e,t){if(!Array.isArray(e))throw TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return a.alloc(0);if(void 0===t)for(r=0,t=0;r<e.length;++r)t+=e[r].length;var r,n=a.allocUnsafe(t),s=0;for(r=0;r<e.length;++r){var i=e[r];if(I(i,Uint8Array)&&(i=a.from(i)),!a.isBuffer(i))throw TypeError('"list" argument must be an Array of Buffers');i.copy(n,s),s+=i.length}return n},a.byteLength=f,a.prototype._isBuffer=!0,a.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)m(this,t,t+1);return this},a.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)m(this,t,t+3),m(this,t+1,t+2);return this},a.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)m(this,t,t+7),m(this,t+1,t+6),m(this,t+2,t+5),m(this,t+3,t+4);return this},a.prototype.toString=function(){var e=this.length;return 0===e?"":0==arguments.length?x(this,0,e):h.apply(this,arguments)},a.prototype.toLocaleString=a.prototype.toString,a.prototype.equals=function(e){if(!a.isBuffer(e))throw TypeError("Argument must be a Buffer");return this===e||0===a.compare(this,e)},a.prototype.inspect=function(){var e="",r=t.INSPECT_MAX_BYTES;return e=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(e+=" ... "),"<Buffer "+e+">"},i&&(a.prototype[i]=a.prototype.inspect),a.prototype.compare=function(e,t,r,n,s){if(I(e,Uint8Array)&&(e=a.from(e,e.offset,e.byteLength)),!a.isBuffer(e))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===s&&(s=this.length),t<0||r>e.length||n<0||s>this.length)throw RangeError("out of range index");if(n>=s&&t>=r)return 0;if(n>=s)return -1;if(t>=r)return 1;if(t>>>=0,r>>>=0,n>>>=0,s>>>=0,this===e)return 0;for(var i=s-n,o=r-t,c=Math.min(i,o),u=this.slice(n,s),l=e.slice(t,r),d=0;d<c;++d)if(u[d]!==l[d]){i=u[d],o=l[d];break}return i<o?-1:o<i?1:0},a.prototype.includes=function(e,t,r){return -1!==this.indexOf(e,t,r)},a.prototype.indexOf=function(e,t,r){return y(this,e,t,r,!0)},a.prototype.lastIndexOf=function(e,t,r){return y(this,e,t,r,!1)},a.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else if(isFinite(t))t>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var s,i,o,a,c,u,l,d,p,f,h,m,y=this.length-t;if((void 0===r||r>y)&&(r=y),e.length>0&&(r<0||t<0)||t>this.length)throw RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var g=!1;;)switch(n){case"hex":return function(e,t,r,n){r=Number(r)||0;var s=e.length-r;n?(n=Number(n))>s&&(n=s):n=s;var i=t.length;n>i/2&&(n=i/2);for(var o=0;o<n;++o){var a=parseInt(t.substr(2*o,2),16);if(a!=a)break;e[r+o]=a}return o}(this,e,t,r);case"utf8":case"utf-8":return c=t,u=r,M(O(e,this.length-c),this,c,u);case"ascii":return l=t,d=r,M($(e),this,l,d);case"latin1":case"binary":return s=this,i=e,o=t,a=r,M($(i),s,o,a);case"base64":return p=t,f=r,M(A(e),this,p,f);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return h=t,m=r,M(function(e,t){for(var r,n,s=[],i=0;i<e.length&&!((t-=2)<0);++i)n=(r=e.charCodeAt(i))>>8,s.push(r%256),s.push(n);return s}(e,this.length-h),this,h,m);default:if(g)throw TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),g=!0}},a.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},a.prototype.slice=function(e,t){var r=this.length;e=~~e,t=void 0===t?r:~~t,e<0?(e+=r)<0&&(e=0):e>r&&(e=r),t<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e);var n=this.subarray(e,t);return Object.setPrototypeOf(n,a.prototype),n},a.prototype.readUIntLE=function(e,t,r){e>>>=0,t>>>=0,r||b(e,t,this.length);for(var n=this[e],s=1,i=0;++i<t&&(s*=256);)n+=this[e+i]*s;return n},a.prototype.readUIntBE=function(e,t,r){e>>>=0,t>>>=0,r||b(e,t,this.length);for(var n=this[e+--t],s=1;t>0&&(s*=256);)n+=this[e+--t]*s;return n},a.prototype.readUInt8=function(e,t){return e>>>=0,t||b(e,1,this.length),this[e]},a.prototype.readUInt16LE=function(e,t){return e>>>=0,t||b(e,2,this.length),this[e]|this[e+1]<<8},a.prototype.readUInt16BE=function(e,t){return e>>>=0,t||b(e,2,this.length),this[e]<<8|this[e+1]},a.prototype.readUInt32LE=function(e,t){return e>>>=0,t||b(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+0x1000000*this[e+3]},a.prototype.readUInt32BE=function(e,t){return e>>>=0,t||b(e,4,this.length),0x1000000*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},a.prototype.readIntLE=function(e,t,r){e>>>=0,t>>>=0,r||b(e,t,this.length);for(var n=this[e],s=1,i=0;++i<t&&(s*=256);)n+=this[e+i]*s;return n>=(s*=128)&&(n-=Math.pow(2,8*t)),n},a.prototype.readIntBE=function(e,t,r){e>>>=0,t>>>=0,r||b(e,t,this.length);for(var n=t,s=1,i=this[e+--n];n>0&&(s*=256);)i+=this[e+--n]*s;return i>=(s*=128)&&(i-=Math.pow(2,8*t)),i},a.prototype.readInt8=function(e,t){return(e>>>=0,t||b(e,1,this.length),128&this[e])?-((255-this[e]+1)*1):this[e]},a.prototype.readInt16LE=function(e,t){e>>>=0,t||b(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?0xffff0000|r:r},a.prototype.readInt16BE=function(e,t){e>>>=0,t||b(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?0xffff0000|r:r},a.prototype.readInt32LE=function(e,t){return e>>>=0,t||b(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},a.prototype.readInt32BE=function(e,t){return e>>>=0,t||b(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},a.prototype.readFloatLE=function(e,t){return e>>>=0,t||b(e,4,this.length),s.read(this,e,!0,23,4)},a.prototype.readFloatBE=function(e,t){return e>>>=0,t||b(e,4,this.length),s.read(this,e,!1,23,4)},a.prototype.readDoubleLE=function(e,t){return e>>>=0,t||b(e,8,this.length),s.read(this,e,!0,52,8)},a.prototype.readDoubleBE=function(e,t){return e>>>=0,t||b(e,8,this.length),s.read(this,e,!1,52,8)},a.prototype.writeUIntLE=function(e,t,r,n){if(e=+e,t>>>=0,r>>>=0,!n){var s=Math.pow(2,8*r)-1;w(this,e,t,r,s,0)}var i=1,o=0;for(this[t]=255&e;++o<r&&(i*=256);)this[t+o]=e/i&255;return t+r},a.prototype.writeUIntBE=function(e,t,r,n){if(e=+e,t>>>=0,r>>>=0,!n){var s=Math.pow(2,8*r)-1;w(this,e,t,r,s,0)}var i=r-1,o=1;for(this[t+i]=255&e;--i>=0&&(o*=256);)this[t+i]=e/o&255;return t+r},a.prototype.writeUInt8=function(e,t,r){return e=+e,t>>>=0,r||w(this,e,t,1,255,0),this[t]=255&e,t+1},a.prototype.writeUInt16LE=function(e,t,r){return e=+e,t>>>=0,r||w(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},a.prototype.writeUInt16BE=function(e,t,r){return e=+e,t>>>=0,r||w(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},a.prototype.writeUInt32LE=function(e,t,r){return e=+e,t>>>=0,r||w(this,e,t,4,0xffffffff,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},a.prototype.writeUInt32BE=function(e,t,r){return e=+e,t>>>=0,r||w(this,e,t,4,0xffffffff,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},a.prototype.writeIntLE=function(e,t,r,n){if(e=+e,t>>>=0,!n){var s=Math.pow(2,8*r-1);w(this,e,t,r,s-1,-s)}var i=0,o=1,a=0;for(this[t]=255&e;++i<r&&(o*=256);)e<0&&0===a&&0!==this[t+i-1]&&(a=1),this[t+i]=(e/o>>0)-a&255;return t+r},a.prototype.writeIntBE=function(e,t,r,n){if(e=+e,t>>>=0,!n){var s=Math.pow(2,8*r-1);w(this,e,t,r,s-1,-s)}var i=r-1,o=1,a=0;for(this[t+i]=255&e;--i>=0&&(o*=256);)e<0&&0===a&&0!==this[t+i+1]&&(a=1),this[t+i]=(e/o>>0)-a&255;return t+r},a.prototype.writeInt8=function(e,t,r){return e=+e,t>>>=0,r||w(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},a.prototype.writeInt16LE=function(e,t,r){return e=+e,t>>>=0,r||w(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},a.prototype.writeInt16BE=function(e,t,r){return e=+e,t>>>=0,r||w(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},a.prototype.writeInt32LE=function(e,t,r){return e=+e,t>>>=0,r||w(this,e,t,4,0x7fffffff,-0x80000000),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},a.prototype.writeInt32BE=function(e,t,r){return e=+e,t>>>=0,r||w(this,e,t,4,0x7fffffff,-0x80000000),e<0&&(e=0xffffffff+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},a.prototype.writeFloatLE=function(e,t,r){return E(this,e,t,!0,r)},a.prototype.writeFloatBE=function(e,t,r){return E(this,e,t,!1,r)},a.prototype.writeDoubleLE=function(e,t,r){return S(this,e,t,!0,r)},a.prototype.writeDoubleBE=function(e,t,r){return S(this,e,t,!1,r)},a.prototype.copy=function(e,t,r,n){if(!a.isBuffer(e))throw TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r||0===e.length||0===this.length)return 0;if(t<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(n<0)throw RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var s=n-r;if(this===e&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(t,r,n);else if(this===e&&r<t&&t<n)for(var i=s-1;i>=0;--i)e[i+t]=this[i+r];else Uint8Array.prototype.set.call(e,this.subarray(r,n),t);return s},a.prototype.fill=function(e,t,r,n){if("string"==typeof e){if("string"==typeof t?(n=t,t=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw TypeError("encoding must be a string");if("string"==typeof n&&!a.isEncoding(n))throw TypeError("Unknown encoding: "+n);if(1===e.length){var s,i=e.charCodeAt(0);("utf8"===n&&i<128||"latin1"===n)&&(e=i)}}else"number"==typeof e?e&=255:"boolean"==typeof e&&(e=Number(e));if(t<0||this.length<t||this.length<r)throw RangeError("Out of range index");if(r<=t)return this;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(s=t;s<r;++s)this[s]=e;else{var o=a.isBuffer(e)?e:a.from(e,n),c=o.length;if(0===c)throw TypeError('The value "'+e+'" is invalid for argument "value"');for(s=0;s<r-t;++s)this[s+t]=o[s%c]}return this};var P=/[^+/0-9A-Za-z-_]/g;function O(e,t){t=t||1/0;for(var r,n=e.length,s=null,i=[],o=0;o<n;++o){if((r=e.charCodeAt(o))>55295&&r<57344){if(!s){if(r>56319||o+1===n){(t-=3)>-1&&i.push(239,191,189);continue}s=r;continue}if(r<56320){(t-=3)>-1&&i.push(239,191,189),s=r;continue}r=(s-55296<<10|r-56320)+65536}else s&&(t-=3)>-1&&i.push(239,191,189);if(s=null,r<128){if((t-=1)<0)break;i.push(r)}else if(r<2048){if((t-=2)<0)break;i.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;i.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((t-=4)<0)break;i.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return i}function $(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}function A(e){return n.toByteArray(function(e){if((e=(e=e.split("=")[0]).trim().replace(P,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function M(e,t,r,n){for(var s=0;s<n&&!(s+r>=t.length)&&!(s>=e.length);++s)t[s+r]=e[s];return s}function I(e,t){return e instanceof t||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===t.name}var C=function(){for(var e="0123456789abcdef",t=Array(256),r=0;r<16;++r)for(var n=16*r,s=0;s<16;++s)t[n+s]=e[r]+e[s];return t}()},783:function(e,t){t.read=function(e,t,r,n,s){var i,o,a=8*s-n-1,c=(1<<a)-1,u=c>>1,l=-7,d=r?s-1:0,p=r?-1:1,f=e[t+d];for(d+=p,i=f&(1<<-l)-1,f>>=-l,l+=a;l>0;i=256*i+e[t+d],d+=p,l-=8);for(o=i&(1<<-l)-1,i>>=-l,l+=n;l>0;o=256*o+e[t+d],d+=p,l-=8);if(0===i)i=1-u;else{if(i===c)return o?NaN:1/0*(f?-1:1);o+=Math.pow(2,n),i-=u}return(f?-1:1)*o*Math.pow(2,i-n)},t.write=function(e,t,r,n,s,i){var o,a,c,u=8*i-s-1,l=(1<<u)-1,d=l>>1,p=23===s?5960464477539062e-23:0,f=n?0:i-1,h=n?1:-1,m=t<0||0===t&&1/t<0?1:0;for(isNaN(t=Math.abs(t))||t===1/0?(a=isNaN(t)?1:0,o=l):(o=Math.floor(Math.log(t)/Math.LN2),t*(c=Math.pow(2,-o))<1&&(o--,c*=2),o+d>=1?t+=p/c:t+=p*Math.pow(2,1-d),t*c>=2&&(o++,c/=2),o+d>=l?(a=0,o=l):o+d>=1?(a=(t*c-1)*Math.pow(2,s),o+=d):(a=t*Math.pow(2,d-1)*Math.pow(2,s),o=0));s>=8;e[r+f]=255&a,f+=h,a/=256,s-=8);for(o=o<<s|a,u+=s;u>0;e[r+f]=255&o,f+=h,o/=256,u-=8);e[r+f-h]|=128*m}}},r={};function n(e){var s=r[e];if(void 0!==s)return s.exports;var i=r[e]={exports:{}},o=!0;try{t[e](i,i.exports,n),o=!1}finally{o&&delete r[e]}return i.exports}n.ab="//";var s=n(72);e.exports=s}()},3107:(e,t,r)=>{"use strict";r.d(t,{A:()=>x});var n=r(6540),s=r(961);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var o=function(e,t){void 0===t&&(t=!0),(0,n.useEffect)(function(){if(t){var r=function(t){"Escape"===t.key&&e(t)};return document.addEventListener("keyup",r),function(){t&&document.removeEventListener("keyup",r)}}},[e,t])},a=function(e,t){void 0===t&&(t=!0),(0,n.useEffect)(function(){if(t){var r=function(){e()};return window.addEventListener("resize",r),function(){t&&window.removeEventListener("resize",r)}}},[e,t])},c=function(e,t,r){void 0===r&&(r=!0),(0,n.useEffect)(function(){if(r){var n=function(r){var n=Array.isArray(e)?e:[e],s=!1;n.forEach(function(e){if(!e.current||e.current.contains(r.target)){s=!0;return}}),r.stopPropagation(),s||t(r)};return document.addEventListener("mousedown",n),document.addEventListener("touchstart",n),function(){r&&(document.removeEventListener("mousedown",n),document.removeEventListener("touchstart",n))}}},[e,t,r])},u=function(e,t){void 0===t&&(t=!0),(0,n.useEffect)(function(){if(t){var r=function(t){if(9===t.keyCode){var r,n=null==e?void 0:null===(r=e.current)||void 0===r?void 0:r.querySelectorAll('a[href], area[href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), button:not([disabled]), [tabindex="0"]'),s=Array.prototype.slice.call(n);if(1===s.length){t.preventDefault();return}var i=s[0],o=s[s.length-1];t.shiftKey&&document.activeElement===i?(t.preventDefault(),o.focus()):document.activeElement===o&&(t.preventDefault(),i.focus())}};return document.addEventListener("keydown",r),function(){t&&document.removeEventListener("keydown",r)}}},[e,t])},l="undefined"!=typeof window?n.useLayoutEffect:n.useEffect,d={popupContent:{tooltip:{position:"absolute",zIndex:999},modal:{position:"relative",margin:"auto"}},popupArrow:{height:"8px",width:"16px",position:"absolute",background:"transparent",color:"#FFF",zIndex:-1},overlay:{tooltip:{position:"fixed",top:"0",bottom:"0",left:"0",right:"0",zIndex:999},modal:{position:"fixed",top:"0",bottom:"0",left:"0",right:"0",display:"flex",zIndex:999}}},p=["top left","top center","top right","right top","right center","right bottom","bottom left","bottom center","bottom right","left top","left center","left bottom"],f=function(e,t,r,n,s){var i=s.offsetX,o=s.offsetY,a=n?8:0,c=r.split(" "),u=e.top+e.height/2,l=e.left+e.width/2,d=t.height,p=t.width,f=u-d/2,h=l-p/2,m="",y="0%",g="0%";switch(c[0]){case"top":f-=d/2+e.height/2+a,m="rotate(180deg)  translateX(50%)",y="100%",g="50%";break;case"bottom":f+=d/2+e.height/2+a,m="rotate(0deg) translateY(-100%) translateX(-50%)",g="50%";break;case"left":h-=p/2+e.width/2+a,m=" rotate(90deg)  translateY(50%) translateX(-25%)",g="100%",y="50%";break;case"right":h+=p/2+e.width/2+a,m="rotate(-90deg)  translateY(-150%) translateX(25%)",y="50%"}switch(c[1]){case"top":f=e.top,y=e.height/2+"px";break;case"bottom":f=e.top-d+e.height,y=d-e.height/2+"px";break;case"left":h=e.left,g=e.width/2+"px";break;case"right":h=e.left-p+e.width,g=p-e.width/2+"px"}return{top:f="top"===c[0]?f-o:f+o,left:h="left"===c[0]?h-i:h+i,transform:m,arrowLeft:g,arrowTop:y}},h=function(e){var t={top:0,left:0,width:window.innerWidth,height:window.innerHeight};if("string"==typeof e){var r=document.querySelector(e);null!==r&&(t=r.getBoundingClientRect())}return t},m=function(e,t,r,n,s,i){var o=s.offsetX,a=s.offsetY,c={arrowLeft:"0%",arrowTop:"0%",left:0,top:0,transform:"rotate(135deg)"},u=0,l=h(i),d=Array.isArray(r)?r:[r];for((i||Array.isArray(r))&&(d=[].concat(d,p));u<d.length;){var m={top:(c=f(e,t,d[u],n,{offsetX:o,offsetY:a})).top,left:c.left,width:t.width,height:t.height};if(m.top<=l.top||m.left<=l.left||m.top+m.height>=l.top+l.height||m.left+m.width>=l.left+l.width)u++;else break}return c},y=0,g=function(){var e=document.getElementById("popup-root");return null===e&&((e=document.createElement("div")).setAttribute("id","popup-root"),document.body.appendChild(e)),e};let x=(0,n.forwardRef)(function(e,t){var r=e.trigger,p=void 0===r?null:r,f=e.onOpen,h=void 0===f?function(){}:f,x=e.onClose,b=void 0===x?function(){}:x,w=e.defaultOpen,v=e.open,E=void 0===v?void 0:v,S=e.disabled,P=void 0!==S&&S,O=e.nested,$=void 0!==O&&O,A=e.closeOnDocumentClick,M=void 0===A||A,I=e.repositionOnResize,C=e.closeOnEscape,k=e.on,T=void 0===k?["click"]:k,R=e.contentStyle,N=void 0===R?{}:R,D=e.arrowStyle,U=void 0===D?{}:D,j=e.overlayStyle,F=e.className,_=void 0===F?"":F,L=e.position,z=void 0===L?"bottom center":L,B=e.modal,K=void 0!==B&&B,V=e.lockScroll,H=void 0!==V&&V,q=e.arrow,G=void 0===q||q,W=e.offsetX,X=void 0===W?0:W,Y=e.offsetY,J=void 0===Y?0:Y,Z=e.mouseEnterDelay,Q=void 0===Z?100:Z,ee=e.mouseLeaveDelay,et=void 0===ee?100:ee,er=e.keepTooltipInside,en=void 0!==er&&er,es=e.children,ei=(0,n.useState)(E||void 0!==w&&w),eo=ei[0],ea=ei[1],ec=(0,n.useRef)(null),eu=(0,n.useRef)(null),el=(0,n.useRef)(null),ed=(0,n.useRef)(null),ep=(0,n.useRef)("popup-"+ ++y),ef=!!K||!p,eh=(0,n.useRef)(0);l(function(){return eo?(ed.current=document.activeElement,eP(),eS(),ev()):eE(),function(){clearTimeout(eh.current)}},[eo]),(0,n.useEffect)(function(){"boolean"==typeof E&&(E?em():ey())},[E,P]);var em=function(e){eo||P||(ea(!0),setTimeout(function(){return h(e)},0))},ey=function(e){var t;eo&&!P&&(ea(!1),ef&&(null===(t=ed.current)||void 0===t||t.focus()),setTimeout(function(){return b(e)},0))},eg=function(e){null==e||e.stopPropagation(),eo?ey(e):em(e)},ex=function(e){clearTimeout(eh.current),eh.current=setTimeout(function(){return em(e)},Q)},eb=function(e){null==e||e.preventDefault(),eg()},ew=function(e){clearTimeout(eh.current),eh.current=setTimeout(function(){return ey(e)},et)},ev=function(){ef&&H&&(document.getElementsByTagName("body")[0].style.overflow="hidden")},eE=function(){ef&&H&&(document.getElementsByTagName("body")[0].style.overflow="auto")},eS=function(){var e,t=null==eu?void 0:null===(e=eu.current)||void 0===e?void 0:e.querySelectorAll('a[href], area[href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), button:not([disabled]), [tabindex="0"]'),r=Array.prototype.slice.call(t)[0];null==r||r.focus()};(0,n.useImperativeHandle)(t,function(){return{open:function(){em()},close:function(){ey()},toggle:function(){eg()}}});var eP=function(){if(!ef&&eo&&(null==ec?void 0:ec.current)&&(null==ec?void 0:ec.current)&&(null==eu?void 0:eu.current)){var e,t,r=m(ec.current.getBoundingClientRect(),eu.current.getBoundingClientRect(),z,G,{offsetX:X,offsetY:J},en);eu.current.style.top=r.top+window.scrollY+"px",eu.current.style.left=r.left+window.scrollX+"px",G&&el.current&&(el.current.style.transform=r.transform,el.current.style.setProperty("-ms-transform",r.transform),el.current.style.setProperty("-webkit-transform",r.transform),el.current.style.top=(null===(e=U.top)||void 0===e?void 0:e.toString())||r.arrowTop,el.current.style.left=(null===(t=U.left)||void 0===t?void 0:t.toString())||r.arrowLeft)}};o(ey,void 0===C||C),u(eu,eo&&ef),a(eP,void 0===I||I),c(p?[eu,ec]:[eu],ey,M&&!$);var eO=function(){var e=ef?d.popupContent.modal:d.popupContent.tooltip,t={className:"popup-content "+(""!==_?_.split(" ").map(function(e){return e+"-content"}).join(" "):""),style:i({},e,N,{pointerEvents:"auto"}),ref:eu,onClick:function(e){e.stopPropagation()}};return!K&&T.indexOf("hover")>=0&&(t.onMouseEnter=ex,t.onMouseLeave=ew),t},e$=function(){return n.createElement("div",Object.assign({},eO(),{key:"C",role:ef?"dialog":"tooltip",id:ep.current}),G&&!ef&&n.createElement("div",{ref:el,style:d.popupArrow},n.createElement("svg",{"data-testid":"arrow",className:"popup-arrow "+(""!==_?_.split(" ").map(function(e){return e+"-arrow"}).join(" "):""),viewBox:"0 0 32 16",style:i({position:"absolute"},U)},n.createElement("path",{d:"M16 0l16 16H0z",fill:"currentcolor"}))),es&&"function"==typeof es?es(ey,eo):es)},eA=!(T.indexOf("hover")>=0),eM=ef?d.overlay.modal:d.overlay.tooltip,eI=[eA&&n.createElement("div",{key:"O","data-testid":"overlay","data-popup":ef?"modal":"tooltip",className:"popup-overlay "+(""!==_?_.split(" ").map(function(e){return e+"-overlay"}).join(" "):""),style:i({},eM,void 0===j?{}:j,{pointerEvents:M&&$||ef?"auto":"none"}),onClick:M&&$?ey:void 0,tabIndex:-1},ef&&e$()),!ef&&e$()];return n.createElement(n.Fragment,null,function(){for(var e={key:"T",ref:ec,"aria-describedby":ep.current},t=Array.isArray(T)?T:[T],r=0,s=t.length;r<s;r++)switch(t[r]){case"click":e.onClick=eg;break;case"right-click":e.onContextMenu=eb;break;case"hover":e.onMouseEnter=ex,e.onMouseLeave=ew;break;case"focus":e.onFocus=ex,e.onBlur=ew}if("function"==typeof p){var i=p(eo);return!!p&&n.cloneElement(i,e)}return!!p&&n.cloneElement(p,e)}(),eo&&s.createPortal(eI,g()))})},7983:e=>{let t=/^[-+]?0x[a-fA-F0-9]+$/,r=/^([\-\+])?(0*)(\.[0-9]+([eE]\-?[0-9]+)?|[0-9]+(\.[0-9]+([eE]\-?[0-9]+)?)?)$/;!Number.parseInt&&window.parseInt&&(Number.parseInt=window.parseInt),!Number.parseFloat&&window.parseFloat&&(Number.parseFloat=window.parseFloat);let n={hex:!0,leadingZeros:!0,decimalPoint:".",eNotation:!0};e.exports=function(e,s={}){if(s=Object.assign({},n,s),!e||"string"!=typeof e)return e;let i=e.trim();if(void 0!==s.skipLike&&s.skipLike.test(i))return e;if(s.hex&&t.test(i))return Number.parseInt(i,16);{let t=r.exec(i);if(!t)return e;{var o;let r=t[1],n=t[2],a=((o=t[3])&&-1!==o.indexOf(".")&&("."===(o=o.replace(/0+$/,""))?o="0":"."===o[0]?o="0"+o:"."===o[o.length-1]&&(o=o.substr(0,o.length-1))),o),c=t[4]||t[6];if(!s.leadingZeros&&n.length>0&&r&&"."!==i[2])return e;{if(!s.leadingZeros&&n.length>0&&!r&&"."!==i[1])return e;let t=Number(i),o=""+t;return -1!==o.search(/[eE]/)||c?s.eNotation?t:e:-1!==i.indexOf(".")?"0"===o&&""===a?t:o===a?t:r&&o==="-"+a?t:e:n?a===o?t:r+a===o?t:e:i===o?t:i===r+o?t:e}}}}},1635:(e,t,r)=>{"use strict";function n(e,t,r,n){return new(r||(r=Promise))(function(s,i){function o(e){try{c(n.next(e))}catch(e){i(e)}}function a(e){try{c(n.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?s(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(o,a)}c((n=n.apply(e,t||[])).next())})}function s(e,t){var r,n,s,i={label:0,sent:function(){if(1&s[0])throw s[1];return s[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=a(0),o.throw=a(1),o.return=a(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(a){return function(c){return function(a){if(r)throw TypeError("Generator is already executing.");for(;o&&(o=0,a[0]&&(i=0)),i;)try{if(r=1,n&&(s=2&a[0]?n.return:a[0]?n.throw||((s=n.return)&&s.call(n),0):n.next)&&!(s=s.call(n,a[1])).done)return s;switch(n=0,s&&(a=[2&a[0],s.value]),a[0]){case 0:case 1:s=a;break;case 4:return i.label++,{value:a[1],done:!1};case 5:i.label++,n=a[1],a=[0];continue;case 7:a=i.ops.pop(),i.trys.pop();continue;default:if(!(s=(s=i.trys).length>0&&s[s.length-1])&&(6===a[0]||2===a[0])){i=0;continue}if(3===a[0]&&(!s||a[1]>s[0]&&a[1]<s[3])){i.label=a[1];break}if(6===a[0]&&i.label<s[1]){i.label=s[1],s=a;break}if(s&&i.label<s[2]){i.label=s[2],i.ops.push(a);break}s[2]&&i.ops.pop(),i.trys.pop();continue}a=t.call(e,i)}catch(e){a=[6,e],n=0}finally{r=s=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,c])}}}function i(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}r.d(t,{Ju:()=>i,YH:()=>s,sH:()=>n}),Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError}}]);