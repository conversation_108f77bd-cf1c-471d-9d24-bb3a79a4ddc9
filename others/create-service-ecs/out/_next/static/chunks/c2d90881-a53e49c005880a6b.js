"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[948],{907:(e,a,o)=>{o.d(a,{$Y:()=>i,En:()=>d,lw:()=>u,ub:()=>w});var t=o(5469),r=o(7823),n=o(4383),l=o(5268),c=o(3318),s=o(5378);let i=async(e,a)=>{let o;let t=(0,n.lI)(e,a);t.bp("/2013-04-01/hostedzone/{HostedZoneId}/rrset"),t.p("HostedZoneId",()=>e.HostedZoneId,"{HostedZoneId}",!1),o=oe;let l=new r.C(e6);return l.a("xmlns","https://route53.amazonaws.com/doc/2013-04-01/"),null!=e[e3]&&l.c(eA(e[e3],a).n(e3)),o+=l.toString(),t.m("POST").h({"content-type":"application/xml"}).b(o),t.build()},u=async(e,a)=>{let o;let t=(0,n.lI)(e,a);t.bp("/2013-04-01/hostedzone");let r=(0,l.Tj)({[a7]:[,e[a$]],[a4]:[()=>void 0!==e.MaxItems,()=>e[aj].toString()],[a2]:[,e[an]],[a8]:[,e[ah]]});return t.m("GET").h({}).q(r).b(o),t.build()},d=async(e,a)=>{if(200!==e.statusCode&&e.statusCode>=300)return y(e,a);let o=(0,l.Tj)({$metadata:eB(e)}),r=(0,l.Y0)((0,l.Xk)(await (0,t.t_)(e.body,a)),"body");return null!=r[e2]&&(o[e2]=eV(r[e2],a)),o},w=async(e,a)=>{if(200!==e.statusCode&&e.statusCode>=300)return y(e,a);let o=(0,l.Tj)({$metadata:eB(e)}),r=(0,l.Y0)((0,l.Xk)(await (0,t.t_)(e.body,a)),"body");return""===r.HostedZones?o[aC]=[]:null!=r[aC]&&null!=r[aC][aE]&&(o[aC]=eG((0,l.Yd)(r[aC][aE]),a)),null!=r[aT]&&(o[aT]=(0,l.yG)(r[aT])),null!=r[a$]&&(o[a$]=(0,l.lK)(r[a$])),null!=r[aj]&&(o[aj]=(0,l.xW)(r[aj])),null!=r[aP]&&(o[aP]=(0,l.lK)(r[aP])),o},y=async(e,a)=>{let o={...e,body:await (0,t.FI)(e.body,a)},r=(0,t.FZ)(e,o.body);switch(r){case"ConcurrentModification":case"com.amazonaws.route53#ConcurrentModification":throw await T(o,a);case"InvalidInput":case"com.amazonaws.route53#InvalidInput":throw await V(o,a);case"InvalidKMSArn":case"com.amazonaws.route53#InvalidKMSArn":throw await q(o,a);case"InvalidKeySigningKeyStatus":case"com.amazonaws.route53#InvalidKeySigningKeyStatus":throw await U(o,a);case"InvalidSigningStatus":case"com.amazonaws.route53#InvalidSigningStatus":throw await W(o,a);case"NoSuchKeySigningKey":case"com.amazonaws.route53#NoSuchKeySigningKey":throw await ei(o,a);case"ConflictingDomainExists":case"com.amazonaws.route53#ConflictingDomainExists":throw await g(o,a);case"InvalidVPCId":case"com.amazonaws.route53#InvalidVPCId":throw await Y(o,a);case"LimitsExceeded":case"com.amazonaws.route53#LimitsExceeded":throw await ee(o,a);case"NoSuchHostedZone":case"com.amazonaws.route53#NoSuchHostedZone":throw await es(o,a);case"NotAuthorizedException":case"com.amazonaws.route53#NotAuthorizedException":throw await ey(o,a);case"PriorRequestNotComplete":case"com.amazonaws.route53#PriorRequestNotComplete":throw await em(o,a);case"PublicZoneVPCAssociation":case"com.amazonaws.route53#PublicZoneVPCAssociation":throw await eE(o,a);case"CidrBlockInUseException":case"com.amazonaws.route53#CidrBlockInUseException":throw await E(o,a);case"CidrCollectionVersionMismatchException":case"com.amazonaws.route53#CidrCollectionVersionMismatchException":throw await C(o,a);case"NoSuchCidrCollectionException":case"com.amazonaws.route53#NoSuchCidrCollectionException":throw await eo(o,a);case"InvalidChangeBatch":case"com.amazonaws.route53#InvalidChangeBatch":throw await Z(o,a);case"NoSuchHealthCheck":case"com.amazonaws.route53#NoSuchHealthCheck":throw await ec(o,a);case"ThrottlingException":case"com.amazonaws.route53#ThrottlingException":throw await eh(o,a);case"CidrCollectionAlreadyExistsException":case"com.amazonaws.route53#CidrCollectionAlreadyExistsException":throw await b(o,a);case"HealthCheckAlreadyExists":case"com.amazonaws.route53#HealthCheckAlreadyExists":throw await I(o,a);case"TooManyHealthChecks":case"com.amazonaws.route53#TooManyHealthChecks":throw await eC(o,a);case"DelegationSetNotAvailable":case"com.amazonaws.route53#DelegationSetNotAvailable":throw await z(o,a);case"DelegationSetNotReusable":case"com.amazonaws.route53#DelegationSetNotReusable":throw await $(o,a);case"HostedZoneAlreadyExists":case"com.amazonaws.route53#HostedZoneAlreadyExists":throw await P(o,a);case"InvalidDomainName":case"com.amazonaws.route53#InvalidDomainName":throw await k(o,a);case"NoSuchDelegationSet":case"com.amazonaws.route53#NoSuchDelegationSet":throw await en(o,a);case"TooManyHostedZones":case"com.amazonaws.route53#TooManyHostedZones":throw await eT(o,a);case"InvalidArgument":case"com.amazonaws.route53#InvalidArgument":throw await L(o,a);case"InvalidKeySigningKeyName":case"com.amazonaws.route53#InvalidKeySigningKeyName":throw await F(o,a);case"KeySigningKeyAlreadyExists":case"com.amazonaws.route53#KeySigningKeyAlreadyExists":throw await _(o,a);case"TooManyKeySigningKeys":case"com.amazonaws.route53#TooManyKeySigningKeys":throw await eg(o,a);case"InsufficientCloudWatchLogsResourcePolicy":case"com.amazonaws.route53#InsufficientCloudWatchLogsResourcePolicy":throw await D(o,a);case"NoSuchCloudWatchLogsLogGroup":case"com.amazonaws.route53#NoSuchCloudWatchLogsLogGroup":throw await er(o,a);case"QueryLoggingConfigAlreadyExists":case"com.amazonaws.route53#QueryLoggingConfigAlreadyExists":throw await eb(o,a);case"DelegationSetAlreadyCreated":case"com.amazonaws.route53#DelegationSetAlreadyCreated":throw await M(o,a);case"DelegationSetAlreadyReusable":case"com.amazonaws.route53#DelegationSetAlreadyReusable":throw await S(o,a);case"HostedZoneNotFound":case"com.amazonaws.route53#HostedZoneNotFound":throw await x(o,a);case"InvalidTrafficPolicyDocument":case"com.amazonaws.route53#InvalidTrafficPolicyDocument":throw await B(o,a);case"TooManyTrafficPolicies":case"com.amazonaws.route53#TooManyTrafficPolicies":throw await eK(o,a);case"TrafficPolicyAlreadyExists":case"com.amazonaws.route53#TrafficPolicyAlreadyExists":throw await ez(o,a);case"NoSuchTrafficPolicy":case"com.amazonaws.route53#NoSuchTrafficPolicy":throw await ed(o,a);case"TooManyTrafficPolicyInstances":case"com.amazonaws.route53#TooManyTrafficPolicyInstances":throw await eM(o,a);case"TrafficPolicyInstanceAlreadyExists":case"com.amazonaws.route53#TrafficPolicyInstanceAlreadyExists":throw await e$(o,a);case"TooManyTrafficPolicyVersionsForCurrentPolicy":case"com.amazonaws.route53#TooManyTrafficPolicyVersionsForCurrentPolicy":throw await eS(o,a);case"TooManyVPCAssociationAuthorizations":case"com.amazonaws.route53#TooManyVPCAssociationAuthorizations":throw await ef(o,a);case"KeySigningKeyInParentDSRecord":case"com.amazonaws.route53#KeySigningKeyInParentDSRecord":throw await O(o,a);case"KeySigningKeyInUse":case"com.amazonaws.route53#KeySigningKeyInUse":throw await Q(o,a);case"CidrCollectionInUseException":case"com.amazonaws.route53#CidrCollectionInUseException":throw await h(o,a);case"HealthCheckInUse":case"com.amazonaws.route53#HealthCheckInUse":throw await N(o,a);case"HostedZoneNotEmpty":case"com.amazonaws.route53#HostedZoneNotEmpty":throw await A(o,a);case"NoSuchQueryLoggingConfig":case"com.amazonaws.route53#NoSuchQueryLoggingConfig":throw await eu(o,a);case"DelegationSetInUse":case"com.amazonaws.route53#DelegationSetInUse":throw await f(o,a);case"TrafficPolicyInUse":case"com.amazonaws.route53#TrafficPolicyInUse":throw await ej(o,a);case"NoSuchTrafficPolicyInstance":case"com.amazonaws.route53#NoSuchTrafficPolicyInstance":throw await ew(o,a);case"VPCAssociationAuthorizationNotFound":case"com.amazonaws.route53#VPCAssociationAuthorizationNotFound":throw await eI(o,a);case"DNSSECNotFound":case"com.amazonaws.route53#DNSSECNotFound":throw await j(o,a);case"LastVPCAssociation":case"com.amazonaws.route53#LastVPCAssociation":throw await X(o,a);case"VPCAssociationNotFound":case"com.amazonaws.route53#VPCAssociationNotFound":throw await eN(o,a);case"HostedZonePartiallyDelegated":case"com.amazonaws.route53#HostedZonePartiallyDelegated":throw await v(o,a);case"KeySigningKeyWithActiveStatusNotFound":case"com.amazonaws.route53#KeySigningKeyWithActiveStatusNotFound":throw await J(o,a);case"NoSuchChange":case"com.amazonaws.route53#NoSuchChange":throw await ea(o,a);case"NoSuchGeoLocation":case"com.amazonaws.route53#NoSuchGeoLocation":throw await el(o,a);case"IncompatibleVersion":case"com.amazonaws.route53#IncompatibleVersion":throw await H(o,a);case"HostedZoneNotPrivate":case"com.amazonaws.route53#HostedZoneNotPrivate":throw await R(o,a);case"NoSuchCidrLocationException":case"com.amazonaws.route53#NoSuchCidrLocationException":throw await et(o,a);case"InvalidPaginationToken":case"com.amazonaws.route53#InvalidPaginationToken":throw await G(o,a);case"HealthCheckVersionMismatch":case"com.amazonaws.route53#HealthCheckVersionMismatch":throw await p(o,a);case"ConflictingTypes":case"com.amazonaws.route53#ConflictingTypes":throw await K(o,a);default:return m({output:e,parsedBody:o.body.Error,errorCode:r})}},m=(0,l.jr)(s.M),E=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[aN]&&(o[aN]=(0,l.lK)(t[aN]));let r=new c.JR({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},b=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[aN]&&(o[aN]=(0,l.lK)(t[aN]));let r=new c.Eq({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},h=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[aN]&&(o[aN]=(0,l.lK)(t[aN]));let r=new c.ol({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},C=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[aN]&&(o[aN]=(0,l.lK)(t[aN]));let r=new c.il({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},T=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.wt({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},g=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.Pv({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},K=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.ci({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},M=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.$v({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},S=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.gK({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},f=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.b$({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},z=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.fz({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},$=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.n2({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},j=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.co({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},I=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.jq({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},N=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.ts({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},p=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.d3({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},P=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.Qp({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},A=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.an({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},x=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.jS({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},R=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.WC({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},v=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.R9({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},H=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.sR({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},D=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.TW({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},L=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.f0({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},Z=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9])),""===t.messages?o[a6]=[]:null!=t[a6]&&null!=t[a6][aN]&&(o[a6]=eF((0,l.Yd)(t[a6][aN]),a));let r=new c.oJ({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},k=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.f({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},V=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.o9({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},F=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.sS({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},U=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.BV({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},q=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.S8({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},G=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.tg({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},W=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.$P({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},B=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.sq({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},Y=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.ES({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},_=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.Od({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},O=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.hO({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},Q=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.yw({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},J=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.X7({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},X=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.Nd({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},ee=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.UD({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},ea=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.Zm({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},eo=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[aN]&&(o[aN]=(0,l.lK)(t[aN]));let r=new c.OE({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},et=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[aN]&&(o[aN]=(0,l.lK)(t[aN]));let r=new c.pO({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},er=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.bK({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},en=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.ff({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},el=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.Rc({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},ec=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.ls({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},es=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.gD({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},ei=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.Ms({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},eu=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.wo({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},ed=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.qU({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},ew=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.vq({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},ey=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.Y7({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},em=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.$4({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},eE=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.EF({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},eb=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.ky({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},eh=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.x5({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},eC=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.LF({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},eT=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.cn({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},eg=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.ec({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},eK=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.wN({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},eM=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.vk({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},eS=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.Cv({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},ef=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.cC({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},ez=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.io({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},e$=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.H1({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},ej=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.aN({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},eI=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.df({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},eN=async(e,a)=>{let o=(0,l.Tj)({}),t=e.body.Error;null!=t[a9]&&(o[a9]=(0,l.lK)(t[a9]));let r=new c.YJ({$metadata:eB(e),...o});return(0,l.Mw)(r,e.body.Error)},ep=(e,a)=>{let o=new r.C(eO);return null!=e[ab]&&o.c(r.C.of(av,e[ab]).n(ab)),o.cc(e,ar),null!=e[ac]&&o.c(r.C.of(e_,String(e[ac])).n(ac)),o},eP=(e,a)=>{let o=new r.C(aa);return null!=e[eY]&&o.c(r.C.of(e0,e[eY]).n(eY)),null!=e[aU]&&o.c(ek(e[aU],a).n(aU)),o},eA=(e,a)=>{let o=new r.C(e3);return null!=e[eX]&&o.c(r.C.of(ax,e[eX]).n(eX)),o.lc(e,"Changes","Changes",()=>ex(e[ae],a)),o},ex=(e,a)=>e.filter(e=>null!=e).map(e=>eP(e,a).n(aa)),eR=(e,a)=>{let o=new r.C(e9);return null!=e[e8]&&o.c(r.C.of(a3,e[e8]).n(e8)),null!=e[aK]&&o.c(r.C.of(e4,e[aK]).n(aK)),o},ev=(e,a)=>{let o=new r.C(at);return o.cc(e,af),o.cc(e,az),o},eH=(e,a)=>{let o=new r.C(ai);return null!=e[e5]&&o.c(r.C.of(au,e[e5]).n(e5)),null!=e[e1]&&o.c(r.C.of(ad,e[e1]).n(e1)),null!=e[a_]&&o.c(r.C.of(aw,e[a_]).n(a_)),o},eD=(e,a)=>{let o=new r.C(ay);return o.cc(e,eQ),o.cc(e,aS),null!=e[at]&&o.c(ev(e[at],a).n(at)),null!=e[eJ]&&o.c(r.C.of(eJ,String(e[eJ])).n(eJ)),o},eL=(e,a)=>{let o=new r.C(aH);return null!=e[a5]&&o.c(r.C.of(aR,e[a5]).n(a5)),o},eZ=(e,a)=>e.filter(e=>null!=e).map(e=>eL(e,a).n(aH)),ek=(e,a)=>{let o=new r.C(aU);return null!=e[ap]&&o.c(r.C.of(ar,e[ap]).n(ap)),null!=e[aJ]&&o.c(r.C.of(aq,e[aJ]).n(aJ)),null!=e[aO]&&o.c(r.C.of(aZ,e[aO]).n(aO)),null!=e[a1]&&o.c(r.C.of(aF,String(e[a1])).n(a1)),null!=e[aW]&&o.c(r.C.of(aV,e[aW]).n(aW)),null!=e[ai]&&o.c(eH(e[ai],a).n(ai)),null!=e[as]&&o.c(r.C.of(aL,e[as]).n(as)),null!=e[aI]&&o.c(r.C.of(ak,String(e[aI])).n(aI)),null!=e[a0]&&o.c(r.C.of(a0,String(e[a0])).n(a0)),o.lc(e,"ResourceRecords","ResourceRecords",()=>eZ(e[aG],a)),null!=e[eO]&&o.c(ep(e[eO],a).n(eO)),o.cc(e,am),o.cc(e,aX),null!=e[e9]&&o.c(eR(e[e9],a).n(e9)),null!=e[ay]&&o.c(eD(e[ay],a).n(ay)),o},eV=(e,a)=>{let o={};return null!=e[ag]&&(o[ag]=(0,l.lK)(e[ag])),null!=e[aB]&&(o[aB]=(0,l.lK)(e[aB])),null!=e[aY]&&(o[aY]=(0,l.Y0)((0,l.t_)(e[aY]))),null!=e[eX]&&(o[eX]=(0,l.lK)(e[eX])),o},eF=(e,a)=>(e||[]).filter(e=>null!=e).map(e=>(0,l.lK)(e)),eU=(e,a)=>{let o={};return null!=e[ag]&&(o[ag]=(0,l.lK)(e[ag])),null!=e[ap]&&(o[ap]=(0,l.lK)(e[ap])),null!=e[e7]&&(o[e7]=(0,l.lK)(e[e7])),null!=e[ao]&&(o[ao]=eq(e[ao],a)),null!=e[aD]&&(o[aD]=(0,l.V0)(e[aD])),null!=e[aM]&&(o[aM]=eW(e[aM],a)),o},eq=(e,a)=>{let o={};return null!=e[eX]&&(o[eX]=(0,l.lK)(e[eX])),null!=e[aA]&&(o[aA]=(0,l.yG)(e[aA])),o},eG=(e,a)=>(e||[]).filter(e=>null!=e).map(e=>eU(e,a)),eW=(e,a)=>{let o={};return null!=e[aQ]&&(o[aQ]=(0,l.lK)(e[aQ])),null!=e[al]&&(o[al]=(0,l.lK)(e[al])),o},eB=e=>({httpStatusCode:e.statusCode,requestId:e.headers["x-amzn-requestid"]??e.headers["x-amzn-request-id"]??e.headers["x-amz-request-id"],extendedRequestId:e.headers["x-amz-id-2"],cfId:e.headers["x-amz-cf-id"]}),eY="Action",e_="AliasHealthEnabled",eO="AliasTarget",eQ="AWSRegion",eJ="Bias",eX="Comment",e0="ChangeAction",e3="ChangeBatch",e5="ContinentCode",e1="CountryCode",e2="ChangeInfo",e8="CollectionId",e4="CidrLocationNameDefaultAllowed",e7="CallerReference",e9="CidrRoutingConfig",e6="ChangeResourceRecordSetsRequest",ae="Changes",aa="Change",ao="Config",at="Coordinates",ar="DNSName",an="DelegationSetId",al="Description",ac="EvaluateTargetHealth",as="Failover",ai="GeoLocation",au="GeoLocationContinentCode",ad="GeoLocationCountryCode",aw="GeoLocationSubdivisionCode",ay="GeoProximityLocation",am="HealthCheckId",aE="HostedZone",ab="HostedZoneId",ah="HostedZoneType",aC="HostedZones",aT="IsTruncated",ag="Id",aK="LocationName",aM="LinkedService",aS="LocalZoneGroup",af="Latitude",az="Longitude",a$="Marker",aj="MaxItems",aI="MultiValueAnswer",aN="Message",ap="Name",aP="NextMarker",aA="PrivateZone",ax="ResourceDescription",aR="RData",av="ResourceId",aH="ResourceRecord",aD="ResourceRecordSetCount",aL="ResourceRecordSetFailover",aZ="ResourceRecordSetIdentifier",ak="ResourceRecordSetMultiValueAnswer",aV="ResourceRecordSetRegion",aF="ResourceRecordSetWeight",aU="ResourceRecordSet",aq="RRType",aG="ResourceRecords",aW="Region",aB="Status",aY="SubmittedAt",a_="SubdivisionCode",aO="SetIdentifier",aQ="ServicePrincipal",aJ="Type",aX="TrafficPolicyInstanceId",a0="TTL",a3="UUID",a5="Value",a1="Weight",a2="delegationsetid",a8="hostedzonetype",a4="maxitems",a7="marker",a9="message",a6="messages",oe='<?xml version="1.0" encoding="UTF-8"?>'}}]);