(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[332],{7276:(e,r,a)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/",function(){return a(5946)}])},5946:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>en});var t=a(4848),s=a(6540),n=a(5835),o=a(7150),i=a(3107);function l(e){let{onAuthSuccess:r}=e,[a,l]=(0,s.useState)(""),[c,d]=(0,s.useState)(""),[u,h]=(0,s.useState)(""),[p,m]=(0,s.useState)(""),[x,v]=(0,s.useState)(!1),[g,j]=(0,s.useState)(null),f=async e=>{if(e.preventDefault(),!a||!c||!u){m("Por favor, preencha todos os campos.");return}m("");let r=new n.i({region:"us-east-1",credentials:{accessKeyId:a,secretAccessKey:c,sessionToken:u}});try{let e=new o.z({}),a=await r.send(e);j(a),v(!0)}catch(e){console.error(e),m("Credenciais inv\xe1lidas. Por favor, verifique e tente novamente.")}};return(0,t.jsxs)("div",{className:"container",children:[(0,t.jsx)("h1",{children:"Autentica\xe7\xe3o AWS"}),p&&(0,t.jsx)("p",{className:"text-red-500",children:p}),(0,t.jsxs)("form",{onSubmit:f,children:[(0,t.jsxs)("div",{className:"form-group",children:[(0,t.jsx)("label",{htmlFor:"accessKeyId",children:"ID da Chave de Acesso AWS"}),(0,t.jsx)("input",{type:"text",id:"accessKeyId",value:a,onChange:e=>l(e.target.value),required:!0})]}),(0,t.jsxs)("div",{className:"form-group",children:[(0,t.jsx)("label",{htmlFor:"secretAccessKey",children:"Chave de Acesso Secreta AWS"}),(0,t.jsx)("input",{type:"text",id:"secretAccessKey",value:c,onChange:e=>d(e.target.value),required:!0})]}),(0,t.jsxs)("div",{className:"form-group",children:[(0,t.jsx)("label",{htmlFor:"sessionToken",children:"Token de Sess\xe3o AWS"}),(0,t.jsx)("input",{type:"text",id:"sessionToken",value:u,onChange:e=>h(e.target.value),required:!0})]}),(0,t.jsx)("button",{type:"submit",className:"btn",children:"Autenticar"})]}),(0,t.jsx)(i.A,{open:x,modal:!0,closeOnDocumentClick:!1,onClose:()=>v(!1),children:(0,t.jsxs)("div",{className:"modal-card",children:[(0,t.jsx)("h2",{className:"mb-4",children:"Confirmar Conta"}),g&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Account:"})," ",g.Account]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"ARN:"})," ",(0,t.jsx)("span",{className:"arn-wrap",children:g.Arn})]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"UserId:"})," ",g.UserId]}),(0,t.jsx)("p",{className:"mt-4",children:"Deseja prosseguir com essa conta?"}),(0,t.jsxs)("div",{className:"button-group",children:[(0,t.jsx)("button",{onClick:()=>{v(!1),r({accessKeyId:a,secretAccessKey:c,sessionToken:u})},className:"btn btn-success",children:"Sim, Confirmar"}),(0,t.jsx)("button",{onClick:()=>{v(!1),l(""),d(""),h("")},className:"btn btn-secondary",children:"Cancelar"})]})]})]})})]})}function c(e){let{onOperationSelected:r}=e,[a,n]=(0,s.useState)("");return(0,t.jsxs)("div",{className:"container",children:[(0,t.jsx)("h1",{className:"page-title",children:"Escolha a Opera\xe7\xe3o"}),(0,t.jsxs)("div",{className:"form-group",children:[(0,t.jsx)("label",{children:"Selecione uma op\xe7\xe3o:"}),(0,t.jsxs)("select",{className:"dropdown",value:a,onChange:e=>n(e.target.value),children:[(0,t.jsx)("option",{value:"",children:"-- Selecione --"}),(0,t.jsx)("option",{value:"ECS",children:"Criar Servi\xe7o no ECS"}),(0,t.jsx)("option",{value:"Encryption",children:"Criptografar/Descriptografar Arquivo"}),(0,t.jsx)("option",{value:"ECR",children:"Criar Reposit\xf3rio ECR"})]})]}),(0,t.jsx)("button",{className:"btn",onClick:()=>{if(!a){alert("Por favor, selecione uma opera\xe7\xe3o.");return}r(a)},children:"Continuar"})]})}var d=a(8860),u=a(6660),h=a(5887),p=a(1290),m=a(5010),x=a(6411),v=a(4769),g=a(1769),j=a(7279),f=a(5563),C=a(3665),y=a(3097),N=a(8725),S=a(5444),b=a(4670),w=a(5775);async function E(e){try{let r=[],a=null;do{let t=new d.P({sort:"DESC",maxResults:100,nextToken:a}),s=await e.send(t);r.push(...s.taskDefinitionArns||[]),a=s.nextToken}while(a);let t={};for(let e of r){let[r,a]=e.split("/").pop().split(":"),s=parseInt(a,10)||0;(!t[r]||s>t[r].version)&&(t[r]={arn:e,version:s})}return Object.values(t).map(e=>e.arn)}catch(e){throw console.error("Erro ao carregar defini\xe7\xf5es de tarefa:",e),e}}async function A(e){try{let r=new u.N({});return(await e.send(r)).clusterArns}catch(e){throw console.error("Erro ao carregar clusters:",e),e}}async function R(e,r){try{var a,t;let s=new h._({clusters:[r]}),n=await e.send(s),o=null===(a=n.clusters)||void 0===a?void 0:a[0];return(null==o?void 0:null===(t=o.capacityProviders)||void 0===t?void 0:t[0])||null}catch(e){throw console.error("Erro ao obter capacity provider:",e),e}}async function k(e){try{let r=new g.A({});return((await e.send(r)).LoadBalancers||[]).filter(e=>"application"===e.Type)}catch(e){throw console.error("Erro ao carregar Load Balancers:",e),e}}async function T(e,r){try{var a;let t=new j.A({LoadBalancerArn:r}),s=await e.send(t),n=null===(a=s.Listeners)||void 0===a?void 0:a.find(e=>443===e.Port);return n?n.ListenerArn:null}catch(e){throw console.error("Erro ao obter listener:",e),e}}async function I(e){try{let r=new S.c({});return(await e.send(r)).HostedZones}catch(e){throw console.error("Erro ao carregar Hosted Zones:",e),e}}async function P(e,r,a){let t=arguments.length>3&&void 0!==arguments[3]?arguments[3]:10,s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:5e3,n=(arguments.length>5&&arguments[5],arguments.length>6?arguments[6]:void 0);for(let l=0;l<t;l++){var o,i;let t=null===(o=(await e.send(new p.H({cluster:r,services:[a]}))).services)||void 0===o?void 0:o[0];if(n("Tentativa ".concat(l+1,": Servi\xe7o ").concat(a," status: ").concat(null!==(i=null==t?void 0:t.status)&&void 0!==i?i:"N/A")),t&&"ACTIVE"===t.status&&t.runningCount===t.desiredCount||t&&["DRAINING","PROVISIONING"].includes(t.status))return t;await new Promise(e=>setTimeout(e,s))}throw Error("O servi\xe7o ".concat(a," n\xe3o ficou ACTIVE ap\xf3s ").concat(t," tentativas."))}async function D(e,r,a,t,s,n){try{let o=new b.y({HostedZoneId:r,ChangeBatch:{Changes:[{Action:"UPSERT",ResourceRecordSet:{Name:a,Type:"A",AliasTarget:{HostedZoneId:s,DNSName:t,EvaluateTargetHealth:!1}}}]}});await e.send(o),null==n||n("Registro Route53 para ".concat(a," criado/atualizado com sucesso."))}catch(e){null==n||n("Erro ao criar registro Route53 para ".concat(a,": ").concat(e.message)),console.error("Erro no registro Route53:",e)}}async function L(e,r,a){try{var t;let s=await e.send(new p.H({cluster:r,services:[a]})),n=null===(t=s.services)||void 0===t?void 0:t[0];if(!n)return{healthy:!1,message:"Servi\xe7o n\xe3o encontrado."};if("ACTIVE"===n.status&&n.runningCount===n.desiredCount)return{healthy:!0,message:"O servi\xe7o est\xe1 ativo e saud\xe1vel."};if(n.runningCount<n.desiredCount)return{healthy:!1,message:"O servi\xe7o est\xe1 inicializando. Verifique os logs da tarefa no ECS se o problema persistir."};if(["DRAINING","PROVISIONING"].includes(n.status))return{healthy:!1,message:"O servi\xe7o est\xe1 em estado: ".concat(n.status,". Consulte os logs da tarefa no ECS.")};return{healthy:!1,message:"Status do servi\xe7o: ".concat(n.status,". Verifique os logs da tarefa no ECS.")}}catch(e){return console.error("Erro ao verificar status do servi\xe7o:",e),{healthy:!1,message:"Erro: ".concat(e.message)}}}async function B(e){let{ecsClient:r,elbClient:a,logsClient:t,route53Client:s,selectedTasks:n,selectedCluster:o,capacityProvider:i,selectedLB:l,listenerArn:c,selectedZone:d,healthCheckPath:u,desiredCount:h,environment:g,logGroupNames:j,progressCallback:S}=e,b=d&&(d.Name||d.name);if(!b)throw Error("Zona hospedada n\xe3o foi selecionada.");let E=[],A=[],R=e=>{E.push(e),null==S||S(e)};for(let e of n)try{var k,T,I,L,B,V,G,H,O,z,q,F,_,Z,M;R("Iniciando implanta\xe7\xe3o para a tarefa ".concat(e));let n=await r.send(new m.Z({taskDefinition:e})),E=null===(T=n.taskDefinition)||void 0===T?void 0:null===(k=T.containerDefinitions)||void 0===k?void 0:k[0];if(!E)throw Error("Defini\xe7\xe3o de container n\xe3o encontrada.");let U=E.name,K=null===(L=E.portMappings)||void 0===L?void 0:null===(I=L[0])||void 0===I?void 0:I.containerPort,W=j[e]||"/ecs/".concat(g,"/").concat(e.split("/").pop().split(":")[0]);R("Verificando grupo de logs ".concat(W));try{await t.send(new w.B({logGroupName:W})),R("Grupo de logs ".concat(W," criado com sucesso."))}catch(e){R("Grupo de logs ".concat(W," j\xe1 existe ou n\xe3o foi necess\xe1rio cri\xe1-lo."))}let X="",$="ecs-".concat(e.split("/").pop().split(":")[0]).substring(0,32);R("Verificando Target Group ".concat($));try{let e=await a.send(new f.s({Names:[$]}));X=null===(V=e.TargetGroups)||void 0===V?void 0:null===(B=V[0])||void 0===B?void 0:B.TargetGroupArn,R("Target Group ".concat($," encontrado."))}catch(r){R("Target Group ".concat($," n\xe3o encontrado. Criando..."));let e=await a.send(new C.G({Name:$,Protocol:"HTTP",Port:80,VpcId:l.VpcId,HealthCheckProtocol:"HTTP",HealthCheckPath:u,HealthCheckIntervalSeconds:30,HealthCheckTimeoutSeconds:5,HealthyThresholdCount:2,UnhealthyThresholdCount:2,TargetType:"instance"}));X=null===(H=e.TargetGroups)||void 0===H?void 0:null===(G=H[0])||void 0===G?void 0:G.TargetGroupArn,R("Target Group ".concat($," criado."))}let J="",Q="".concat(e.split("/").pop().split(":")[0],".").concat(b).replace(/\.$/,"");R("Verificando regras no ALB para o dom\xednio ".concat(Q));try{let e=await a.send(new y.G({ListenerArn:c})),r=null===(O=e.Rules)||void 0===O?void 0:O.find(e=>{var r,a;let t=null===(r=e.Conditions)||void 0===r?void 0:r.find(e=>"host-header"===e.Field),s=null===(a=e.Conditions)||void 0===a?void 0:a.find(e=>"path-pattern"===e.Field);if(t&&s){let e=t.HostHeaderConfig.Values,r=s.PathPatternConfig.Values;return e.includes(Q)&&r.includes("/*")}return!1});if(r)J=r.RuleArn,R("Regra existente encontrada: ".concat(J));else{let r=(e.Rules||[]).map(e=>parseInt(e.Priority)).filter(e=>!isNaN(e)),t=r.length>0?Math.max(...r)+1:1;R("Criando nova regra com prioridade ".concat(t));let s=await a.send(new N.O({ListenerArn:c,Conditions:[{Field:"host-header",HostHeaderConfig:{Values:[Q]}},{Field:"path-pattern",PathPatternConfig:{Values:["/*"]}}],Actions:[{Type:"forward",ForwardConfig:{TargetGroups:[{TargetGroupArn:X,Weight:1}]}}],Priority:t.toString()}));J=null===(q=s.Rules)||void 0===q?void 0:null===(z=q[0])||void 0===z?void 0:z.RuleArn,R("Regra criada com sucesso: ".concat(J))}}catch(e){R("Erro ao criar ou atualizar a regra: ".concat(e.message)),console.error("Erro ao criar ou atualizar a regra:",e)}try{s&&d.Id&&l.DNSName&&l.CanonicalHostedZoneId?await D(s,d.Id,Q,l.DNSName,l.CanonicalHostedZoneId,R):R("Dados insuficientes para criar registro Route53. Registro n\xe3o ser\xe1 criado.")}catch(e){R("Erro ao criar registro no Route53: ".concat(e.message)),console.error("Erro ao criar registro no Route53:",e)}let Y="",ee=e.split("/").pop().split(":")[0];R("Verificando servi\xe7o ECS ".concat(ee));try{let a=await r.send(new p.H({cluster:o,services:[ee]})),t=null===(F=a.services)||void 0===F?void 0:F[0];if(t&&t.status&&"INACTIVE"!==t.status){"ACTIVE"!==t.status&&(R("Servi\xe7o ".concat(ee," est\xe1 em status ").concat(t.status,". Aguardando ACTIVE...")),await P(r,o,ee,10,5e3,S,R)),R("Servi\xe7o ".concat(ee," j\xe1 existe. Atualizando..."));try{let a=await r.send(new x.k({cluster:o,service:ee,taskDefinition:e,desiredCount:h,capacityProviderStrategy:[{capacityProvider:i,weight:1,base:0}],forceNewDeployment:!0}));Y=null===(_=a.service)||void 0===_?void 0:_.serviceArn,R("Servi\xe7o ".concat(ee," atualizado com sucesso."))}catch(a){if("ServiceNotActiveException"===a.name){R("Servi\xe7o ".concat(ee," n\xe3o est\xe1 ACTIVE. Aguardando...")),await P(r,o,ee,10,5e3,S,R);let a=await r.send(new x.k({cluster:o,service:ee,taskDefinition:e,desiredCount:h,capacityProviderStrategy:[{capacityProvider:i,weight:1,base:0}],forceNewDeployment:!0}));Y=null===(Z=a.service)||void 0===Z?void 0:Z.serviceArn,R("Servi\xe7o ".concat(ee," atualizado com sucesso ap\xf3s aguardar ACTIVE."))}else R("Erro ao atualizar servi\xe7o ".concat(ee,": ").concat(a.message)),console.error("Erro ao atualizar servi\xe7o:",a)}}else{R("Servi\xe7o ".concat(ee," n\xe3o existe. Criando..."));let a=new v.J({cluster:o,serviceName:ee,taskDefinition:e,desiredCount:h,capacityProviderStrategy:[{capacityProvider:i,weight:1,base:0}],loadBalancers:[{targetGroupArn:X,containerName:U,containerPort:K}],deploymentConfiguration:{maximumPercent:200,minimumHealthyPercent:100},enableECSManagedTags:!0,enableExecuteCommand:!0,propagateTags:"TASK_DEFINITION"}),t=await r.send(a);Y=null===(M=t.service)||void 0===M?void 0:M.serviceArn,R("Servi\xe7o ".concat(ee," criado com sucesso."))}}catch(e){R("Erro ao criar ou atualizar o servi\xe7o ".concat(ee,": ").concat(e.message)),console.error("Erro ao criar ou atualizar o servi\xe7o:",e)}A.push({task:e,serviceArn:Y})}catch(r){R("Erro ao implantar servi\xe7o para ".concat(e,": ").concat(r.message)),console.error("Erro ao implantar servi\xe7o para ".concat(e,":"),r),A.push({task:e,error:r.message})}return R("Implanta\xe7\xe3o conclu\xedda."),{results:A,logs:E}}function V(e){let{ecsClient:r,onNext:a}=e,[n,o]=(0,s.useState)([]),[i,l]=(0,s.useState)([]),[c,d]=(0,s.useState)(!0),[u,h]=(0,s.useState)("");return((0,s.useEffect)(()=>{async function e(){try{let e=(await E(r)).sort((e,r)=>{let a=e.split("/").pop().split(":")[0].toLowerCase(),t=r.split("/").pop().split(":")[0].toLowerCase();return a<t?-1:a>t?1:0});o(e)}catch(e){h("Erro ao carregar defini\xe7\xf5es de tarefa.")}finally{d(!1)}}r&&e()},[r]),c)?(0,t.jsx)("p",{children:"Carregando defini\xe7\xf5es de tarefa..."}):u?(0,t.jsx)("p",{className:"text-red-500",children:u}):(0,t.jsxs)("div",{className:"container",children:[(0,t.jsx)("h1",{children:"Selecione as Defini\xe7\xf5es de Tarefa"}),(0,t.jsx)("select",{multiple:!0,className:"dropdown",onChange:e=>{l(Array.from(e.target.selectedOptions,e=>e.value))},size:Math.min(10,n.length),children:n.map(e=>{let[r,a]=e.split("/").pop().split(":");return(0,t.jsxs)("option",{value:e,children:[r," (vers\xe3o ",a,")"]},e)})}),u&&(0,t.jsx)("p",{className:"text-red-500 mt-2",children:u}),(0,t.jsx)("button",{onClick:()=>{if(0===i.length){h("Selecione pelo menos uma defini\xe7\xe3o de tarefa.");return}a(i)},className:"btn",children:"Pr\xf3ximo"})]})}function G(e){let{ecsClient:r,onNext:a}=e,[n,o]=(0,s.useState)([]),[i,l]=(0,s.useState)(""),[c,d]=(0,s.useState)(!0),[u,h]=(0,s.useState)("");(0,s.useEffect)(()=>{async function e(){try{let e=await A(r);o(e)}catch(e){h("Erro ao carregar clusters.")}finally{d(!1)}}r&&e()},[r]);let p=async()=>{if(!i){h("Selecione um cluster.");return}try{let e=await R(r,i);e||(e=window.prompt("Nenhum Capacity Provider associado. Insira o nome do Capacity Provider:")),a({selectedCluster:i,capacityProvider:e})}catch(e){h("Erro ao obter o capacity provider.")}};return c?(0,t.jsx)("p",{children:"Carregando clusters..."}):u?(0,t.jsx)("p",{className:"text-red-500",children:u}):(0,t.jsxs)("div",{className:"container",children:[(0,t.jsx)("h1",{children:"Selecione o Cluster ECS"}),(0,t.jsxs)("select",{className:"dropdown",onChange:e=>{l(e.target.value)},value:i,children:[(0,t.jsx)("option",{value:"",children:"Selecione um cluster"}),n.map(e=>{let r=e.split("/").pop();return(0,t.jsx)("option",{value:e,children:r},e)})]}),u&&(0,t.jsx)("p",{className:"text-red-500 mt-2",children:u}),(0,t.jsx)("button",{onClick:p,className:"btn",children:"Pr\xf3ximo"})]})}function H(e){let{elbClient:r,onNext:a}=e,[n,o]=(0,s.useState)([]),[i,l]=(0,s.useState)(null),[c,d]=(0,s.useState)(""),[u,h]=(0,s.useState)(!0),[p,m]=(0,s.useState)("");(0,s.useEffect)(()=>{async function e(){try{let e=await k(r);o(e)}catch(e){m("Erro ao carregar Load Balancers.")}finally{h(!1)}}r&&e()},[r]);let x=async()=>{if(!i){m("Selecione um ALB.");return}try{let e=await T(r,i.LoadBalancerArn);if(!e){m("Nenhum listener na porta 443 encontrado.");return}d(e),a({selectedLB:i,listenerArn:e})}catch(e){m("Erro ao obter o listener.")}};return u?(0,t.jsx)("p",{children:"Carregando ALBs..."}):p?(0,t.jsx)("p",{className:"text-red-500",children:p}):(0,t.jsxs)("div",{className:"container",children:[(0,t.jsx)("h1",{children:"Selecione o Application Load Balancer (ALB)"}),(0,t.jsxs)("select",{className:"dropdown",onChange:e=>{l(n.find(r=>r.LoadBalancerArn===e.target.value))},value:i?i.LoadBalancerArn:"",children:[(0,t.jsx)("option",{value:"",children:"Selecione um ALB"}),n.map(e=>(0,t.jsx)("option",{value:e.LoadBalancerArn,children:e.DNSName},e.LoadBalancerArn))]}),p&&(0,t.jsx)("p",{className:"text-red-500 mt-2",children:p}),(0,t.jsx)("button",{onClick:x,className:"btn",children:"Pr\xf3ximo"})]})}function O(e){let{route53Client:r,onNext:a}=e,[n,o]=(0,s.useState)([]),[i,l]=(0,s.useState)(null),[c,d]=(0,s.useState)(!0),[u,h]=(0,s.useState)("");return((0,s.useEffect)(()=>{async function e(){try{let e=await I(r);o(e)}catch(e){h("Erro ao carregar zonas hospedadas.")}finally{d(!1)}}r&&e()},[r]),c)?(0,t.jsx)("p",{children:"Carregando zonas hospedadas..."}):u?(0,t.jsx)("p",{className:"text-red-500",children:u}):(0,t.jsxs)("div",{className:"container",children:[(0,t.jsx)("h1",{children:"Selecione a Zona Hospedada (Route 53)"}),(0,t.jsxs)("select",{className:"dropdown",onChange:e=>{l(n.find(r=>r.Id===e.target.value))},value:i?i.Id:"",children:[(0,t.jsx)("option",{value:"",children:"Selecione uma zona hospedada"}),n.map(e=>(0,t.jsx)("option",{value:e.Id,children:e.Name},e.Id))]}),u&&(0,t.jsx)("p",{className:"text-red-500 mt-2",children:u}),(0,t.jsx)("button",{onClick:()=>{if(!i){h("Selecione uma zona hospedada.");return}a(i)},className:"btn",children:"Pr\xf3ximo"})]})}function z(e){let{selectedTasks:r,onNext:a}=e,[n,o]=(0,s.useState)("/health"),[i,l]=(0,s.useState)(1),[c,d]=(0,s.useState)("staging"),[u,h]=(0,s.useState)({}),[p,m]=(0,s.useState)(""),x=(e,r)=>{h(a=>({...a,[e]:r}))};return(0,t.jsxs)("div",{className:"container",children:[(0,t.jsx)("h1",{children:"Configura\xe7\xf5es de Implanta\xe7\xe3o"}),p&&(0,t.jsx)("p",{className:"text-red-500 mb-2",children:p}),(0,t.jsxs)("form",{onSubmit:e=>{if(e.preventDefault(),!n){m("Caminho do health check \xe9 obrigat\xf3rio.");return}m(""),a({healthCheckPath:n,desiredCount:i,environment:c,logGroupNames:u})},children:[(0,t.jsxs)("div",{className:"form-group",children:[(0,t.jsx)("label",{htmlFor:"healthCheckPath",children:"Caminho do Health Check:"}),(0,t.jsx)("input",{type:"text",id:"healthCheckPath",value:n,onChange:e=>o(e.target.value),required:!0})]}),(0,t.jsxs)("div",{className:"form-group",children:[(0,t.jsx)("label",{htmlFor:"desiredCount",children:"N\xfamero de R\xe9plicas:"}),(0,t.jsx)("input",{type:"number",id:"desiredCount",value:i,onChange:e=>l(Number(e.target.value)),required:!0})]}),(0,t.jsxs)("div",{className:"form-group",children:[(0,t.jsx)("label",{htmlFor:"environment",children:"Ambiente:"}),(0,t.jsx)("input",{type:"text",id:"environment",value:c,onChange:e=>d(e.target.value),required:!0})]}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Grupos de Logs para cada Tarefa"}),r.map(e=>{let r="/ecs/".concat(c,"/").concat(e.split("/").pop().split(":")[0]);return(0,t.jsxs)("div",{className:"form-group",children:[(0,t.jsxs)("label",{children:[e,":"]}),(0,t.jsx)("input",{type:"text",placeholder:r,value:u[e]||"",onChange:r=>x(e,r.target.value)})]},e)}),(0,t.jsx)("button",{type:"submit",className:"btn",children:"Pr\xf3ximo"})]})]})}function q(e){let{summary:r,onDeploy:a,onBack:s}=e;if(!r)return null;let{selectedTasks:n,clusterData:o,albData:i,hostedZone:l,deploymentConfig:c,ecrRepository:d}=r,u=o.selectedCluster.split("/").pop(),h=l.Name||l.name;return(0,t.jsxs)("div",{className:"card",children:[(0,t.jsx)("h1",{children:"Resumo da Implanta\xe7\xe3o"}),(0,t.jsxs)("div",{className:"summary-content",children:[(0,t.jsxs)("div",{className:"summary-section",children:[(0,t.jsx)("h3",{children:"Defini\xe7\xf5es de Tarefas Selecionadas"}),(0,t.jsx)("ul",{children:n.map(e=>{let r=e.split("/").pop().split(":")[0],a="".concat(r,".").concat(h).replace(/\.$/,"");return(0,t.jsxs)("li",{children:[e,(0,t.jsx)("br",{}),(0,t.jsxs)("small",{style:{color:"#666"},children:["URL do servi\xe7o: ",(0,t.jsx)("strong",{children:a})]})]},e)})})]}),(0,t.jsxs)("div",{className:"summary-section",children:[(0,t.jsx)("h3",{children:"Cluster"}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Cluster:"})," ",u]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Capacity Provider:"})," ",o.capacityProvider]})]}),(0,t.jsxs)("div",{className:"summary-section",children:[(0,t.jsx)("h3",{children:"Application Load Balancer"}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Nome:"})," ",i.selectedLB.LoadBalancerName]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"DNS:"})," ",i.selectedLB.DNSName]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Listener:"})," ",i.listenerArn]})]}),(0,t.jsxs)("div",{className:"summary-section",children:[(0,t.jsx)("h3",{children:"Zona Hospedada (Route 53)"}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Nome:"})," ",l.Name||l.name]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"ID:"})," ",l.Id]})]}),c&&(0,t.jsxs)("div",{className:"summary-section",children:[(0,t.jsx)("h3",{children:"Configura\xe7\xe3o de Implanta\xe7\xe3o"}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Health Check:"})," ",c.healthCheckPath]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"N\xfamero de R\xe9plicas:"})," ",c.desiredCount]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Ambiente:"})," ",c.environment]}),c.logGroupNames&&Object.keys(c.logGroupNames).length>0&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{children:"Grupos de Logs:"}),(0,t.jsx)("ul",{children:Object.entries(c.logGroupNames).map(e=>{let[r,a]=e;return(0,t.jsxs)("li",{children:[r,": ",a]},r)})})]})]}),d&&(0,t.jsxs)("div",{className:"summary-section",children:[(0,t.jsx)("h3",{children:"Reposit\xf3rio ECR"}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Nome do Reposit\xf3rio:"})," ",d.repositoryName]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"URI:"})," ",d.repositoryUri]}),(0,t.jsx)("p",{children:"(Caso n\xe3o existisse, foi criado automaticamente.)"})]})]}),(0,t.jsxs)("div",{className:"button-group",children:[(0,t.jsx)("button",{onClick:s,className:"btn btn-secondary",children:"Voltar"}),(0,t.jsx)("button",{onClick:a,className:"btn btn-success",children:"Confirmar e Implantar"})]})]})}function F(e){let{result:r,onNewDeploy:a,onDeleteService:n,ecsClient:o,selectedCluster:i}=e,[l,c]=(0,s.useState)("");if(!r)return null;let{results:d,logs:u}=r,h=d.some(e=>e.error),p=async()=>{if(!d||0===d.length){alert("Nenhum servi\xe7o para verificar.");return}let e=d[0];if(!e.serviceArn){alert("Servi\xe7o n\xe3o criado ou com erro.");return}let r=e.serviceArn.split("/"),a=r[r.length-1];c((await L(o,i,a)).message)};return(0,t.jsxs)("div",{className:"card",children:[(0,t.jsx)("h1",{className:"page-title",children:"Resultado da Implanta\xe7\xe3o"}),(0,t.jsxs)("div",{style:{marginTop:"20px"},children:[(0,t.jsx)("h3",{children:"Servi\xe7os Implantados"}),(0,t.jsx)("ul",{children:d.map((e,r)=>{let a=e.task.split("/").pop().split(":")[0],s="https://".concat(a,".staging.magie.services");return(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Tarefa:"})," ",a,(0,t.jsx)("br",{}),e.error?(0,t.jsxs)("span",{style:{color:"red"},children:["Erro: ",e.error]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("strong",{children:"Service ARN:"})," ",e.serviceArn,(0,t.jsx)("br",{}),(0,t.jsxs)("span",{className:"service-url",children:["\uD83D\uDD17 Para acessar sua aplica\xe7\xe3o, visite: ",(0,t.jsx)("br",{}),(0,t.jsx)("a",{href:s,target:"_blank",rel:"noopener noreferrer",children:s})]})]})]},r)})})]}),(0,t.jsxs)("div",{style:{marginTop:"20px"},children:[(0,t.jsx)("h3",{children:"Logs da Implanta\xe7\xe3o"}),(0,t.jsx)("div",{className:"log-container",children:u.map((e,r)=>(0,t.jsx)("p",{className:"log-item",children:e},r))})]}),l&&(0,t.jsx)("div",{style:{marginTop:"20px",fontWeight:"bold",color:l.includes("Erro")?"red":"green"},children:l}),(0,t.jsxs)("div",{className:"button-group",style:{marginTop:"15px"},children:[(0,t.jsx)("button",{className:"btn",onClick:a,children:"Voltar ao in\xedcio"}),(0,t.jsx)("button",{className:"btn",onClick:p,children:"Conferir Status da Aplica\xe7\xe3o"}),h&&n&&(0,t.jsx)("button",{className:"btn btn-danger",onClick:n,children:"Excluir Servi\xe7o"})]})]})}var _=a(2015),Z=a(6232),M=a(8432);let U="arn:aws:kms:us-east-1:992382535149:key/80488b4e-0b97-4ff4-bc23-b0802a38ea1f",K="RSAES_OAEP_SHA_256";function W(e){let{credentials:r,onBack:a}=e,[n,o]=(0,s.useState)(null),[i,l]=(0,s.useState)("encrypt"),[c,d]=(0,s.useState)(!1),[u,h]=(0,s.useState)(""),[p,m]=(0,s.useState)(null),[x,v]=(0,s.useState)(""),g=(0,s.useMemo)(()=>r?new _.p({region:"us-east-1",credentials:r}):null,[r]),j=async e=>{if(e.preventDefault(),h(""),m(null),!n){h("Selecione um arquivo.");return}if("encrypt"===i&&n.size>4096){h("O arquivo excede o limite de 4KB para criptografia direta. Selecione um arquivo menor.");return}if(!g){h("Cliente KMS n\xe3o configurado. Verifique as credenciais.");return}d(!0);try{let e,r;let a=await n.arrayBuffer(),t=new Uint8Array(a);if("encrypt"===i){let a=new Z.b({KeyId:U,Plaintext:t,EncryptionAlgorithm:K}),s=await g.send(a);e=new Blob([s.CiphertextBlob],{type:"application/octet-stream"}),r=n.name+".encrypted"}else{let a=new M.v({CiphertextBlob:t,KeyId:U,EncryptionAlgorithm:K}),s=await g.send(a);e=new Blob([s.Plaintext],{type:"application/octet-stream"}),r=n.name.endsWith(".encrypted")?n.name.slice(0,-10):n.name+".decrypted"}let s=URL.createObjectURL(e);m(s),v(r)}catch(e){h("Erro: "+e.message)}finally{d(!1)}};return(0,t.jsxs)("div",{className:"container encryption-container",children:[(0,t.jsx)("h1",{className:"page-title",children:"Criptografar/Descriptografar Arquivo"}),(0,t.jsxs)("form",{onSubmit:j,className:"encryption-form",children:[(0,t.jsxs)("div",{className:"form-group",children:[(0,t.jsx)("label",{children:"Escolha um arquivo:"}),(0,t.jsx)("input",{type:"file",onChange:e=>{var r;o((null===(r=e.target.files)||void 0===r?void 0:r[0])||null),h(""),m(null)}}),n&&(0,t.jsxs)("p",{className:"file-info",children:[(0,t.jsx)("strong",{children:"Arquivo:"})," ",n.name," ",(0,t.jsxs)("span",{className:"file-size",children:["(",(n.size/1024).toFixed(2)," KB)"]})]})]}),(0,t.jsxs)("div",{className:"form-group operation-group",children:[(0,t.jsx)("label",{children:"Opera\xe7\xe3o:"}),(0,t.jsxs)("div",{className:"radio-group",children:[(0,t.jsxs)("label",{children:[(0,t.jsx)("input",{type:"radio",value:"encrypt",checked:"encrypt"===i,onChange:()=>l("encrypt")}),"Criptografar"]}),(0,t.jsxs)("label",{children:[(0,t.jsx)("input",{type:"radio",value:"decrypt",checked:"decrypt"===i,onChange:()=>l("decrypt")}),"Descriptografar"]})]})]}),(0,t.jsx)("button",{type:"submit",className:"btn",disabled:c,children:c?"Processando...":"encrypt"===i?"Criptografar":"Descriptografar"})]}),u&&(0,t.jsx)("p",{className:"error-msg",children:u}),p&&(0,t.jsxs)("div",{className:"success-box",children:[(0,t.jsx)("p",{children:"Opera\xe7\xe3o conclu\xedda com sucesso!"}),(0,t.jsx)("a",{href:p,download:x,className:"btn",children:"encrypt"===i?"Baixar Arquivo Criptografado":"Baixar Arquivo Descriptografado"})]}),(0,t.jsx)("button",{className:"btn-back",onClick:a,children:"← Voltar"})]})}var X=a(59),$=a(4900);async function J(e,r){if(!e)throw Error("Cliente ECR n\xe3o est\xe1 dispon\xedvel. Verifique suas credenciais e regi\xe3o us-east-1.");try{let r=new n.i({region:"us-east-1",credentials:e.config.credentials}),a=await r.send(new o.z({}));console.log("Conectado \xe0 conta AWS: ".concat(a.Account," (us-east-1)"))}catch(e){throw Error("Erro ao obter identidade AWS: ".concat(e.message))}try{let a=new X.p({repositoryNames:[r]}),t=await e.send(a);if(t.repositories&&t.repositories.length>0)return console.log('Reposit\xf3rio "'.concat(r,'" j\xe1 existia.')),{repo:t.repositories[0],created:!1}}catch(a){if("RepositoryNotFoundException"===a.name)try{let a=new $.W({repositoryName:r,imageTagMutability:"IMMUTABLE",encryptionConfiguration:{encryptionType:"AES256"}}),t=await e.send(a);return console.log('Reposit\xf3rio "'.concat(r,'" criado com sucesso na regi\xe3o us-east-1.')),{repo:t.repository,created:!0}}catch(e){throw console.error('Erro ao criar o reposit\xf3rio ECR "'.concat(r,'":'),e),Error("Erro ao criar o reposit\xf3rio ECR: ".concat(e.message))}throw Error("Erro ao obter ou criar reposit\xf3rio ECR: ".concat(a.message))}throw Error('N\xe3o foi poss\xedvel encontrar ou criar o reposit\xf3rio "'.concat(r,'". Motivo desconhecido.'))}function Q(e){let{ecrClient:r,onNext:a}=e,[n,o]=(0,s.useState)(""),[i,l]=(0,s.useState)(""),[c,d]=(0,s.useState)(""),[u,h]=(0,s.useState)(!1),[p,m]=(0,s.useState)(null),[x,v]=(0,s.useState)(!1);(0,s.useEffect)(()=>{console.log("StepECRRepository - ecrClient:",r)},[r]);let g=async()=>{if(console.log("handleSubmit foi chamado"),!n.trim()){l("Por favor, informe o nome do reposit\xf3rio.");return}if(l(""),d(""),m(null),v(!1),!r){l("Cliente ECR n\xe3o dispon\xedvel. Verifique credenciais e regi\xe3o us-east-1.");return}h(!0),d("Verificando/criando reposit\xf3rio. Aguarde...");try{console.log("Chamando createOrGetECRRepository para:",n);let{repo:e,created:a}=await J(r,n);e&&e.repositoryName?(m(e),v(a),a?d('Reposit\xf3rio "'.concat(e.repositoryName,'" foi criado com sucesso!')):d('Reposit\xf3rio "'.concat(e.repositoryName,'" j\xe1 existe e est\xe1 pronto para uso.')),console.log("Reposit\xf3rio obtido/criado com sucesso:",e)):(l("Reposit\xf3rio retornado est\xe1 vazio ou indefinido."),console.error("Reposit\xf3rio retornado est\xe1 vazio.",e))}catch(e){l("Erro ao criar/obter o reposit\xf3rio: ".concat(e.message)),console.error("Detalhes do erro ECR:",e)}finally{h(!1)}},j=()=>{a&&a(null)};return(0,t.jsxs)("div",{className:"container",children:[(0,t.jsx)("h1",{className:"page-title",children:"Criar/Obter Reposit\xf3rio ECR"}),i&&(0,t.jsx)("p",{className:"text-red-500",children:i}),c&&(0,t.jsx)("p",{className:"text-green-500",children:c}),p?(0,t.jsx)(t.Fragment,{children:(0,t.jsxs)("div",{className:"button-group",style:{marginTop:"20px"},children:[(0,t.jsx)("button",{onClick:()=>{m(null),v(!1),o(""),l(""),d("")},className:"btn",children:"Criar Novo Reposit\xf3rio"}),(0,t.jsx)("button",{onClick:j,className:"btn btn-secondary",style:{marginLeft:"10px"},children:"Voltar para Sele\xe7\xe3o"})]})}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"form-group",children:[(0,t.jsx)("label",{children:"Nome do Reposit\xf3rio"}),(0,t.jsx)("input",{type:"text",value:n,onChange:e=>o(e.target.value),placeholder:"ex: meu-repositorio"})]}),(0,t.jsxs)("div",{className:"button-group",style:{marginTop:"20px"},children:[(0,t.jsx)("button",{onClick:g,className:"btn",disabled:u||!r,children:u?"Aguarde...":"Criar/Obter Reposit\xf3rio"}),(0,t.jsx)("button",{onClick:j,className:"btn btn-secondary",style:{marginLeft:"10px"},children:"Voltar"})]})]})]})}function Y(e){let{logs:r,onRestart:a}=e;return(0,t.jsxs)("div",{className:"container",children:[(0,t.jsx)("h1",{children:"Implanta\xe7\xe3o em Andamento"}),(0,t.jsx)("div",{className:"spinner"}),(0,t.jsx)("div",{className:"log-container",children:r.map((e,r)=>(0,t.jsx)("p",{className:"log-item",children:e},r))}),(0,t.jsx)("button",{onClick:a,className:"btn",children:"Implantar Novo Servi\xe7o"})]})}var ee=a(278),er=a(3182),ea=a(787),et=a(6906),es=a(9616);function en(){let[e,r]=(0,s.useState)(0),[a,n]=(0,s.useState)(""),[o,i]=(0,s.useState)(null),d=function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"us-east-1",[a,t]=(0,s.useState)(null);return(0,s.useEffect)(()=>{if(e){let a={region:r,credentials:e};t({ecsClient:new ee.Z(a),elbClient:new er.M(a),route53Client:new ea.L(a),logsClient:new et.T(a),ecrClient:new es.S(a)})}else t(null)},[e,r]),a}(o),[u,h]=(0,s.useState)([]),[p,m]=(0,s.useState)(null),[x,v]=(0,s.useState)(null),[g,j]=(0,s.useState)(null),[f,C]=(0,s.useState)(null),[y,N]=(0,s.useState)(null),[S,b]=(0,s.useState)(null),[w,E]=(0,s.useState)(!1),[A,R]=(0,s.useState)([]),k=1;"ECS"===a?k=9:"Encryption"===a?k=10:"ECR"===a&&(k=2);let T=async()=>{if(!g||!(g.Name||g.name)){alert("Zona hospedada n\xe3o foi selecionada. Por favor, selecione uma zona hospedada.");return}E(!0),R([]);let e={ecsClient:d.ecsClient,elbClient:d.elbClient,logsClient:d.logsClient,route53Client:d.route53Client,selectedTasks:u,selectedCluster:p.selectedCluster,capacityProvider:p.capacityProvider,selectedLB:x.selectedLB,listenerArn:x.listenerArn,selectedZone:g,healthCheckPath:f.healthCheckPath,desiredCount:f.desiredCount,environment:f.environment,logGroupNames:f.logGroupNames,progressCallback:e=>{R(r=>[...r,e])}};b(await B(e)),E(!1),r(8)};return(0,t.jsxs)("div",{className:"flex flex-col items-center min-h-screen p-4",children:[(0,t.jsx)("h1",{className:"page-title",children:"SRE Tools"}),e>=1&&e<=k&&(0,t.jsx)("div",{className:"step-indicator",children:Array.from({length:k}).map((r,a)=>(0,t.jsx)("div",{className:"step-circle ".concat(a<e?"completed":a===e?"active":""),children:a+1},a))}),w?(0,t.jsx)(Y,{logs:A,onRestart:()=>{r(0),i(null),h([]),m(null),v(null),j(null),C(null),N(null),b(null),E(!1),R([]),n("")}}):(0,t.jsxs)("div",{className:"container",children:[0===e&&(0,t.jsx)(l,{onAuthSuccess:e=>{i(e),r(1)}}),1===e&&(0,t.jsx)(c,{onOperationSelected:e=>{switch(n(e),e){case"ECS":r(2);break;case"Encryption":r(10);break;case"ECR":r(11)}}}),"ECS"===a&&2===e&&d&&(0,t.jsx)(V,{ecsClient:d.ecsClient,onNext:e=>{h(e),r(3)}}),"ECS"===a&&3===e&&d&&(0,t.jsx)(G,{ecsClient:d.ecsClient,onNext:e=>{m(e),r(4)}}),"ECS"===a&&4===e&&d&&(0,t.jsx)(H,{elbClient:d.elbClient,onNext:e=>{v(e),r(5)}}),"ECS"===a&&5===e&&d&&(0,t.jsx)(O,{route53Client:d.route53Client,onNext:e=>{j(e),r(6)}}),"ECS"===a&&6===e&&(0,t.jsx)(z,{selectedTasks:u,onNext:e=>{C(e),N({selectedTasks:u,clusterData:p,albData:x,hostedZone:g,deploymentConfig:e}),r(7)}}),"ECS"===a&&7===e&&y&&(0,t.jsx)(q,{summary:y,onDeploy:T,onBack:()=>r(6)}),"ECS"===a&&8===e&&S&&(0,t.jsx)(F,{result:S,onNewDeploy:()=>{r(1),h([]),b(null),E(!1),R([])},ecsClient:d.ecsClient,selectedCluster:p.selectedCluster,onDeleteService:null}),"Encryption"===a&&10===e&&d&&(0,t.jsx)(W,{credentials:o,onBack:()=>r(1)}),"ECR"===a&&11===e&&d&&(0,t.jsx)(Q,{ecrClient:d.ecrClient,onNext:()=>r(1)})]})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[948,996,463,636,593,792],()=>r(7276)),_N_E=e.O()}]);