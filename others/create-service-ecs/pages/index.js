// pages/index.js
import React, { useState } from 'react';
import StepAuth from '../components/StepAuth';
import OperationChoice from '../components/OperationChoice';
import StepTaskDefinitions from '../components/StepTaskDefinitions';
import StepClusterSelection from '../components/StepClusterSelection';
import StepALBSelection from '../components/StepALBSelection';
import StepHostedZones from '../components/StepHostedZones';
import StepDeploymentConfig from '../components/StepDeploymentConfig';
import StepSummary from '../components/StepSummary';
import StepResult from '../components/StepResult';
import StepFileEncryption from '../components/StepFileEncryption';
import StepECRRepository from '../components/StepECRRepository';
import DeployLoading from '../components/DeployLoading';
import useAwsClients from '../hooks/useAwsClients';
import { deployServices } from '../utils/awsFunctions';

export default function Home() {
  const [step, setStep] = useState(0);
  const [operation, setOperation] = useState(''); // 'ECS', 'Encryption' ou 'ECR'
  const [credentials, setCredentials] = useState(null);
  const clients = useAwsClients(credentials);

  // Estados para o fluxo ECS
  const [selectedTasks, setSelectedTasks] = useState([]);
  const [clusterData, setClusterData] = useState(null); // { selectedCluster, capacityProvider }
  const [albData, setAlbData] = useState(null);         // { selectedLB, listenerArn }
  const [hostedZone, setHostedZone] = useState(null);   // Objeto com .Name ou .name
  const [deploymentConfig, setDeploymentConfig] = useState(null);
  const [summary, setSummary] = useState(null);
  const [deploymentResult, setDeploymentResult] = useState(null);

  // Estados para deploy e logs
  const [isDeploying, setIsDeploying] = useState(false);
  const [deployLogs, setDeployLogs] = useState([]);

  /**
   * Define o total de etapas para a barra de progresso conforme a operação escolhida:
   * - ECS: 9 steps (1 a 9)
   * - Encryption: 2 steps (1 e 2)
   * - ECR: 2 steps (1 e 2)
   */
  let totalSteps = 1;
  if (operation === 'ECS') totalSteps = 9;
  else if (operation === 'Encryption') totalSteps = 2;
  else if (operation === 'ECR') totalSteps = 2;

  // -------------------------------
  // Funções de transição entre steps
  // -------------------------------
  const handleAuthSuccess = (creds) => {
    setCredentials(creds);
    setStep(1); // Após autenticação, vai para a escolha de operação
  };

  // Callback para o dropdown no OperationChoice
  const handleOperationSelected = (choice) => {
    setOperation(choice);
    switch (choice) {
      case 'ECS':
        setStep(2);  // Inicia fluxo ECS a partir do step 2
        break;
      case 'Encryption':
        setStep(10); // Vai para fluxo de Criptografia (StepFileEncryption)
        break;
      case 'ECR':
        setStep(11); // Vai para fluxo de criação de repositório ECR (StepECRRepository)
        break;
      default:
        break;
    }
  };

  // Fluxo ECS
  const handleTasksNext = (tasks) => {
    setSelectedTasks(tasks);
    setStep(3);
  };

  const handleClusterNext = (data) => {
    setClusterData(data);
    setStep(4);
  };

  const handleALBNext = (data) => {
    setAlbData(data);
    setStep(5);
  };

  const handleZoneNext = (zone) => {
    setHostedZone(zone);
    setStep(6);
  };

  const handleDeploymentConfigNext = (config) => {
    setDeploymentConfig(config);
    setSummary({
      selectedTasks,
      clusterData,
      albData,
      hostedZone,
      deploymentConfig: config,
    });
    setStep(7);
  };

  const handleDeploy = async () => {
    if (!hostedZone || !(hostedZone.Name || hostedZone.name)) {
      alert("Zona hospedada não foi selecionada. Por favor, selecione uma zona hospedada.");
      return;
    }
    setIsDeploying(true);
    setDeployLogs([]);

    const progressCallback = (msg) => {
      setDeployLogs((prev) => [...prev, msg]);
    };

    const config = {
      ecsClient: clients.ecsClient,
      elbClient: clients.elbClient,
      logsClient: clients.logsClient,
      route53Client: clients.route53Client,
      selectedTasks,
      selectedCluster: clusterData.selectedCluster,
      capacityProvider: clusterData.capacityProvider,
      selectedLB: albData.selectedLB,
      listenerArn: albData.listenerArn,
      selectedZone: hostedZone,
      healthCheckPath: deploymentConfig.healthCheckPath,
      desiredCount: deploymentConfig.desiredCount,
      environment: deploymentConfig.environment,
      logGroupNames: deploymentConfig.logGroupNames,
      progressCallback,
    };

    const result = await deployServices(config);
    setDeploymentResult(result);
    setIsDeploying(false);
    setStep(8);
  };

  // Reinicia o fluxo inteiro
  const handleRestart = () => {
    setStep(0);
    setCredentials(null);
    setSelectedTasks([]);
    setClusterData(null);
    setAlbData(null);
    setHostedZone(null);
    setDeploymentConfig(null);
    setSummary(null);
    setDeploymentResult(null);
    setIsDeploying(false);
    setDeployLogs([]);
    setOperation('');
  };

  // Reinicia mantendo as credenciais
  const handleNewDeploy = () => {
    setStep(1);
    setSelectedTasks([]);
    setDeploymentResult(null);
    setIsDeploying(false);
    setDeployLogs([]);
  };

  // Função para voltar ao início
  const handleBackToStart = () => {
    setStep(1);
    setOperation('');
  };

  return (
    <div className="flex flex-col items-center min-h-screen p-4">
      <h1 className="page-title">SRE Tools</h1>

      {/* Mostra o indicador de etapas apenas para operação ECS */}
      {operation === 'ECS' && step >= 2 && step <= 8 && (
        <div className="step-indicator">
          {Array.from({ length: 7 }).map((_, index) => (
            <div
              key={index}
              className={`step-circle ${
                index < step - 1 ? 'completed' : index === step - 2 ? 'active' : ''
              }`}
            >
              {index + 1}
            </div>
          ))}
        </div>
      )}

      {isDeploying ? (
        <DeployLoading logs={deployLogs} onRestart={handleRestart} />
      ) : (
        <div className="container">
          {/* Step 0: Autenticação */}
          {step === 0 && <StepAuth onAuthSuccess={handleAuthSuccess} />}

          {/* Step 1: Escolha da operação (Dropdown) */}
          {step === 1 && (
            <OperationChoice onOperationSelected={handleOperationSelected} />
          )}

          {/* Fluxo ECS: Steps 2 a 8 */}
          {operation === 'ECS' && step === 2 && clients && (
            <StepTaskDefinitions 
              ecsClient={clients.ecsClient} 
              onNext={handleTasksNext} 
              onBack={handleBackToStart}
            />
          )}
          {operation === 'ECS' && step === 3 && clients && (
            <StepClusterSelection 
              ecsClient={clients.ecsClient} 
              onNext={handleClusterNext} 
              onBack={() => setStep(2)}
            />
          )}
          {operation === 'ECS' && step === 4 && clients && (
            <StepALBSelection 
              elbClient={clients.elbClient} 
              onNext={handleALBNext} 
              onBack={() => setStep(3)}
            />
          )}
          {operation === 'ECS' && step === 5 && clients && (
            <StepHostedZones 
              route53Client={clients.route53Client} 
              onNext={handleZoneNext} 
              onBack={() => setStep(4)}
            />
          )}
          {operation === 'ECS' && step === 6 && (
            <StepDeploymentConfig 
              selectedTasks={selectedTasks} 
              onNext={handleDeploymentConfigNext} 
              onBack={() => setStep(5)}
            />
          )}
          {operation === 'ECS' && step === 7 && summary && (
            <StepSummary 
              summary={summary} 
              onDeploy={handleDeploy} 
              onBack={() => setStep(6)} 
            />
          )}
          {operation === 'ECS' && step === 8 && deploymentResult && (
            <StepResult
              result={deploymentResult}
              onNewDeploy={handleNewDeploy}
              ecsClient={clients.ecsClient}
              selectedCluster={clusterData.selectedCluster}
              onDeleteService={null}
            />
          )}

          {/* Fluxo Criptografia: Step 10 */}
          {operation === 'Encryption' && step === 10 && clients && (
            <StepFileEncryption 
              credentials={credentials} 
              onBack={handleBackToStart} 
            />
          )}

          {/* Fluxo ECR: Step 11 */}
          {operation === 'ECR' && step === 11 && clients && (
            <StepECRRepository 
              ecrClient={clients.ecrClient} 
              onNext={handleBackToStart} 
            />
          )}
        </div>
      )}
    </div>
  );
}
