export AWS_PROFILE=staging
export USER_POOL_ID=us-east-1_KZG4qHjjM
export CLIENT_ID=207el5106n4cfj59lqdvt2l37t
export CLIENT_SECRET=e1gulv1nk1r8dmuv1bs37o03n8lv73ri2udfihjq8r77fcrpreh
export LOGIN_USERNAME=testuser1

# Create a new user using admin credentials
python cognito_auth.py \
  --user-pool-id $USER_POOL_ID \
  --client-id $CLIENT_ID \
  --client-secret $CLIENT_SECRET \
  --username $LOGIN_USERNAME \
  --password ******** \
  --flow admin-create-user \
  --account-uuid account-uuid-$LOGIN_USERNAME

# User password auth with client secret
python cognito_auth.py \
  --user-pool-id $USER_POOL_ID \
  --client-id $CLIENT_ID \
  --client-secret $CLIENT_SECRET \
  --username $LOGIN_USERNAME \
  --password ******** \
  --flow user-password-auth

# User password auth without client secret
python cognito_auth.py \
  --user-pool-id $USER_POOL_ID \
  --client-id $CLIENT_ID \
  --username $LOGIN_USERNAME \
  --password ******** \
  --flow user-password-auth

# Custom auth flow
python cognito_auth.py \
  --user-pool-id $USER_POOL_ID \
  --client-id $CLIENT_ID \
  --client-secret $CLIENT_SECRET \
  --username $LOGIN_USERNAME \
  --password ******** \
  --flow custom-auth

# Custom auth flow with SRP_A
python cognito_auth.py \
  --user-pool-id $USER_POOL_ID \
  --client-id $CLIENT_ID \
  --client-secret $CLIENT_SECRET \
  --username $LOGIN_USERNAME \
  --password ******** \
  --flow custom-auth-srpa

# Custom auth flow with client_secret
python cognito_auth.py \
  --user-pool-id $USER_POOL_ID \
  --client-id $CLIENT_ID \
  --client-secret $CLIENT_SECRET \
  --username $LOGIN_USERNAME \
  --password ******** \
  --flow full-auth

# Custom auth flow without client_secret
python cognito_auth.py \
  --user-pool-id $USER_POOL_ID \
  --client-id $CLIENT_ID \
  --username $LOGIN_USERNAME \
  --password ******** \
  --flow full-auth

# List users from the pool using CLI
aws cognito-idp list-users --user-pool-id $USER_POOL_ID
