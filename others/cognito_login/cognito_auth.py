import argparse
import boto3
import json
import sys
import hmac
import hashlib
import base64

from botocore.exceptions import ClientError
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.text import Text
from pycognito.aws_srp import AWSSRP

console = Console()

# def get_secret_hash(username, client_id, client_secret):
#     message = username + client_id
#     digest = hmac.new(
#         key=client_secret.encode('utf-8'),
#         msg=message.encode('utf-8'),
#         digestmod=hashlib.sha256
#     ).digest()
#     return base64.b64encode(digest).decode()


def create_cognito_client(region):
    return boto3.client('cognito-idp', region_name=region)


def admin_create_user(client, user_pool_id, username, password, account_uuid):
    try:
        client.admin_create_user(
            UserPoolId=user_pool_id,
            Username=username,
            MessageAction='SUPPRESS',
            TemporaryPassword=password,
            UserAttributes=[
                {
                    'Name': 'custom:accountUuid',
                    'Value': account_uuid
                },
                # {
                #     'Name': 'email_verified',
                #     'Value': 'true'
                # }
            ]
        )
        console.print(f"✅ User {username} created successfully")
        client.admin_set_user_password(
            UserPoolId=user_pool_id,
            Username=username,
            Password=password,
            Permanent=True
        )
        console.print(f"✅ Password set successfully for user {username}")
    except ClientError as e:
        console.print(f"❌ User creation failed: {e.response['Error']['Code']} - {e.response['Error']['Message']}")


def initiate_auth(client, client_id, auth_flow, auth_params):
    try:
        return client.initiate_auth(
            ClientId=client_id,
            AuthFlow=auth_flow,
            AuthParameters=auth_params
        )
    except ClientError as e:
        console.print(f"❌ {auth_flow} failed: {e.response['Error']['Code']} - {e.response['Error']['Message']}", style="red")
        return None


def respond_to_challenge(client, client_id, challenge_name, session, responses):
    try:
        return client.respond_to_auth_challenge(
            ClientId=client_id,
            ChallengeName=challenge_name,
            Session=session,
            ChallengeResponses=responses
        )
    except ClientError as e:
        console.print(f"❌ Challenge failed: {e.response['Error']['Code']} - {e.response['Error']['Message']}", style="red")
        return None


def print_result(response):
    if not response:
        return

    console.print(response)

    if 'AuthenticationResult' in response:
        auth = response['AuthenticationResult']

        table = Table(title="🎉 Authentication Successful", show_header=True, header_style="bold green")
        table.add_column("Property", style="cyan")
        table.add_column("Value", style="white")

        table.add_row("Access Token", f"{auth.get('AccessToken', '')[:50]}...")
        table.add_row("Token Type", auth.get('TokenType', 'N/A'))
        table.add_row("Expires In", f"{auth.get('ExpiresIn', 'N/A')} seconds")

        console.print(table)

    elif 'ChallengeName' in response:
        challenge_panel = Panel(
            f"[yellow]Challenge: {response['ChallengeName']}[/yellow]\n"
            f"[dim]Session: {response.get('Session', '')[:50]}...[/dim]",
            title="🔄 Authentication Challenge",
            border_style="yellow"
        )
        console.print(challenge_panel)

        if 'ChallengeParameters' in response:
            params_table = Table(title="Challenge Parameters", show_header=True)
            params_table.add_column("Parameter", style="cyan")
            params_table.add_column("Value", style="white")

            for k, v in response['ChallengeParameters'].items():
                params_table.add_row(k, str(v))

            console.print(params_table)


def user_password_auth(aws_srp, client, client_id, client_secret, username, password):
    console.print(f"🔐 USER_PASSWORD_AUTH for [bold cyan]{username}[/bold cyan]", style="blue")
    params = {'USERNAME': username, 'PASSWORD': password}
    if aws_srp:
        secret_hash = aws_srp.get_secret_hash(username, client_id, client_secret)
        params['SECRET_HASH'] = secret_hash
    return initiate_auth(client, client_id, 'USER_PASSWORD_AUTH', params)


def custom_auth_srpa(aws_srp, client, client_id, client_secret, username, password):
    console.print(f"🔧 CUSTOM_AUTH with SRP_A for [bold cyan]{username}[/bold cyan]", style="blue")

    params = {'USERNAME': username}
    if password:
        try:
            # tokens = aws_srp.authenticate_user()
            # console.print(tokens)

            auth_params = aws_srp.get_auth_params()
            # console.print(auth_params)    

            params['PASSWORD'] = password
            params['CHALLENGE_NAME'] = 'SRP_A'
            params['SRP_A'] = auth_params['SRP_A']

            if aws_srp:
                secret_hash = aws_srp.get_secret_hash(username, client_id, client_secret)
                params['SECRET_HASH'] = secret_hash

            result_challenge = initiate_auth(client, client_id, 'CUSTOM_AUTH', params)
            console.print(result_challenge)
            challenge_response = aws_srp.process_challenge(result_challenge['ChallengeParameters'], params)
            # console.print(challenge_response)

            return respond_to_challenge(client, client_id, result_challenge['ChallengeName'], result_challenge['Session'], challenge_response)
        except Exception as e:
            console.print(f"❌ SRP_A failed: {e}", style="red")
            return None


def custom_auth(aws_srp, client, client_id, client_secret, username, password=None):
    console.print(f"🔧 CUSTOM_AUTH for [bold cyan]{username}[/bold cyan]", style="blue")

    params = {'USERNAME': username}
    if password:
        try:
            # params['PASSWORD'] = password

            if aws_srp:
                secret_hash = aws_srp.get_secret_hash(username, client_id, client_secret)
                params['SECRET_HASH'] = secret_hash

            return initiate_auth(client, client_id, 'CUSTOM_AUTH', params)
        except Exception as e:
            console.print(f"❌ CUSTOM_AUTH failed: {e}", style="red")
            return None


def full_user_password_and_custom_auth(aws_srp, client, client_id, client_secret, username, password):
    console.print(f"🔐 Full USER_PASSWORD_AUTH for [bold cyan]{username}[/bold cyan]", style="blue")
    result = user_password_auth(aws_srp, client, client_id, client_secret, username, password)
    # console.print(result)
    
    if result and 'AuthenticationResult' in result and result['AuthenticationResult']['AccessToken']:
        console.print("✅ USER_PASSWORD_AUTH Authentication successful")
    else:
        console.print("❌ USER_PASSWORD_AUTH Authentication failed")
        return
    
    return custom_auth(aws_srp, client, client_id, client_secret, username, password)


def handle_custom_challenge(aws_srp, client, client_id, client_secret, username, result):
    if not result or 'ChallengeName' not in result:
        return result
    
    if result['ChallengeName'] == 'CUSTOM_CHALLENGE':
        # console.print("\n🔢 Enter OTP code:", style="yellow", end=" ")
        # otp = input()
        console.print("\n🔢 Confirm challenge (Y/n):", style="yellow", end=" ")
        otp = input()
        if otp and otp.lower() == 'n':
            console.print("❌ Skipping challenge", style="red")
            return
        
        params = {'USERNAME': username, 'ANSWER': 'TRUSTED'}

        if aws_srp:
            secret_hash = aws_srp.get_secret_hash(username, client_id, client_secret)
            params['SECRET_HASH'] = secret_hash
            
        return respond_to_challenge(client, client_id, 'CUSTOM_CHALLENGE', result['Session'], params)
    return result


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--user-pool-id', required=True)
    parser.add_argument('--client-id', required=True)
    parser.add_argument('--client-secret')
    parser.add_argument('--username', required=True)
    parser.add_argument('--password')
    parser.add_argument('--region', default='us-east-1')
    parser.add_argument('--flow', choices=['user-password-auth', 'custom-auth', 'full-auth', 'custom-auth-srpa', 'admin-create-user'], required=True)
    parser.add_argument('--challenge-name')
    parser.add_argument('--session')
    parser.add_argument('--challenge-response')
    parser.add_argument('--account-uuid')

    args = parser.parse_args()

    client = create_cognito_client(args.region)

    aws_srp = AWSSRP(
            username=args.username,
            password=args.password,
            pool_id=args.user_pool_id,
            client_id=args.client_id,
            client_secret=args.client_secret,
            client=client
        ) if args.client_secret else None

    if args.flow == 'user-password-auth':
        if not args.password:
            console.print("❌ Password required for user-password flow", style="red")
            sys.exit(1)
        result = user_password_auth(aws_srp, client, args.client_id, args.client_secret, args.username, args.password)
        print_result(result)
    elif args.flow == 'custom-auth':
        result = custom_auth(aws_srp, client, args.client_id, args.client_secret, args.user_pool_id, args.username, args.password)
        print_result(result)
        final_result = handle_custom_challenge(aws_srp, client, args.client_id, args.client_secret, args.username, result)
        if final_result != result:
            print_result(final_result)
    elif args.flow == 'full-auth':
        result = full_user_password_and_custom_auth(aws_srp, client, args.client_id, args.client_secret, args.username, args.password)
        print_result(result)
        final_result = handle_custom_challenge(aws_srp, client, args.client_id, args.client_secret, args.username, result)
        if final_result != result:
            print_result(final_result)
    elif args.flow == 'custom-auth-srpa':
        result = custom_auth_srpa(aws_srp, client, args.client_id, args.client_secret, args.user_pool_id, args.username, args.password)
        print_result(result)
        final_result = handle_custom_challenge(aws_srp, client, args.client_id, args.client_secret, args.username, result)
        if final_result != result:
            print_result(final_result)
    elif args.flow == 'admin-create-user':
        if not args.account_uuid:
            console.print("❌ Account UUID required for admin-create-user flow", style="red")
            sys.exit(1)
        admin_create_user(client, args.user_pool_id, args.username, args.password, args.account_uuid)


if __name__ == '__main__':
    main()
