(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[332],{7276:(e,a,r)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/",function(){return r(2790)}])},2790:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>m});var t=r(4848),o=r(6540),l=r(7583);function n(e,a,r,t){let o="br.gov.bcb.pix",l="00".concat(o.length.toString().padStart(2,"0")).concat(o),n="";"email"===a?n="01".concat(e.length.toString().padStart(2,"0")).concat(e):"cpfCnpj"===a?n="01".concat(e.length.toString().padStart(2,"0")).concat(e):"telefone"===a?n="01".concat(e.length.toString().padStart(2,"0")).concat(e):"chaveAleatoria"===a&&(n="25".concat(e.length.toString().padStart(2,"0")).concat(e));let s="26".concat((l+n).length.toString().padStart(2,"0")).concat(l).concat(n),i=r.trim().toUpperCase().slice(0,25)||"NOME",c=t.trim().toUpperCase().slice(0,15)||"CIDADE",d="000201010212"+s+"5204000053039865802BR"+"59".concat(i.length.toString().padStart(2,"0")).concat(i)+"60".concat(c.length.toString().padStart(2,"0")).concat(c)+"62070503***6304",x=function(e){let a=65535;for(let r=0;r<e.length;r++){a^=e.charCodeAt(r)<<8;for(let e=0;e<8;e++)a=32768&a?(a<<1^4129)&65535:a<<1&65535}return a.toString(16).toUpperCase().padStart(4,"0")}(d);return d+x}async function s(e,a,r){let t;let o={errorCorrectionLevel:"H",width:256,color:{dark:r.qrColor,light:r.qrBgColor},type:"image/png",margin:4};if("pix"===e){let e=n(a.chave,a.pixKeyType,a.nome,a.cidade);t=await l.toDataURL(e,o)}else"url"===e&&(t=await l.toDataURL(a.url,o));return t}let i=e=>{let{options:a,onOptionsChange:r}=e;return(0,t.jsx)("div",{children:(0,t.jsxs)("div",{className:"color-options",children:[(0,t.jsxs)("div",{className:"color-picker",children:[(0,t.jsx)("label",{children:"Cor do QR Code:"}),(0,t.jsx)("input",{type:"color",value:a.qrColor,onChange:e=>r("qrColor",e.target.value)})]}),(0,t.jsxs)("div",{className:"color-picker",children:[(0,t.jsx)("label",{children:"Cor de Fundo:"}),(0,t.jsx)("input",{type:"color",value:a.qrBgColor,onChange:e=>r("qrBgColor",e.target.value)})]})]})})},c=e=>{let{qrData:a,qrCodeRef:r,svgString:o}=e;return(0,t.jsxs)("div",{className:"result",children:[(0,t.jsx)("h2",{children:"Seu QR Code"}),(0,t.jsx)("div",{ref:r,dangerouslySetInnerHTML:{__html:o},style:{width:"256px",height:"256px",margin:"20px auto",display:"block"}})]})};var d=r(5703),x=r.n(d);let p=e=>{let{formData:a,onFormChange:r}=e;return(0,t.jsxs)("div",{className:"jsx-dfaaca690eea8e12 form-group",children:[(0,t.jsx)("label",{className:"jsx-dfaaca690eea8e12",children:"URL:"}),(0,t.jsx)("input",{type:"text",name:"url",required:!0,value:a.url,onChange:e=>r("url",e.target.value),style:{textTransform:"none"},className:"jsx-dfaaca690eea8e12"}),(0,t.jsx)(x(),{id:"dfaaca690eea8e12",children:".form-group.jsx-dfaaca690eea8e12{margin-bottom:15px;text-align:left}.form-group.jsx-dfaaca690eea8e12 label.jsx-dfaaca690eea8e12{display:block;margin-bottom:5px;font-size:14px;color:#333}input.jsx-dfaaca690eea8e12{width:100%;padding:10px;margin-bottom:10px;border:1px solid#ddd;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;font-size:14px;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}"})]})},u=e=>{let{formData:a,onFormChange:r}=e,[l,n]=(0,o.useState)("email"),s=e=>{r(e.target.name,e.target.value)};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"jsx-d8679a6fa5114a1a form-group",children:[(0,t.jsx)("label",{className:"jsx-d8679a6fa5114a1a",children:"Tipo de Chave Pix:"}),(0,t.jsxs)("select",{value:l,onChange:e=>{n(e.target.value),r("chave","")},className:"jsx-d8679a6fa5114a1a",children:[(0,t.jsx)("option",{value:"email",className:"jsx-d8679a6fa5114a1a",children:"E-mail"}),(0,t.jsx)("option",{value:"cpfCnpj",className:"jsx-d8679a6fa5114a1a",children:"CPF/CNPJ"}),(0,t.jsx)("option",{value:"telefone",className:"jsx-d8679a6fa5114a1a",children:"Telefone Celular"}),(0,t.jsx)("option",{value:"chaveAleatoria",className:"jsx-d8679a6fa5114a1a",children:"Chave Aleat\xf3ria"})]})]}),(0,t.jsxs)("div",{className:"jsx-d8679a6fa5114a1a form-group",children:[(0,t.jsx)("label",{className:"jsx-d8679a6fa5114a1a",children:"Chave Pix:"}),(0,t.jsx)("input",{type:"text",name:"chave",required:!0,value:a.chave,onChange:s,style:{textTransform:"chaveAleatoria"===l?"uppercase":"lowercase"},placeholder:"cpfCnpj"===l?"CPF/CNPJ":"email"===l?"E-mail":"telefone"===l?"+55...":"Chave Aleat\xf3ria",className:"jsx-d8679a6fa5114a1a"})]}),(0,t.jsxs)("div",{className:"jsx-d8679a6fa5114a1a form-group",children:[(0,t.jsx)("label",{className:"jsx-d8679a6fa5114a1a",children:"Nome do Recebedor:"}),(0,t.jsx)("input",{type:"text",name:"nome",value:a.nome,onChange:s,style:{textTransform:"uppercase"},className:"jsx-d8679a6fa5114a1a"})]}),(0,t.jsxs)("div",{className:"jsx-d8679a6fa5114a1a form-group",children:[(0,t.jsx)("label",{className:"jsx-d8679a6fa5114a1a",children:"Cidade do Recebedor:"}),(0,t.jsx)("input",{type:"text",name:"cidade",value:a.cidade,onChange:s,style:{textTransform:"uppercase"},className:"jsx-d8679a6fa5114a1a"})]}),(0,t.jsx)(x(),{id:"d8679a6fa5114a1a",children:".form-group.jsx-d8679a6fa5114a1a{margin-bottom:15px;text-align:left}.form-group.jsx-d8679a6fa5114a1a label.jsx-d8679a6fa5114a1a{display:block;margin-bottom:5px;font-size:14px;color:#333}input.jsx-d8679a6fa5114a1a,select.jsx-d8679a6fa5114a1a{width:100%;padding:10px;margin-bottom:10px;border:1px solid#ddd;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;font-size:14px;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}"})]})};var h=r(9146);let g=e=>{let{mode:a,onBack:r}=e,[d,x]=(0,o.useState)("pix"===a?{chave:"",nome:"",cidade:"",pixKeyType:"email"}:{url:""}),[g,m]=(0,o.useState)(null),[j,f]=(0,o.useState)(null),[b,C]=(0,o.useState)(!1),[v,S]=(0,o.useState)({qrColor:"#000000",qrBgColor:"#FFFFFF"}),y=(0,o.useRef)(null),N=async(e,a,r)=>{if("url"===e&&a.url||"pix"===e)try{let t;let o={errorCorrectionLevel:"H",width:256,color:{dark:r.qrColor,light:r.qrBgColor},margin:4};if("url"===e)t=await l.toString(a.url,o);else if("pix"===e){let e=n(a.chave,a.pixKeyType,a.nome,a.cidade);t=await l.toString(e,o)}f(t)}catch(e){console.error("Falha ao gerar o QR Code SVG",e),alert("Falha ao gerar o QR Code SVG")}},R=async()=>{C(!0);try{let e=await s(a,d,v);m(e),N(a,d,v)}catch(e){console.error("Falha ao gerar o QR Code",e),alert("Falha ao gerar o QR Code")}finally{C(!1)}};(0,o.useEffect)(()=>{},[v]);let k=(e,a)=>{x({...d,[e]:a})};return(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{children:"pix"===a?"Gerar QR Code Pix":"Gerar QR Code de URL"}),(0,t.jsx)("p",{children:"pix"===a?"Preencha os campos para gerar um QR Code Pix v\xe1lido.":"Digite a URL para gerar o QR Code."}),(0,t.jsx)(i,{options:v,onOptionsChange:(e,a)=>{S({...v,[e]:a})}}),"pix"===a&&(0,t.jsx)(u,{formData:d,onFormChange:k}),"url"===a&&(0,t.jsx)(p,{formData:d,onFormChange:k}),(0,t.jsx)("button",{onClick:R,disabled:b,children:b?"Gerando...":"Gerar QR Code"}),(0,t.jsx)("button",{onClick:r,style:{marginTop:"10px"},children:"Voltar"}),g&&(0,t.jsx)(c,{qrCodeRef:y,svgString:j}),g&&j&&(0,t.jsxs)("div",{style:{marginTop:"20px",textAlign:"center"},children:[(0,t.jsx)("button",{onClick:()=>{if(!j)return;let e=new Blob([j],{type:"image/svg+xml"}),r=URL.createObjectURL(e),t=document.createElement("a");t.href=r,t.download="pix"===a?"pix_qrcode.svg":"url_qrcode.svg",document.body.appendChild(t),t.click(),document.body.removeChild(t),URL.revokeObjectURL(r)},style:{marginRight:"10px"},children:"Baixar como SVG"}),(0,t.jsx)("button",{onClick:()=>{if(!y.current)return;let e=y.current.querySelector("svg");if(!e){console.error("Elemento SVG n\xe3o encontrado.");return}(0,h.saveSvgAsPng)(e,"".concat(a,"_qrcode.png"),{scale:2})},children:"Baixar como PNG"})]})]})};function m(){let[e,a]=(0,o.useState)(null);return(0,t.jsxs)("div",{className:"container",children:[!e&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("h1",{children:"O que voc\xea deseja fazer?"}),(0,t.jsx)("button",{onClick:()=>a("url"),children:"Gerar QR Code de URL"}),(0,t.jsx)("button",{onClick:()=>a("pix"),children:"Gerar QR Code Pix"})]}),e&&(0,t.jsx)(g,{mode:e,onBack:()=>a(null)})]})}}},e=>{var a=a=>e(e.s=a);e.O(0,[838,636,593,792],()=>a(7276)),_N_E=e.O()}]);