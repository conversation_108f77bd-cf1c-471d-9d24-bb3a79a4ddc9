(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[838],{6320:t=>{"use strict";var e={single_source_shortest_paths:function(t,r,n){var o,i,s,a,u,l,c,h={},f={};f[r]=0;var d=e.PriorityQueue.make();for(d.push(r,0);!d.empty();)for(s in i=(o=d.pop()).value,a=o.cost,u=t[i]||{})u.hasOwnProperty(s)&&(l=a+u[s],c=f[s],(void 0===f[s]||c>l)&&(f[s]=l,d.push(s,l),h[s]=i));if(void 0!==n&&void 0===f[n])throw Error(["Could not find a path from ",r," to ",n,"."].join(""));return h},extract_shortest_path_from_predecessor_list:function(t,e){for(var r=[],n=e;n;)r.push(n),t[n],n=t[n];return r.reverse(),r},find_path:function(t,r,n){var o=e.single_source_shortest_paths(t,r,n);return e.extract_shortest_path_from_predecessor_list(o,n)},PriorityQueue:{make:function(t){var r,n=e.PriorityQueue,o={};for(r in t=t||{},n)n.hasOwnProperty(r)&&(o[r]=n[r]);return o.queue=[],o.sorter=t.sorter||n.default_sorter,o},default_sorter:function(t,e){return t.cost-e.cost},push:function(t,e){this.queue.push({value:t,cost:e}),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return 0===this.queue.length}}};t.exports=e},2532:(t,e,r)=>{"use strict";var n=r(7836);r(9750);var o=r(6540),i=function(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}(o),s=void 0!==n&&n.env&&!0,a=function(t){return"[object String]"===Object.prototype.toString.call(t)},u=function(){function t(t){var e=void 0===t?{}:t,r=e.name,n=void 0===r?"stylesheet":r,o=e.optimizeForSpeed,i=void 0===o?s:o;l(a(n),"`name` must be a string"),this._name=n,this._deletedRulePlaceholder="#"+n+"-deleted-rule____{}",l("boolean"==typeof i,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=i,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var u=document.querySelector('meta[property="csp-nonce"]');this._nonce=u?u.getAttribute("content"):null}var e=t.prototype;return e.setOptimizeForSpeed=function(t){l("boolean"==typeof t,"`setOptimizeForSpeed` accepts a boolean"),l(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=t,this.inject()},e.isOptimizeForSpeed=function(){return this._optimizeForSpeed},e.inject=function(){var t=this;if(l(!this._injected,"sheet already injected"),this._injected=!0,this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(s||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(e,r){return"number"==typeof r?t._serverSheet.cssRules[r]={cssText:e}:t._serverSheet.cssRules.push({cssText:e}),r},deleteRule:function(e){t._serverSheet.cssRules[e]=null}}},e.getSheetForTag=function(t){if(t.sheet)return t.sheet;for(var e=0;e<document.styleSheets.length;e++)if(document.styleSheets[e].ownerNode===t)return document.styleSheets[e]},e.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},e.insertRule=function(t,e){if(l(a(t),"`insertRule` accepts only strings"),this._optimizeForSpeed){var r=this.getSheet();"number"!=typeof e&&(e=r.cssRules.length);try{r.insertRule(t,e)}catch(e){return s||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var n=this._tags[e];this._tags.push(this.makeStyleTag(this._name,t,n))}return this._rulesCount++},e.replaceRule=function(t,e){if(this._optimizeForSpeed){var r=this.getSheet();if(e.trim()||(e=this._deletedRulePlaceholder),!r.cssRules[t])return t;r.deleteRule(t);try{r.insertRule(e,t)}catch(n){s||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),r.insertRule(this._deletedRulePlaceholder,t)}}else{var n=this._tags[t];l(n,"old rule at index `"+t+"` not found"),n.textContent=e}return t},e.deleteRule=function(t){if(this._optimizeForSpeed)this.replaceRule(t,"");else{var e=this._tags[t];l(e,"rule at index `"+t+"` not found"),e.parentNode.removeChild(e),this._tags[t]=null}},e.flush=function(){this._injected=!1,this._rulesCount=0,this._tags.forEach(function(t){return t&&t.parentNode.removeChild(t)}),this._tags=[]},e.cssRules=function(){var t=this;return this._tags.reduce(function(e,r){return r?e=e.concat(Array.prototype.map.call(t.getSheetForTag(r).cssRules,function(e){return e.cssText===t._deletedRulePlaceholder?null:e})):e.push(null),e},[])},e.makeStyleTag=function(t,e,r){e&&l(a(e),"makeStyleTag accepts only strings as second parameter");var n=document.createElement("style");this._nonce&&n.setAttribute("nonce",this._nonce),n.type="text/css",n.setAttribute("data-"+t,""),e&&n.appendChild(document.createTextNode(e));var o=document.head||document.getElementsByTagName("head")[0];return r?o.insertBefore(n,r):o.appendChild(n),n},function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}(t.prototype,[{key:"length",get:function(){return this._rulesCount}}]),t}();function l(t,e){if(!t)throw Error("StyleSheet: "+e+".")}var c=function(t){for(var e=5381,r=t.length;r;)e=33*e^t.charCodeAt(--r);return e>>>0},h={};function f(t,e){if(!e)return"jsx-"+t;var r=String(e),n=t+r;return h[n]||(h[n]="jsx-"+c(t+"-"+r)),h[n]}function d(t,e){var r=t+e;return h[r]||(h[r]=e.replace(/__jsx-style-dynamic-selector/g,t)),h[r]}var g=function(){function t(t){var e=void 0===t?{}:t,r=e.styleSheet,n=void 0===r?null:r,o=e.optimizeForSpeed,i=void 0!==o&&o;this._sheet=n||new u({name:"styled-jsx",optimizeForSpeed:i}),this._sheet.inject(),n&&"boolean"==typeof i&&(this._sheet.setOptimizeForSpeed(i),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var e=t.prototype;return e.add=function(t){var e=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(t.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(t,e){return t[e]=0,t},{}));var r=this.getIdAndRules(t),n=r.styleId,o=r.rules;if(n in this._instancesCounts){this._instancesCounts[n]+=1;return}var i=o.map(function(t){return e._sheet.insertRule(t)}).filter(function(t){return -1!==t});this._indices[n]=i,this._instancesCounts[n]=1},e.remove=function(t){var e=this,r=this.getIdAndRules(t).styleId;if(function(t,e){if(!t)throw Error("StyleSheetRegistry: "+e+".")}(r in this._instancesCounts,"styleId: `"+r+"` not found"),this._instancesCounts[r]-=1,this._instancesCounts[r]<1){var n=this._fromServer&&this._fromServer[r];n?(n.parentNode.removeChild(n),delete this._fromServer[r]):(this._indices[r].forEach(function(t){return e._sheet.deleteRule(t)}),delete this._indices[r]),delete this._instancesCounts[r]}},e.update=function(t,e){this.add(e),this.remove(t)},e.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},e.cssRules=function(){var t=this,e=this._fromServer?Object.keys(this._fromServer).map(function(e){return[e,t._fromServer[e]]}):[],r=this._sheet.cssRules();return e.concat(Object.keys(this._indices).map(function(e){return[e,t._indices[e].map(function(t){return r[t].cssText}).join(t._optimizeForSpeed?"":"\n")]}).filter(function(t){return!!t[1]}))},e.styles=function(t){var e,r;return e=this.cssRules(),void 0===(r=t)&&(r={}),e.map(function(t){var e=t[0],n=t[1];return i.default.createElement("style",{id:"__"+e,key:"__"+e,nonce:r.nonce?r.nonce:void 0,dangerouslySetInnerHTML:{__html:n}})})},e.getIdAndRules=function(t){var e=t.children,r=t.dynamic,n=t.id;if(r){var o=f(n,r);return{styleId:o,rules:Array.isArray(e)?e.map(function(t){return d(o,t)}):[d(o,e)]}}return{styleId:f(n),rules:Array.isArray(e)?e:[e]}},e.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(t,e){return t[e.id.slice(2)]=e,t},{})},t}(),p=o.createContext(null);p.displayName="StyleSheetContext";var m=i.default.useInsertionEffect||i.default.useLayoutEffect,v=new g;function y(t){var e=v||o.useContext(p);return e&&m(function(){return e.add(t),function(){e.remove(t)}},[t.id,String(t.dynamic)]),null}y.dynamic=function(t){return t.map(function(t){return f(t[0],t[1])}).join(" ")},e.style=y},5703:(t,e,r)=>{"use strict";t.exports=r(2532).style},9750:()=>{},7583:(t,e,r)=>{let n=r(1333),o=r(157),i=r(7899),s=r(6756);function a(t,e,r,i,s){let a=[].slice.call(arguments,1),u=a.length,l="function"==typeof a[u-1];if(!l&&!n())throw Error("Callback required as last argument");if(l){if(u<2)throw Error("Too few arguments provided");2===u?(s=r,r=e,e=i=void 0):3===u&&(e.getContext&&void 0===s?(s=i,i=void 0):(s=i,i=r,r=e,e=void 0))}else{if(u<1)throw Error("Too few arguments provided");return 1===u?(r=e,e=i=void 0):2!==u||e.getContext||(i=r,r=e,e=void 0),new Promise(function(n,s){try{let s=o.create(r,i);n(t(s,e,i))}catch(t){s(t)}})}try{let n=o.create(r,i);s(null,t(n,e,i))}catch(t){s(t)}}e.create=o.create,e.toCanvas=a.bind(null,i.render),e.toDataURL=a.bind(null,i.renderToDataURL),e.toString=a.bind(null,function(t,e,r){return s.render(t,r)})},1333:t=>{t.exports=function(){return"function"==typeof Promise&&Promise.prototype&&Promise.prototype.then}},6421:(t,e,r)=>{let n=r(6886).getSymbolSize;e.getRowColCoords=function(t){if(1===t)return[];let e=Math.floor(t/7)+2,r=n(t),o=145===r?26:2*Math.ceil((r-13)/(2*e-2)),i=[r-7];for(let t=1;t<e-1;t++)i[t]=i[t-1]-o;return i.push(6),i.reverse()},e.getPositions=function(t){let r=[],n=e.getRowColCoords(t),o=n.length;for(let t=0;t<o;t++)for(let e=0;e<o;e++)(0!==t||0!==e)&&(0!==t||e!==o-1)&&(t!==o-1||0!==e)&&r.push([n[t],n[e]]);return r}},1433:(t,e,r)=>{let n=r(208),o=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function i(t){this.mode=n.ALPHANUMERIC,this.data=t}i.getBitsLength=function(t){return 11*Math.floor(t/2)+t%2*6},i.prototype.getLength=function(){return this.data.length},i.prototype.getBitsLength=function(){return i.getBitsLength(this.data.length)},i.prototype.write=function(t){let e;for(e=0;e+2<=this.data.length;e+=2){let r=45*o.indexOf(this.data[e]);r+=o.indexOf(this.data[e+1]),t.put(r,11)}this.data.length%2&&t.put(o.indexOf(this.data[e]),6)},t.exports=i},9899:t=>{function e(){this.buffer=[],this.length=0}e.prototype={get:function(t){let e=Math.floor(t/8);return(this.buffer[e]>>>7-t%8&1)==1},put:function(t,e){for(let r=0;r<e;r++)this.putBit((t>>>e-r-1&1)==1)},getLengthInBits:function(){return this.length},putBit:function(t){let e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),t&&(this.buffer[e]|=128>>>this.length%8),this.length++}},t.exports=e},8820:t=>{function e(t){if(!t||t<1)throw Error("BitMatrix size must be defined and greater than 0");this.size=t,this.data=new Uint8Array(t*t),this.reservedBit=new Uint8Array(t*t)}e.prototype.set=function(t,e,r,n){let o=t*this.size+e;this.data[o]=r,n&&(this.reservedBit[o]=!0)},e.prototype.get=function(t,e){return this.data[t*this.size+e]},e.prototype.xor=function(t,e,r){this.data[t*this.size+e]^=r},e.prototype.isReserved=function(t,e){return this.reservedBit[t*this.size+e]},t.exports=e},5822:(t,e,r)=>{let n=r(208);function o(t){this.mode=n.BYTE,"string"==typeof t?this.data=new TextEncoder().encode(t):this.data=new Uint8Array(t)}o.getBitsLength=function(t){return 8*t},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(t){for(let e=0,r=this.data.length;e<r;e++)t.put(this.data[e],8)},t.exports=o},7518:(t,e,r)=>{let n=r(9953),o=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],i=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];e.getBlocksCount=function(t,e){switch(e){case n.L:return o[(t-1)*4+0];case n.M:return o[(t-1)*4+1];case n.Q:return o[(t-1)*4+2];case n.H:return o[(t-1)*4+3];default:return}},e.getTotalCodewordsCount=function(t,e){switch(e){case n.L:return i[(t-1)*4+0];case n.M:return i[(t-1)*4+1];case n.Q:return i[(t-1)*4+2];case n.H:return i[(t-1)*4+3];default:return}}},9953:(t,e)=>{e.L={bit:1},e.M={bit:0},e.Q={bit:3},e.H={bit:2},e.isValid=function(t){return t&&void 0!==t.bit&&t.bit>=0&&t.bit<4},e.from=function(t,r){if(e.isValid(t))return t;try{return function(t){if("string"!=typeof t)throw Error("Param is not a string");switch(t.toLowerCase()){case"l":case"low":return e.L;case"m":case"medium":return e.M;case"q":case"quartile":return e.Q;case"h":case"high":return e.H;default:throw Error("Unknown EC Level: "+t)}}(t)}catch(t){return r}}},7756:(t,e,r)=>{let n=r(6886).getSymbolSize;e.getPositions=function(t){let e=n(t);return[[0,0],[e-7,0],[0,e-7]]}},4565:(t,e,r)=>{let n=r(6886),o=n.getBCHDigit(1335);e.getEncodedBits=function(t,e){let r=t.bit<<3|e,i=r<<10;for(;n.getBCHDigit(i)-o>=0;)i^=1335<<n.getBCHDigit(i)-o;return(r<<10|i)^21522}},2731:(t,e)=>{let r=new Uint8Array(512),n=new Uint8Array(256);!function(){let t=1;for(let e=0;e<255;e++)r[e]=t,n[t]=e,256&(t<<=1)&&(t^=285);for(let t=255;t<512;t++)r[t]=r[t-255]}(),e.log=function(t){if(t<1)throw Error("log("+t+")");return n[t]},e.exp=function(t){return r[t]},e.mul=function(t,e){return 0===t||0===e?0:r[n[t]+n[e]]}},4861:(t,e,r)=>{let n=r(208),o=r(6886);function i(t){this.mode=n.KANJI,this.data=t}i.getBitsLength=function(t){return 13*t},i.prototype.getLength=function(){return this.data.length},i.prototype.getBitsLength=function(){return i.getBitsLength(this.data.length)},i.prototype.write=function(t){let e;for(e=0;e<this.data.length;e++){let r=o.toSJIS(this.data[e]);if(r>=33088&&r<=40956)r-=33088;else if(r>=57408&&r<=60351)r-=49472;else throw Error("Invalid SJIS character: "+this.data[e]+"\nMake sure your charset is UTF-8");r=(r>>>8&255)*192+(255&r),t.put(r,13)}},t.exports=i},1332:(t,e)=>{e.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};let r={N1:3,N2:3,N3:40,N4:10};e.isValid=function(t){return null!=t&&""!==t&&!isNaN(t)&&t>=0&&t<=7},e.from=function(t){return e.isValid(t)?parseInt(t,10):void 0},e.getPenaltyN1=function(t){let e=t.size,n=0,o=0,i=0,s=null,a=null;for(let u=0;u<e;u++){o=i=0,s=a=null;for(let l=0;l<e;l++){let e=t.get(u,l);e===s?o++:(o>=5&&(n+=r.N1+(o-5)),s=e,o=1),(e=t.get(l,u))===a?i++:(i>=5&&(n+=r.N1+(i-5)),a=e,i=1)}o>=5&&(n+=r.N1+(o-5)),i>=5&&(n+=r.N1+(i-5))}return n},e.getPenaltyN2=function(t){let e=t.size,n=0;for(let r=0;r<e-1;r++)for(let o=0;o<e-1;o++){let e=t.get(r,o)+t.get(r,o+1)+t.get(r+1,o)+t.get(r+1,o+1);(4===e||0===e)&&n++}return n*r.N2},e.getPenaltyN3=function(t){let e=t.size,n=0,o=0,i=0;for(let r=0;r<e;r++){o=i=0;for(let s=0;s<e;s++)o=o<<1&2047|t.get(r,s),s>=10&&(1488===o||93===o)&&n++,i=i<<1&2047|t.get(s,r),s>=10&&(1488===i||93===i)&&n++}return n*r.N3},e.getPenaltyN4=function(t){let e=0,n=t.data.length;for(let r=0;r<n;r++)e+=t.data[r];return Math.abs(Math.ceil(100*e/n/5)-10)*r.N4},e.applyMask=function(t,r){let n=r.size;for(let o=0;o<n;o++)for(let i=0;i<n;i++)r.isReserved(i,o)||r.xor(i,o,function(t,r,n){switch(t){case e.Patterns.PATTERN000:return(r+n)%2==0;case e.Patterns.PATTERN001:return r%2==0;case e.Patterns.PATTERN010:return n%3==0;case e.Patterns.PATTERN011:return(r+n)%3==0;case e.Patterns.PATTERN100:return(Math.floor(r/2)+Math.floor(n/3))%2==0;case e.Patterns.PATTERN101:return r*n%2+r*n%3==0;case e.Patterns.PATTERN110:return(r*n%2+r*n%3)%2==0;case e.Patterns.PATTERN111:return(r*n%3+(r+n)%2)%2==0;default:throw Error("bad maskPattern:"+t)}}(t,i,o))},e.getBestMask=function(t,r){let n=Object.keys(e.Patterns).length,o=0,i=1/0;for(let s=0;s<n;s++){r(s),e.applyMask(s,t);let n=e.getPenaltyN1(t)+e.getPenaltyN2(t)+e.getPenaltyN3(t)+e.getPenaltyN4(t);e.applyMask(s,t),n<i&&(i=n,o=s)}return o}},208:(t,e,r)=>{let n=r(1878),o=r(7044);e.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},e.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},e.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},e.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},e.MIXED={bit:-1},e.getCharCountIndicator=function(t,e){if(!t.ccBits)throw Error("Invalid mode: "+t);if(!n.isValid(e))throw Error("Invalid version: "+e);return e>=1&&e<10?t.ccBits[0]:e<27?t.ccBits[1]:t.ccBits[2]},e.getBestModeForData=function(t){return o.testNumeric(t)?e.NUMERIC:o.testAlphanumeric(t)?e.ALPHANUMERIC:o.testKanji(t)?e.KANJI:e.BYTE},e.toString=function(t){if(t&&t.id)return t.id;throw Error("Invalid mode")},e.isValid=function(t){return t&&t.bit&&t.ccBits},e.from=function(t,r){if(e.isValid(t))return t;try{return function(t){if("string"!=typeof t)throw Error("Param is not a string");switch(t.toLowerCase()){case"numeric":return e.NUMERIC;case"alphanumeric":return e.ALPHANUMERIC;case"kanji":return e.KANJI;case"byte":return e.BYTE;default:throw Error("Unknown mode: "+t)}}(t)}catch(t){return r}}},6738:(t,e,r)=>{let n=r(208);function o(t){this.mode=n.NUMERIC,this.data=t.toString()}o.getBitsLength=function(t){return 10*Math.floor(t/3)+(t%3?t%3*3+1:0)},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(t){let e,r;for(e=0;e+3<=this.data.length;e+=3)r=parseInt(this.data.substr(e,3),10),t.put(r,10);let n=this.data.length-e;n>0&&(r=parseInt(this.data.substr(e),10),t.put(r,3*n+1))},t.exports=o},4713:(t,e,r)=>{let n=r(2731);e.mul=function(t,e){let r=new Uint8Array(t.length+e.length-1);for(let o=0;o<t.length;o++)for(let i=0;i<e.length;i++)r[o+i]^=n.mul(t[o],e[i]);return r},e.mod=function(t,e){let r=new Uint8Array(t);for(;r.length-e.length>=0;){let t=r[0];for(let o=0;o<e.length;o++)r[o]^=n.mul(e[o],t);let o=0;for(;o<r.length&&0===r[o];)o++;r=r.slice(o)}return r},e.generateECPolynomial=function(t){let r=new Uint8Array([1]);for(let o=0;o<t;o++)r=e.mul(r,new Uint8Array([1,n.exp(o)]));return r}},157:(t,e,r)=>{let n=r(6886),o=r(9953),i=r(9899),s=r(8820),a=r(6421),u=r(7756),l=r(1332),c=r(7518),h=r(4764),f=r(1427),d=r(4565),g=r(208),p=r(9801);function m(t,e,r){let n,o;let i=t.size,s=d.getEncodedBits(e,r);for(n=0;n<15;n++)o=(s>>n&1)==1,n<6?t.set(n,8,o,!0):n<8?t.set(n+1,8,o,!0):t.set(i-15+n,8,o,!0),n<8?t.set(8,i-n-1,o,!0):n<9?t.set(8,15-n-1+1,o,!0):t.set(8,15-n-1,o,!0);t.set(i-8,8,1,!0)}e.create=function(t,e){let r,d;if(void 0===t||""===t)throw Error("No input text");let v=o.M;return void 0!==e&&(v=o.from(e.errorCorrectionLevel,o.M),r=f.from(e.version),d=l.from(e.maskPattern),e.toSJISFunc&&n.setToSJISFunction(e.toSJISFunc)),function(t,e,r,o){let d;if(Array.isArray(t))d=p.fromArray(t);else if("string"==typeof t){let n=e;if(!n){let e=p.rawSplit(t);n=f.getBestVersionForData(e,r)}d=p.fromString(t,n||40)}else throw Error("Invalid data");let v=f.getBestVersionForData(d,r);if(!v)throw Error("The amount of data is too big to be stored in a QR Code");if(e){if(e<v)throw Error("\nThe chosen QR Code version cannot contain this amount of data.\nMinimum version required to store current data is: "+v+".\n")}else e=v;let y=function(t,e,r){let o=new i;r.forEach(function(e){o.put(e.mode.bit,4),o.put(e.getLength(),g.getCharCountIndicator(e.mode,t)),e.write(o)});let s=(n.getSymbolTotalCodewords(t)-c.getTotalCodewordsCount(t,e))*8;for(o.getLengthInBits()+4<=s&&o.put(0,4);o.getLengthInBits()%8!=0;)o.putBit(0);let a=(s-o.getLengthInBits())/8;for(let t=0;t<a;t++)o.put(t%2?17:236,8);return function(t,e,r){let o,i;let s=n.getSymbolTotalCodewords(e),a=s-c.getTotalCodewordsCount(e,r),u=c.getBlocksCount(e,r),l=s%u,f=u-l,d=Math.floor(s/u),g=Math.floor(a/u),p=g+1,m=d-g,v=new h(m),y=0,w=Array(u),E=Array(u),S=0,A=new Uint8Array(t.buffer);for(let t=0;t<u;t++){let e=t<f?g:p;w[t]=A.slice(y,y+e),E[t]=v.encode(w[t]),y+=e,S=Math.max(S,e)}let b=new Uint8Array(s),C=0;for(o=0;o<S;o++)for(i=0;i<u;i++)o<w[i].length&&(b[C++]=w[i][o]);for(o=0;o<m;o++)for(i=0;i<u;i++)b[C++]=E[i][o];return b}(o,t,e)}(e,r,d),w=new s(n.getSymbolSize(e));return function(t,e){let r=t.size,n=u.getPositions(e);for(let e=0;e<n.length;e++){let o=n[e][0],i=n[e][1];for(let e=-1;e<=7;e++)if(!(o+e<=-1)&&!(r<=o+e))for(let n=-1;n<=7;n++)i+n<=-1||r<=i+n||(e>=0&&e<=6&&(0===n||6===n)||n>=0&&n<=6&&(0===e||6===e)||e>=2&&e<=4&&n>=2&&n<=4?t.set(o+e,i+n,!0,!0):t.set(o+e,i+n,!1,!0))}}(w,e),function(t){let e=t.size;for(let r=8;r<e-8;r++){let e=r%2==0;t.set(r,6,e,!0),t.set(6,r,e,!0)}}(w),function(t,e){let r=a.getPositions(e);for(let e=0;e<r.length;e++){let n=r[e][0],o=r[e][1];for(let e=-2;e<=2;e++)for(let r=-2;r<=2;r++)-2===e||2===e||-2===r||2===r||0===e&&0===r?t.set(n+e,o+r,!0,!0):t.set(n+e,o+r,!1,!0)}}(w,e),m(w,r,0),e>=7&&function(t,e){let r,n,o;let i=t.size,s=f.getEncodedBits(e);for(let e=0;e<18;e++)r=Math.floor(e/3),n=e%3+i-8-3,o=(s>>e&1)==1,t.set(r,n,o,!0),t.set(n,r,o,!0)}(w,e),function(t,e){let r=t.size,n=-1,o=r-1,i=7,s=0;for(let a=r-1;a>0;a-=2)for(6===a&&a--;;){for(let r=0;r<2;r++)if(!t.isReserved(o,a-r)){let n=!1;s<e.length&&(n=(e[s]>>>i&1)==1),t.set(o,a-r,n),-1==--i&&(s++,i=7)}if((o+=n)<0||r<=o){o-=n,n=-n;break}}}(w,y),isNaN(o)&&(o=l.getBestMask(w,m.bind(null,w,r))),l.applyMask(o,w),m(w,r,o),{modules:w,version:e,errorCorrectionLevel:r,maskPattern:o,segments:d}}(t,r,v,d)}},4764:(t,e,r)=>{let n=r(4713);function o(t){this.genPoly=void 0,this.degree=t,this.degree&&this.initialize(this.degree)}o.prototype.initialize=function(t){this.degree=t,this.genPoly=n.generateECPolynomial(this.degree)},o.prototype.encode=function(t){if(!this.genPoly)throw Error("Encoder not initialized");let e=new Uint8Array(t.length+this.degree);e.set(t);let r=n.mod(e,this.genPoly),o=this.degree-r.length;if(o>0){let t=new Uint8Array(this.degree);return t.set(r,o),t}return r},t.exports=o},7044:(t,e)=>{let r="[0-9]+",n="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+",o="(?:(?![A-Z0-9 $%*+\\-./:]|"+(n=n.replace(/u/g,"\\u"))+")(?:.|[\r\n]))+";e.KANJI=RegExp(n,"g"),e.BYTE_KANJI=RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),e.BYTE=RegExp(o,"g"),e.NUMERIC=RegExp(r,"g"),e.ALPHANUMERIC=RegExp("[A-Z $%*+\\-./:]+","g");let i=RegExp("^"+n+"$"),s=RegExp("^"+r+"$"),a=RegExp("^[A-Z0-9 $%*+\\-./:]+$");e.testKanji=function(t){return i.test(t)},e.testNumeric=function(t){return s.test(t)},e.testAlphanumeric=function(t){return a.test(t)}},9801:(t,e,r)=>{let n=r(208),o=r(6738),i=r(1433),s=r(5822),a=r(4861),u=r(7044),l=r(6886),c=r(6320);function h(t){return unescape(encodeURIComponent(t)).length}function f(t,e,r){let n;let o=[];for(;null!==(n=t.exec(r));)o.push({data:n[0],index:n.index,mode:e,length:n[0].length});return o}function d(t){let e,r;let o=f(u.NUMERIC,n.NUMERIC,t),i=f(u.ALPHANUMERIC,n.ALPHANUMERIC,t);return l.isKanjiModeEnabled()?(e=f(u.BYTE,n.BYTE,t),r=f(u.KANJI,n.KANJI,t)):(e=f(u.BYTE_KANJI,n.BYTE,t),r=[]),o.concat(i,e,r).sort(function(t,e){return t.index-e.index}).map(function(t){return{data:t.data,mode:t.mode,length:t.length}})}function g(t,e){switch(e){case n.NUMERIC:return o.getBitsLength(t);case n.ALPHANUMERIC:return i.getBitsLength(t);case n.KANJI:return a.getBitsLength(t);case n.BYTE:return s.getBitsLength(t)}}function p(t,e){let r;let u=n.getBestModeForData(t);if((r=n.from(e,u))!==n.BYTE&&r.bit<u.bit)throw Error('"'+t+'" cannot be encoded with mode '+n.toString(r)+".\n Suggested mode is: "+n.toString(u));switch(r!==n.KANJI||l.isKanjiModeEnabled()||(r=n.BYTE),r){case n.NUMERIC:return new o(t);case n.ALPHANUMERIC:return new i(t);case n.KANJI:return new a(t);case n.BYTE:return new s(t)}}e.fromArray=function(t){return t.reduce(function(t,e){return"string"==typeof e?t.push(p(e,null)):e.data&&t.push(p(e.data,e.mode)),t},[])},e.fromString=function(t,r){let o=function(t,e){let r={},o={start:{}},i=["start"];for(let s=0;s<t.length;s++){let a=t[s],u=[];for(let t=0;t<a.length;t++){let l=a[t],c=""+s+t;u.push(c),r[c]={node:l,lastCount:0},o[c]={};for(let t=0;t<i.length;t++){let s=i[t];r[s]&&r[s].node.mode===l.mode?(o[s][c]=g(r[s].lastCount+l.length,l.mode)-g(r[s].lastCount,l.mode),r[s].lastCount+=l.length):(r[s]&&(r[s].lastCount=l.length),o[s][c]=g(l.length,l.mode)+4+n.getCharCountIndicator(l.mode,e))}}i=u}for(let t=0;t<i.length;t++)o[i[t]].end=0;return{map:o,table:r}}(function(t){let e=[];for(let r=0;r<t.length;r++){let o=t[r];switch(o.mode){case n.NUMERIC:e.push([o,{data:o.data,mode:n.ALPHANUMERIC,length:o.length},{data:o.data,mode:n.BYTE,length:o.length}]);break;case n.ALPHANUMERIC:e.push([o,{data:o.data,mode:n.BYTE,length:o.length}]);break;case n.KANJI:e.push([o,{data:o.data,mode:n.BYTE,length:h(o.data)}]);break;case n.BYTE:e.push([{data:o.data,mode:n.BYTE,length:h(o.data)}])}}return e}(d(t,l.isKanjiModeEnabled())),r),i=c.find_path(o.map,"start","end"),s=[];for(let t=1;t<i.length-1;t++)s.push(o.table[i[t]].node);return e.fromArray(s.reduce(function(t,e){let r=t.length-1>=0?t[t.length-1]:null;return r&&r.mode===e.mode?t[t.length-1].data+=e.data:t.push(e),t},[]))},e.rawSplit=function(t){return e.fromArray(d(t,l.isKanjiModeEnabled()))}},6886:(t,e)=>{let r;let n=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];e.getSymbolSize=function(t){if(!t)throw Error('"version" cannot be null or undefined');if(t<1||t>40)throw Error('"version" should be in range from 1 to 40');return 4*t+17},e.getSymbolTotalCodewords=function(t){return n[t]},e.getBCHDigit=function(t){let e=0;for(;0!==t;)e++,t>>>=1;return e},e.setToSJISFunction=function(t){if("function"!=typeof t)throw Error('"toSJISFunc" is not a valid function.');r=t},e.isKanjiModeEnabled=function(){return void 0!==r},e.toSJIS=function(t){return r(t)}},1878:(t,e)=>{e.isValid=function(t){return!isNaN(t)&&t>=1&&t<=40}},1427:(t,e,r)=>{let n=r(6886),o=r(7518),i=r(9953),s=r(208),a=r(1878),u=n.getBCHDigit(7973);function l(t,e){return s.getCharCountIndicator(t,e)+4}e.from=function(t,e){return a.isValid(t)?parseInt(t,10):e},e.getCapacity=function(t,e,r){if(!a.isValid(t))throw Error("Invalid QR Code version");void 0===r&&(r=s.BYTE);let i=(n.getSymbolTotalCodewords(t)-o.getTotalCodewordsCount(t,e))*8;if(r===s.MIXED)return i;let u=i-l(r,t);switch(r){case s.NUMERIC:return Math.floor(u/10*3);case s.ALPHANUMERIC:return Math.floor(u/11*2);case s.KANJI:return Math.floor(u/13);case s.BYTE:default:return Math.floor(u/8)}},e.getBestVersionForData=function(t,r){let n;let o=i.from(r,i.M);if(Array.isArray(t)){if(t.length>1)return function(t,r){for(let n=1;n<=40;n++)if(function(t,e){let r=0;return t.forEach(function(t){let n=l(t.mode,e);r+=n+t.getBitsLength()}),r}(t,n)<=e.getCapacity(n,r,s.MIXED))return n}(t,o);if(0===t.length)return 1;n=t[0]}else n=t;return function(t,r,n){for(let o=1;o<=40;o++)if(r<=e.getCapacity(o,n,t))return o}(n.mode,n.getLength(),o)},e.getEncodedBits=function(t){if(!a.isValid(t)||t<7)throw Error("Invalid QR Code version");let e=t<<12;for(;n.getBCHDigit(e)-u>=0;)e^=7973<<n.getBCHDigit(e)-u;return t<<12|e}},7899:(t,e,r)=>{let n=r(2726);e.render=function(t,e,r){var o;let i=r,s=e;void 0!==i||e&&e.getContext||(i=e,e=void 0),e||(s=function(){try{return document.createElement("canvas")}catch(t){throw Error("You need to specify a canvas element")}}()),i=n.getOptions(i);let a=n.getImageWidth(t.modules.size,i),u=s.getContext("2d"),l=u.createImageData(a,a);return n.qrToImageData(l.data,t,i),o=s,u.clearRect(0,0,o.width,o.height),o.style||(o.style={}),o.height=a,o.width=a,o.style.height=a+"px",o.style.width=a+"px",u.putImageData(l,0,0),s},e.renderToDataURL=function(t,r,n){let o=n;void 0!==o||r&&r.getContext||(o=r,r=void 0),o||(o={});let i=e.render(t,r,o),s=o.type||"image/png",a=o.rendererOpts||{};return i.toDataURL(s,a.quality)}},6756:(t,e,r)=>{let n=r(2726);function o(t,e){let r=t.a/255,n=e+'="'+t.hex+'"';return r<1?n+" "+e+'-opacity="'+r.toFixed(2).slice(1)+'"':n}function i(t,e,r){let n=t+e;return void 0!==r&&(n+=" "+r),n}e.render=function(t,e,r){let s=n.getOptions(e),a=t.modules.size,u=t.modules.data,l=a+2*s.margin,c=s.color.light.a?"<path "+o(s.color.light,"fill")+' d="M0 0h'+l+"v"+l+'H0z"/>':"",h="<path "+o(s.color.dark,"stroke")+' d="'+function(t,e,r){let n="",o=0,s=!1,a=0;for(let u=0;u<t.length;u++){let l=Math.floor(u%e),c=Math.floor(u/e);l||s||(s=!0),t[u]?(a++,u>0&&l>0&&t[u-1]||(n+=s?i("M",l+r,.5+c+r):i("m",o,0),o=0,s=!1),l+1<e&&t[u+1]||(n+=i("h",a),a=0)):o++}return n}(u,a,s.margin)+'"/>',f='<svg xmlns="http://www.w3.org/2000/svg" '+(s.width?'width="'+s.width+'" height="'+s.width+'" ':"")+('viewBox="0 0 '+l)+" "+l+'" shape-rendering="crispEdges">'+c+h+"</svg>\n";return"function"==typeof r&&r(null,f),f}},2726:(t,e)=>{function r(t){if("number"==typeof t&&(t=t.toString()),"string"!=typeof t)throw Error("Color should be defined as hex string");let e=t.slice().replace("#","").split("");if(e.length<3||5===e.length||e.length>8)throw Error("Invalid hex color: "+t);(3===e.length||4===e.length)&&(e=Array.prototype.concat.apply([],e.map(function(t){return[t,t]}))),6===e.length&&e.push("F","F");let r=parseInt(e.join(""),16);return{r:r>>24&255,g:r>>16&255,b:r>>8&255,a:255&r,hex:"#"+e.slice(0,6).join("")}}e.getOptions=function(t){t||(t={}),t.color||(t.color={});let e=void 0===t.margin||null===t.margin||t.margin<0?4:t.margin,n=t.width&&t.width>=21?t.width:void 0,o=t.scale||4;return{width:n,scale:n?4:o,margin:e,color:{dark:r(t.color.dark||"#000000ff"),light:r(t.color.light||"#ffffffff")},type:t.type,rendererOpts:t.rendererOpts||{}}},e.getScale=function(t,e){return e.width&&e.width>=t+2*e.margin?e.width/(t+2*e.margin):e.scale},e.getImageWidth=function(t,r){let n=e.getScale(t,r);return Math.floor((t+2*r.margin)*n)},e.qrToImageData=function(t,r,n){let o=r.modules.size,i=r.modules.data,s=e.getScale(o,n),a=Math.floor((o+2*n.margin)*s),u=n.margin*s,l=[n.color.light,n.color.dark];for(let e=0;e<a;e++)for(let r=0;r<a;r++){let c=(e*a+r)*4,h=n.color.light;e>=u&&r>=u&&e<a-u&&r<a-u&&(h=l[i[Math.floor((e-u)/s)*o+Math.floor((r-u)/s)]?1:0]),t[c++]=h.r,t[c++]=h.g,t[c++]=h.b,t[c]=h.a}}},9146:(t,e)=>{"use strict";var r;!function(){var n=e||{};void 0!==(r=(function(){return n}).apply(e,[]))&&(t.exports=r),n.default=n;var o="http://www.w3.org/2000/xmlns/",i="http://www.w3.org/2000/svg",s=/url\(["']?(.+?)["']?\)/,a={woff2:"font/woff2",woff:"font/woff",otf:"application/x-font-opentype",ttf:"application/x-font-ttf",eot:"application/vnd.ms-fontobject",sfnt:"application/font-sfnt",svg:"image/svg+xml"},u=function(t){return t instanceof HTMLElement||t instanceof SVGElement},l=function(t){if(!u(t))throw Error("an HTMLElement or SVGElement is required; got "+t)},c=function(t){return new Promise(function(e,r){u(t)?e(t):r(Error("an HTMLElement or SVGElement is required; got "+t))})},h=function(t){var e=Object.keys(a).filter(function(e){return t.indexOf("."+e)>0}).map(function(t){return a[t]});return e?e[0]:(console.error("Unknown font format for "+t+". Fonts may not be working correctly."),"application/octet-stream")},f=function(t){for(var e="",r=new Uint8Array(t),n=0;n<r.byteLength;n++)e+=String.fromCharCode(r[n]);return window.btoa(e)},d=function(t,e,r){var n=t.viewBox&&t.viewBox.baseVal&&t.viewBox.baseVal[r]||null!==e.getAttribute(r)&&!e.getAttribute(r).match(/%$/)&&parseInt(e.getAttribute(r))||t.getBoundingClientRect()[r]||parseInt(e.style[r])||parseInt(window.getComputedStyle(t).getPropertyValue(r));return null==n||isNaN(parseFloat(n))?0:n},g=function(t,e,r,n){if("svg"===t.tagName)return{width:r||d(t,e,"width"),height:n||d(t,e,"height")};if(t.getBBox){var o=t.getBBox(),i=o.x,s=o.y;return{width:i+o.width,height:s+o.height}}},p=function(t){for(var e=window.atob(t.split(",")[1]),r=t.split(",")[0].split(":")[1].split(";")[0],n=new ArrayBuffer(e.length),o=new Uint8Array(n),i=0;i<e.length;i++)o[i]=e.charCodeAt(i);return new Blob([n],{type:r})},m=function(t,e){if(e)try{return t.querySelector(e)||t.parentNode&&t.parentNode.querySelector(e)}catch(t){console.warn('Invalid CSS selector "'+e+'"',t)}},v=function(t,e){var r=t.cssText.match(s),n=r&&r[1]||"";if(!(!n||n.match(/^data:/))&&"about:blank"!==n){var o=n.startsWith("../")?e+"/../"+n:n.startsWith("./")?e+"/."+n:n;return{text:t.cssText,format:h(o),url:o}}},y={},w=null,E=function(t,e){var r=e||{},n=r.selectorRemap,o=r.modifyStyle,i=r.modifyCss,a=r.fonts,u=r.excludeUnusedCss,l=i||function(t,e){return(n?n(t):t)+"{"+(o?o(e):e)+"}\n"},c=[],h=void 0===a,d=a||[];return(w||(w=Array.from(document.styleSheets).map(function(t){try{return{rules:t.cssRules,href:t.href}}catch(e){return console.warn("Stylesheet could not be loaded: "+t.href,e),{}}}))).forEach(function(e){var r=e.rules,n=e.href;r&&Array.from(r).forEach(function(e){if(void 0!==e.style){if(m(t,e.selectorText))c.push(l(e.selectorText,e.style.cssText));else if(h&&e.cssText.match(/^@font-face/)){var r=v(e,n);r&&d.push(r)}else u||c.push(e.cssText)}})}),Promise.all(d.map(function(t){return new Promise(function(e,r){if(y[t.url])return e(y[t.url]);var n=new XMLHttpRequest;n.addEventListener("load",function(){var r=f(n.response),o=t.text.replace(s,'url("data:'+t.format+";base64,"+r+'")')+"\n";y[t.url]=o,e(o)}),n.addEventListener("error",function(r){console.warn("Failed to load font from: "+t.url,r),y[t.url]=null,e(null)}),n.addEventListener("abort",function(r){console.warn("Aborted loading font from: "+t.url,r),e(null)}),n.open("GET",t.url),n.responseType="arraybuffer",n.send()})})).then(function(t){return t.filter(function(t){return t}).join("")}).then(function(t){return c.join("\n")+t})},S=function(){if(!navigator.msSaveOrOpenBlob&&!("download"in document.createElement("a")))return{popup:window.open()}};n.prepareSvg=function(t,e,r){l(t);var n=e||{},s=n.left,a=void 0===s?0:s,u=n.top,c=void 0===u?0:u,h=n.width,f=n.height,d=n.scale,p=void 0===d?1:d,m=n.responsive,v=void 0!==m&&m,y=n.excludeCss,w=void 0!==y&&y;return Promise.all(Array.from(t.querySelectorAll("image")).map(function(t){var e,r=t.getAttributeNS("http://www.w3.org/1999/xlink","href")||t.getAttribute("href");return r?((e=r)&&0===e.lastIndexOf("http",0)&&-1===e.lastIndexOf(window.location.host)&&(r+=(-1===r.indexOf("?")?"?":"&")+"t="+new Date().valueOf()),new Promise(function(e,n){var o=document.createElement("canvas"),i=new Image;i.crossOrigin="anonymous",i.src=r,i.onerror=function(){return n(Error("Could not load "+r))},i.onload=function(){o.width=i.width,o.height=i.height,o.getContext("2d").drawImage(i,0,0),t.setAttributeNS("http://www.w3.org/1999/xlink","href",o.toDataURL("image/png")),e(!0)}})):Promise.resolve(null)})).then(function(){var n=t.cloneNode(!0);n.style.backgroundColor=(e||{}).backgroundColor||t.style.backgroundColor;var s=g(t,n,h,f),u=s.width,l=s.height;if("svg"!==t.tagName){if(t.getBBox){null!=n.getAttribute("transform")&&n.setAttribute("transform",n.getAttribute("transform").replace(/translate\(.*?\)/,""));var d=document.createElementNS("http://www.w3.org/2000/svg","svg");d.appendChild(n),n=d}else{console.error("Attempted to render non-SVG element",t);return}}if(n.setAttribute("version","1.1"),n.setAttribute("viewBox",[a,c,u,l].join(" ")),n.getAttribute("xmlns")||n.setAttributeNS(o,"xmlns",i),n.getAttribute("xmlns:xlink")||n.setAttributeNS(o,"xmlns:xlink","http://www.w3.org/1999/xlink"),v?(n.removeAttribute("width"),n.removeAttribute("height"),n.setAttribute("preserveAspectRatio","xMinYMin meet")):(n.setAttribute("width",u*p),n.setAttribute("height",l*p)),Array.from(n.querySelectorAll("foreignObject > *")).forEach(function(t){t.setAttributeNS(o,"xmlns","svg"===t.tagName?i:"http://www.w3.org/1999/xhtml")}),!w)return E(t,e).then(function(t){var e=document.createElement("style");e.setAttribute("type","text/css"),e.innerHTML="<![CDATA[\n"+t+"\n]]>";var o=document.createElement("defs");o.appendChild(e),n.insertBefore(o,n.firstChild);var i=document.createElement("div");i.appendChild(n);var s=i.innerHTML.replace(/NS\d+:href/gi,'xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href');if("function"!=typeof r)return{src:s,width:u,height:l};r(s,u,l)});var m=document.createElement("div");m.appendChild(n);var y=m.innerHTML;if("function"!=typeof r)return{src:y,width:u,height:l};r(y,u,l)})},n.svgAsDataUri=function(t,e,r){return l(t),n.prepareSvg(t,e).then(function(t){var e=t.src,n=t.width,o=t.height,i="data:image/svg+xml;base64,"+window.btoa(decodeURIComponent(encodeURIComponent('<?xml version="1.0" standalone="no"?><!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" [<!ENTITY nbsp "&#160;">]>'+e).replace(/%([0-9A-F]{2})/g,function(t,e){var r=String.fromCharCode("0x"+e);return"%"===r?"%25":r})));return"function"==typeof r&&r(i,n,o),i})},n.svgAsPngUri=function(t,e,r){l(t);var o=e||{},i=o.encoderType,s=void 0===i?"image/png":i,a=o.encoderOptions,u=void 0===a?.8:a,c=o.canvg,h=function(t){var e=t.src,n=t.width,o=t.height,i=document.createElement("canvas"),a=i.getContext("2d"),l=window.devicePixelRatio||1;i.width=n*l,i.height=o*l,i.style.width=i.width+"px",i.style.height=i.height+"px",a.setTransform(l,0,0,l,0,0),c?c(i,e):a.drawImage(e,0,0);var h=void 0;try{h=i.toDataURL(s,u)}catch(t){if("undefined"!=typeof SecurityError&&t instanceof SecurityError||"SecurityError"===t.name){console.error("Rendered SVG images cannot be downloaded in this browser.");return}throw t}return"function"==typeof r&&r(h,i.width,i.height),Promise.resolve(h)};return c?n.prepareSvg(t,e).then(h):n.svgAsDataUri(t,e).then(function(t){return new Promise(function(e,r){var n=new Image;n.onload=function(){return e(h({src:n,width:n.width,height:n.height}))},n.onerror=function(){r("There was an error loading the data URI as an image on the following SVG\n"+window.atob(t.slice(26))+"Open the following link to see browser's diagnosis\n"+t)},n.src=t})})},n.download=function(t,e,r){if(navigator.msSaveOrOpenBlob)navigator.msSaveOrOpenBlob(p(e),t);else{var n=document.createElement("a");if("download"in n){n.download=t,n.style.display="none",document.body.appendChild(n);try{var o=p(e),i=URL.createObjectURL(o);n.href=i,n.onclick=function(){return requestAnimationFrame(function(){return URL.revokeObjectURL(i)})}}catch(t){console.error(t),console.warn("Error while getting object URL. Falling back to string URL."),n.href=e}n.click(),document.body.removeChild(n)}else r&&r.popup&&(r.popup.document.title=t,r.popup.location.replace(e))}},n.saveSvg=function(t,e,r){var o=S();return c(t).then(function(t){return n.svgAsDataUri(t,r||{})}).then(function(t){return n.download(e,t,o)})},n.saveSvgAsPng=function(t,e,r){var o=S();return c(t).then(function(t){return n.svgAsPngUri(t,r||{})}).then(function(t){return n.download(e,t,o)})}}()}}]);