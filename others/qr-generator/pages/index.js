import { useState } from 'react';
import QrCodeGenerator from '../components/QrCodeGenerator';

export default function Home() {
  const [mode, setMode] = useState(null);

  return (
    <div className="container">
      {!mode && (
        <>
          <h1>O que você deseja fazer?</h1>
          <button onClick={() => setMode('url')}>
            Gerar QR Code de URL
          </button>
          <button onClick={() => setMode('pix')}>
            Gerar QR Code Pix
          </button>
        </>
      )}

      {/* Passa o mode para o QrCodeGenerator */}
      {mode && (
        <QrCodeGenerator mode={mode} onBack={() => setMode(null)} />
      )}
    </div>
  );
}