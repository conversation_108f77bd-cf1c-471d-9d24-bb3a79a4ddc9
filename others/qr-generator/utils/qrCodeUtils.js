import QRCode from 'qrcode';
import { gerarPayloadPix } from './pixUtils';

export async function generateQrCodeData(mode, formData, qrOptions) {
  let qrDataURL;

  let qrCodeOptions = {
    errorCorrectionLevel: 'H',
    width: 256,
    color: {
      dark: qrOptions.qrColor,
      light: qrOptions.qrBgColor,
    },
    type: 'image/png',
    margin: 4,
  };

  if (mode === 'pix') {
    const payload = gerarPayloadPix(
      formData.chave,
      formData.pixKeyType,
      formData.nome,
      formData.cidade
    );
    qrDataURL = await QRCode.toDataURL(payload, qrCodeOptions);
  } else if (mode === 'url') {
    qrDataURL = await QRCode.toDataURL(formData.url, qrCodeOptions);
  }

  return qrDataURL;
}