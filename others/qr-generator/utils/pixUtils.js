export function calcCRC(payload) {
  const polynomial = 0x1021;
  let crc = 0xFFFF;
  for (let i = 0; i < payload.length; i++) {
    crc ^= payload.charCodeAt(i) << 8;
    for (let j = 0; j < 8; j++) {
      if (crc & 0x8000) {
        crc = ((crc << 1) ^ polynomial) & 0xFFFF;
      } else {
        crc = (crc << 1) & 0xFFFF;
      }
    }
  }
  return crc.toString(16).toUpperCase().padStart(4, '0');
}

export function gerarPayloadPix(chave, tipoChave, nome_merchant, cidade_merchant) {
  const payload_format_indicator = '000201';
  const point_of_initiation_method = '010212';
  const gui = 'br.gov.bcb.pix';
  const gui_field = `00${gui.length.toString().padStart(2, '0')}${gui}`;

  let chave_field = '';
  if (tipoChave === 'email') {
    chave_field = `01${chave.length.toString().padStart(2, '0')}${chave}`;
  } else if (tipoChave === 'cpfCnpj') {
    chave_field = `01${chave.length.toString().padStart(2, '0')}${chave}`;
  } else if (tipoChave === 'telefone') {
    chave_field = `01${chave.length.toString().padStart(2, '0')}${chave}`;
  } else if (tipoChave === 'chaveAleatoria') {
    chave_field = `25${chave.length.toString().padStart(2, '0')}${chave}`;
  }

  const merchant_account_info = `26${(gui_field + chave_field).length.toString().padStart(2, '0')}${gui_field}${chave_field}`;
  const merchant_category_code = '********';
  const transaction_currency = '5303986';
  const country_code = '5802BR';

  const nome_final = nome_merchant.trim().toUpperCase().slice(0, 25) || 'NOME';
  const cidade_final = cidade_merchant.trim().toUpperCase().slice(0, 15) || 'CIDADE';

  const merchant_name = `59${nome_final.length.toString().padStart(2, '0')}${nome_final}`;
  const merchant_city = `60${cidade_final.length.toString().padStart(2, '0')}${cidade_final}`;
  const additional_data = '***********';

  const payload_sem_crc =
    payload_format_indicator +
    point_of_initiation_method +
    merchant_account_info +
    merchant_category_code +
    transaction_currency +
    country_code +
    merchant_name +
    merchant_city +
    additional_data +
    '6304';

  const crc = calcCRC(payload_sem_crc);
  return payload_sem_crc + crc;
}