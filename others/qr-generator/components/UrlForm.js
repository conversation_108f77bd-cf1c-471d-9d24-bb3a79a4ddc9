const UrlForm = ({ formData, onFormChange }) => {
  return (
    <div className="form-group">
      <label>URL:</label>
      <input
        type="text"
        name="url"
        required
        value={formData.url}
        onChange={(e) => onFormChange('url', e.target.value)}
        style={{ textTransform: 'none' }}
      />
      <style jsx>{`
        .form-group {
          margin-bottom: 15px;
          text-align: left;
        }
        .form-group label {
          display: block;
          margin-bottom: 5px;
          font-size: 14px;
          color: #333;
        }
        input {
          width: 100%;
          padding: 10px;
          margin-bottom: 10px;
          border: 1px solid #ddd;
          border-radius: 8px;
          font-size: 14px;
          box-sizing: border-box;
        }
      `}</style>
    </div>
  );
};

export default UrlForm;