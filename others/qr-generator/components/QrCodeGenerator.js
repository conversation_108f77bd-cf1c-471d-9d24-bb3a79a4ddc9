import { useState, useEffect, useRef } from 'react';
import { generateQrCodeData } from '../utils/qrCodeUtils';
import QrCodeOptions from './QrCodeOptions';
import QrCodeResult from './QrCodeResult';
import UrlForm from './UrlForm';
import PixForm from './PixForm';
import { saveSvgAsPng } from 'save-svg-as-png';
import QRCode from 'qrcode';
import { gerarPayloadPix } from '../utils/pixUtils';

const QrCodeGenerator = ({ mode, onBack }) => {
  const [formData, setFormData] = useState(
    mode === 'pix'
      ? {
          chave: '',
          nome: '',
          cidade: '',
          pixKeyType: 'email',
        }
      : {
          url: '',
        }
  );
  const [qrData, setQrData] = useState(null);
  const [svgString, setSvgString] = useState(null);
  const [loading, setLoading] = useState(false);
  const [qrOptions, setQrOptions] = useState({
    qrColor: '#000000',
    qrBgColor: '#FFFFFF',
  });
  const qrCodeRef = useRef(null);

  const generateQRCodeSVG = async (mode, formData, qrOptions) => {
    if ((mode === 'url' && formData.url) || (mode === 'pix')) {
      try {
        const qrCodeOptions = {
          errorCorrectionLevel: 'H',
          width: 256,
          color: {
            dark: qrOptions.qrColor,
            light: qrOptions.qrBgColor,
          },
          margin: 4,
        };

        let svgData;
        if (mode === 'url') {
          svgData = await QRCode.toString(formData.url, qrCodeOptions);
        } else if (mode === 'pix') {
          const payload = gerarPayloadPix(
            formData.chave,
            formData.pixKeyType,
            formData.nome,
            formData.cidade
          );
          svgData = await QRCode.toString(payload, qrCodeOptions);
        }

        setSvgString(svgData);
      } catch (error) {
        console.error('Falha ao gerar o QR Code SVG', error);
        alert('Falha ao gerar o QR Code SVG');
      }
    }
  };

  const generateQRCode = async () => {
    setLoading(true);
    try {
      const qrCodeData = await generateQrCodeData(
        mode,
        formData,
        qrOptions
      );
      setQrData(qrCodeData);
      generateQRCodeSVG(mode, formData, qrOptions);
    } catch (error) {
      console.error('Falha ao gerar o QR Code', error);
      alert('Falha ao gerar o QR Code');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {}, [qrOptions]);

  const handleFormChange = (field, value) => {
    setFormData({ ...formData, [field]: value });
  };

  const handleOptionsChange = (field, value) => {
    setQrOptions({ ...qrOptions, [field]: value });
  };

  const handleDownloadSVG = () => {
    if (!svgString) return;
    const blob = new Blob([svgString], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(blob);
    const downloadLink = document.createElement('a');
    downloadLink.href = url;
    downloadLink.download =
      mode === 'pix' ? 'pix_qrcode.svg' : 'url_qrcode.svg';
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);
    URL.revokeObjectURL(url);
  };

  const handleDownloadPNG = () => {
    if (!qrCodeRef.current) return;

    const svgElement = qrCodeRef.current.querySelector('svg');

    if (!svgElement) {
      console.error('Elemento SVG não encontrado.');
      return;
    }

    saveSvgAsPng(svgElement, `${mode}_qrcode.png`, {
      scale: 2,
    });
  };

  return (
    <div>
      <h1>
        {mode === 'pix' ? 'Gerar QR Code Pix' : 'Gerar QR Code de URL'}
      </h1>
      <p>
        {mode === 'pix'
          ? 'Preencha os campos para gerar um QR Code Pix válido.'
          : 'Digite a URL para gerar o QR Code.'}
      </p>

      <QrCodeOptions options={qrOptions} onOptionsChange={handleOptionsChange} />

      {mode === 'pix' && (
        <PixForm formData={formData} onFormChange={handleFormChange} />
      )}
      {mode === 'url' && (
        <UrlForm formData={formData} onFormChange={handleFormChange} />
      )}

      <button onClick={generateQRCode} disabled={loading}>
        {loading ? 'Gerando...' : 'Gerar QR Code'}
      </button>
      <button onClick={onBack} style={{ marginTop: '10px' }}>
        Voltar
      </button>

      {qrData && <QrCodeResult qrCodeRef={qrCodeRef} svgString={svgString} />}

      {qrData && svgString && (
        <div style={{ marginTop: '20px', textAlign: 'center' }}>
          <button onClick={handleDownloadSVG} style={{ marginRight: '10px' }}>
            Baixar como SVG
          </button>
          <button onClick={handleDownloadPNG}>Baixar como PNG</button>
        </div>
      )}
    </div>
  );
};

export default QrCodeGenerator;