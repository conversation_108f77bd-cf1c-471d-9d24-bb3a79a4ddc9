import React, { useState } from 'react';

const PixForm = ({ formData, onFormChange }) => {
  const [pixKeyType, setPixKeyType] = useState('email'); // Estado para o tipo de chave

  const handleKeyTypeChange = (event) => {
    setPixKeyType(event.target.value);
    onFormChange('chave', ''); // Limpa o campo chave ao mudar o tipo
  };

  const handleInputChange = (event) => {
    onFormChange(event.target.name, event.target.value);
  };

  const validatePixKey = () => {
    if (pixKeyType === 'cpfCnpj') {
      const value = formData.chave.replace(/\D/g, '');
      if (value.length === 11) {
        // Valida CPF
        // (Implemente a validação de CPF aqui)
        return true;
      } else if (value.length === 14) {
        // Valida CNPJ
        // (Implemente a validação de CNPJ aqui)
        return true;
      }
    } else if (pixKeyType === 'email') {
      // Valida e-mail
      return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.chave);
    } else if (pixKeyType === 'telefone') {
      // Valida telefone (formato internacional)
      return /^\+\d{2}\d{2}\d{8,9}$/.test(formData.chave);
    } else if (pixKeyType === 'chaveAleatoria') {
      // Valida chave aleatória (formato UUID)
      return /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
        formData.chave
      );
    }
    return false;
  };

  return (
    <>
      <div className="form-group">
        <label>Tipo de Chave Pix:</label>
        <select value={pixKeyType} onChange={handleKeyTypeChange}>
          <option value="email">E-mail</option>
          <option value="cpfCnpj">CPF/CNPJ</option>
          <option value="telefone">Telefone Celular</option>
          <option value="chaveAleatoria">Chave Aleatória</option>
        </select>
      </div>

      <div className="form-group">
        <label>Chave Pix:</label>
        <input
          type="text"
          name="chave"
          required
          value={formData.chave}
          onChange={handleInputChange}
          style={{ textTransform: pixKeyType === 'chaveAleatoria' ? 'uppercase' : 'lowercase' }}
          placeholder={
            pixKeyType === 'cpfCnpj'
              ? 'CPF/CNPJ'
              : pixKeyType === 'email'
              ? 'E-mail'
              : pixKeyType === 'telefone'
              ? '+55...'
              : 'Chave Aleatória'
          }
        />
      </div>

      <div className="form-group">
        <label>Nome do Recebedor:</label>
        <input
          type="text"
          name="nome"
          value={formData.nome}
          onChange={handleInputChange}
          style={{ textTransform: 'uppercase' }}
        />
      </div>

      <div className="form-group">
        <label>Cidade do Recebedor:</label>
        <input
          type="text"
          name="cidade"
          value={formData.cidade}
          onChange={handleInputChange}
          style={{ textTransform: 'uppercase' }}
        />
      </div>

      <style jsx>{`
        .form-group {
          margin-bottom: 15px;
          text-align: left;
        }
        .form-group label {
          display: block;
          margin-bottom: 5px;
          font-size: 14px;
          color: #333;
        }
        input,
        select {
          width: 100%;
          padding: 10px;
          margin-bottom: 10px;
          border: 1px solid #ddd;
          border-radius: 8px;
          font-size: 14px;
          box-sizing: border-box;
        }
      `}</style>
    </>
  );
};

export default PixForm;