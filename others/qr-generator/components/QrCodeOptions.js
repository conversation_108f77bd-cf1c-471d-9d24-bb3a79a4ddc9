const QrCodeOptions = ({ options, onOptionsChange }) => {
  return (
    <div>
      {/* Opções de Cor */}
      <div className="color-options">
        <div className="color-picker">
          <label>Cor do QR Code:</label>
          <input
            type="color"
            value={options.qrColor}
            onChange={(e) => onOptionsChange('qrColor', e.target.value)}
          />
        </div>
        <div className="color-picker">
          <label>Cor de Fundo:</label>
          <input
            type="color"
            value={options.qrBgColor}
            onChange={(e) => onOptionsChange('qrBgColor', e.target.value)}
          />
        </div>
      </div>
    </div>
  );
};

export default QrCodeOptions;